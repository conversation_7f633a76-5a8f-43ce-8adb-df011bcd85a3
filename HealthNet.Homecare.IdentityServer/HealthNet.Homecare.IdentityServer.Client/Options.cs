using Microsoft.Extensions.Options;

namespace HealthNet.Homecare.IdentityServer.Client;

public class AppOptions(
    IOptions<IdentityServerOptions> IdentityServerOptions
)
{
    public IdentityServerOptions IdentityServer { get; init; } = IdentityServerOptions.Value;
};

public class IdentityServerOptions
{
    public string ClientId { get; init; } = default!;
    public string ClientSecret { get; init; } = default!;
    public string ClientScope { get; init; } = default!;
    public string ClientRedirectUri { get; init; } = default!;
    public string ClientPostLogoutRedirectUri { get; init; } = default!;

    public const string SectionName = "IdentityServer";
};
