import * as React from 'react';
import {
    Html,
    Head,
    Body,
    Preview,
    Section,
    Img,
    Text,
    Container,
    Hr,
    Link as EmailLink,
} from '@react-email/components';

interface MyTemplateProps {
    isPreview?: boolean;
}

export function MyTemplate({ isPreview = false }: MyTemplateProps) {
    const logoUrl = isPreview ? "/images/logo.svg" : "{LOGO_URL}";
    const illustrationUrl = isPreview ? "/images/email-illustration.png" : "{ILLUSTRATION_URL}";
    const codeDisplay = isPreview ? "123456" : "{CODE}";

    return (
        <Html lang="en">
            <Head>
                <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&display=swap" rel="stylesheet" />
                <style>
                    {`
                        @font-face {
                            font-family: 'Nunito';
                            font-style: normal;
                            font-weight: 400;
                            src: url('https://fonts.gstatic.com/s/nunito/v25/XRXV3I6Li01BKofIO-aBXso.woff2') format('woff2');
                        }

                        body, table, td {
                            font-family: 'Nunito', Arial, sans-serif;
                        }
                        `}
                </style>
            </Head>
            <Body>
                <Preview>Use this code to verify your Log in to the HealthNet Clinician Gateway</Preview>
                <Section style={{ background: '#fff', padding: '40px 0', fontFamily: "'Nunito', Arial, sans-serif" }}>
                    <Img src={logoUrl} width="245" alt="HealthNet Homecare" style={{ margin: '0 auto 16px' }} />
                    <Container style={{ maxWidth: 520, background: '#fff', borderRadius: 16, margin: '0 auto', padding: '45px 0' }}>
                        <Section style={{ textAlign: 'center', padding: '32px 24px 24px 24px', background: '#E0E9F3', borderRadius: 20 }}>
                            <Img src={illustrationUrl} width="220" alt="Security Illustration" style={{ margin: '45px auto 16px' }} />
                            <Text style={{ fontSize: 16, color: '#003F6B', margin: '16px 0 0 0', textAlign: 'center' }}>
                                Use this code to verify your Log in to
                            </Text>
                            <Text style={{ fontSize: 16, color: '#003F6B', margin: '0 0 8px 0', textAlign: 'center' }}>
                                HealthNet Clinician Gateway
                            </Text>
                            <Text style={{ fontSize: 40, fontWeight: 700, letterSpacing: 8, color: '#003F6B', lineHeight: '69px' }}>{codeDisplay}</Text>
                        </Section>
                    </Container>
                </Section>
                <Section style={{ background: '#fff', padding: '0 0 40px 0', marginTop: '250px' }}>
                    <Container style={{ maxWidth: 520, background: '#fff', borderRadius: 16, margin: '0 auto' }}>
                        <Section style={{ textAlign: 'center', padding: '32px 24px 24px 24px' }}>
                            <Hr style={{ margin: '0 0 24px 0', borderColor: '#E6E6E6' }} />
                            <div style={{ margin: '0 auto 24px', backgroundColor: '#11BBC7', height: '4px', width: '35px' }} />
                            <Text style={{ fontSize: 16, color: '#003F6B', fontWeight: '700', lineHeight: '27px' }}>
                                Kind Regards,<br />The HealthNet Team
                            </Text>
                            <Img src={logoUrl} width="245" alt="HealthNet Homecare" style={{ margin: '0 auto' }} />
                            <Text style={{ fontSize: 12, color: '#555555', margin: '70px 0 0 0' }}>
                                HealthNet Homecare's pharmacies are registered with and inspected by the General Pharmaceutical Council;<br />
                                Registration number 1113506 (Featherstone) and 9011236 (Swadlincote)
                            </Text>
                            <Text style={{ fontSize: 12, fontWeight: '700', color: '#555555', margin: '8px 0 0 0' }}>
                                Email: <EMAIL><br />
                                Tel: 08000 833 060<br />
                            </Text>
                            <Text style={{ fontSize: 12, color: '#555555', margin: '8px 0 0 0' }}>
                                HealthNet Homecare (UK) Limited<br />
                                Unit 1 & 2 Alfred Eley Close<br />
                                Swadlincote, England<br />
                                DE11 0WU
                            </Text>
                            <Text style={{ fontSize: 12, color: '#0368C8', margin: '16px 0 0 0' }}>
                                <EmailLink style={{ textDecoration: 'underline' }} href="https://portal.healthnethomecare.co.uk">Visit the Patient Portal</EmailLink>
                                <span style={{ display: 'inline-block', margin: '0 8px', backgroundColor: '#555555', height: '12px', width: '1px', verticalAlign: 'middle' }} />
                                <EmailLink style={{ textDecoration: 'underline' }} href="https://www.healthnethomecare.co.uk/privacy-policy/">Privacy Policy</EmailLink>
                            </Text>
                        </Section>
                    </Container>
                </Section>
            </Body>
        </Html>
    );
}

export default MyTemplate;