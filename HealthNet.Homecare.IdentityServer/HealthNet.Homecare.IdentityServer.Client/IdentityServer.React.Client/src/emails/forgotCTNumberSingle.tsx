import * as React from 'react';
import {
    Html,
    Head,
    Body,
    Preview,
    Section,
    Img,
    Text,
    Container,
    Hr,
    Link as EmailLink,
    Button,
} from '@react-email/components';

interface MyTemplateProps {
    isPreview?: boolean;
}

export function MyTemplate({ isPreview = false }: MyTemplateProps) {
    const logoUrl = isPreview ? "/images/logo.svg" : "{LOGO_URL}";
    const illustrationUrl = isPreview ? "/images/email-illustration.png" : "{ILLUSTRATION_URL}";
    const name = isPreview ? "John Doe" : "{NAME}";
    const ctNumber = isPreview ? "CT278632" : "{CT_NUMBER}";
    const goToLoginLink = isPreview ? "https://www.healthnethomecare.co.uk" : "{LOGIN_URL}";

    return (
        <Html lang="en">
            <Head>
                <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&display=swap" rel="stylesheet" />
                <style>
                    {`
                        @font-face {
                            font-family: 'Nunito';
                            font-style: normal;
                            font-weight: 400;
                            src: url('https://fonts.gstatic.com/s/nunito/v25/XRXV3I6Li01BKofIO-aBXso.woff2') format('woff2');
                        }

                        body, table, td {
                            font-family: 'Nunito', Arial, sans-serif;
                        }
                        `}
                </style>
            </Head>
            <Body>
                <Preview>Use this code to verify your Log in to the HealthNet Clinician Gateway</Preview>
                <Section style={{ background: '#fff', fontFamily: "'Nunito', Arial, sans-serif" }}>
                    <Img src={logoUrl} width="245" alt="HealthNet Homecare" style={{ margin: '0 auto 16px' }} />
                    <Container style={{ maxWidth: 700, background: '#fff', borderRadius: 16, margin: '0 auto'}}>
                        <Section style={{ textAlign: 'center', padding: '32px 24px 24px 24px', background: '#E0E9F3', borderRadius: 20 }}>
                            <Img src={illustrationUrl} width="220" alt="Security Illustration" style={{ margin: '45px auto 16px' }} />
                            <Text style={{ fontSize: 16, color: '#003F6B', marginTop: '32px', textAlign: 'center' }}>
                                Hello {name},
                            </Text>
                            <Text style={{ fontSize: 22, fontWeight: 700, color: '#003F6B', margin: '54px 0' }}>
                                You have requested a reminder of your CT Number/Username
                            </Text>
                        </Section>
                    </Container>
                </Section>
                <Section style={{ background: '#fff', fontFamily: "'Nunito', Arial, sans-serif" }}>
                    <Container style={{ maxWidth: 700, background: '#fff', borderRadius: 16, margin: '0 auto' }}>
                        <Section style={{ textAlign: 'center' }}>
                            <Text style={{ fontSize: 22, fontWeight: 400, color: '#003F6B', marginTop: '36px' }}>
                                Your CT Number/Username is below:
                            </Text>
                            <Text style={{ fontSize: 22, color: '#003F6B', textAlign: 'center', fontWeight: 700 }}>
                                {ctNumber}
                            </Text>
                            <Button href={goToLoginLink} style={{ backgroundColor: '#003F6B', color: '#fff', borderRadius: 2, padding: '15px 35px', fontSize: 16, fontWeight: 700, margin: '16px 0' }}>Go to Log in</Button>
                        </Section>
                    </Container>
                </Section>
                <Section style={{ background: '#fff' }}>
                    <Container style={{ maxWidth: 700, background: '#fff', borderRadius: 16, margin: '0 auto' }}>
                        <Section style={{ textAlign: 'center' }}>
                            <Hr style={{ margin: '0 0 24px 0', borderColor: '#E6E6E6' }} />
                            <div style={{ margin: '0 auto 24px', backgroundColor: '#11BBC7', height: '4px', width: '35px' }} />
                            <Text style={{ fontSize: 16, color: '#003F6B', fontWeight: '700', lineHeight: '27px' }}>
                                Kind Regards,<br />The HealthNet Team
                            </Text>
                            <Img src={logoUrl} width="245" alt="HealthNet Homecare" style={{ margin: '0 auto' }} />
                            <Text style={{ fontSize: 12, color: '#555555', margin: '70px 0 0 0' }}>
                                HealthNet Homecare's pharmacies are registered with and inspected by the General Pharmaceutical Council;<br />
                                Registration number 1113506 (Featherstone) and 9011236 (Swadlincote)
                            </Text>
                            <Text style={{ fontSize: 12, fontWeight: '700', color: '#555555', margin: '8px 0 0 0' }}>
                                Email: <EMAIL><br />
                                Tel: 08000 833 060<br />
                            </Text>
                            <Text style={{ fontSize: 12, color: '#555555', margin: '8px 0 0 0' }}>
                                HealthNet Homecare (UK) Limited<br />
                                Unit 1 & 2 Alfred Eley Close<br />
                                Swadlincote, England<br />
                                DE11 0WU
                            </Text>
                            <Text style={{ fontSize: 12, color: '#0368C8', margin: '16px 0 0 0' }}>
                                <EmailLink style={{ textDecoration: 'underline' }} href="https://portal.healthnethomecare.co.uk">Visit the Patient Portal</EmailLink>
                                <span style={{ display: 'inline-block', margin: '0 8px', backgroundColor: '#555555', height: '12px', width: '1px', verticalAlign: 'middle' }} />
                                <EmailLink style={{ textDecoration: 'underline' }} href="https://www.healthnethomecare.co.uk/privacy-policy/">Privacy Policy</EmailLink>
                            </Text>
                        </Section>
                    </Container>
                </Section>
            </Body>
        </Html>
    );
}

export default MyTemplate;