"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { UserManager, WebStorageStateStore } from "oidc-client-ts";
import oidcConfig from "../auth/oidc-config";

export default function SignoutCallbackPage() {
  const router = useRouter();

  useEffect(() => {
    if (typeof window === "undefined") return;

    const userManager = new UserManager(oidcConfig);

    userManager
      .signoutRedirectCallback()
      .then(() => {
        console.log("Logout callback complete");
        userManager.removeUser();         // Clear user data
        userManager.clearStaleState();    // Clean OIDC storage
        router.push("/");                // Redirect to main page
      })
      .catch((err) => {
        console.error("Logout callback failed:", err);
        router.push("/");
      });
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <p>Completing logout...</p>
    </div>
  );
}
