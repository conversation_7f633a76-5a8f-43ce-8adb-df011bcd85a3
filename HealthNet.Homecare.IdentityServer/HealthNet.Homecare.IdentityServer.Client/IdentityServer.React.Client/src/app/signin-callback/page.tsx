"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { UserManager, WebStorageStateStore } from "oidc-client-ts";
import oidcConfig from "../auth/oidc-config";

export default function Callback() {
  const router = useRouter();

  useEffect(() => {
    if (typeof window === "undefined") return;

    const userManager = new UserManager(oidcConfig);

    userManager
      .signinRedirectCallback()
      .then((user) => {
        // Successfully signed in, redirect to home page
        console.log("User", user);
        router.push("/");
      })
      .catch((error) => {
        // Handle error appropriately
        console.error("Error during signin redirect callback:", error);
        router.push("/");
      });
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-gray-700">
          Processing login...
        </h2>
        <p className="text-gray-500 mt-2">
          Please wait while we complete your authentication
        </p>
      </div>
    </div>
  );
}
