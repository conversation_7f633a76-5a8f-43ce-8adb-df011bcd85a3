"use client";

import { useState } from "react";
import { useEffect } from "react";
import { UserManager, WebStorageStateStore, User } from "oidc-client-ts";
import oidcConfig from './auth/oidc-config';

export default function Home() {
  const [user, setUser] = useState<User | null>(null);
  const [userManager, setUserManager] = useState<UserManager | null>(null);

  useEffect(() => {
    if (typeof window === "undefined") return;

    const manager = new UserManager(oidcConfig);

        manager.getUser()
          .then(user => {
            if (user && !user.expired) {
              setUser(user);
            } else {
              setUser(null);
           }
        })
        .catch(err => {
          console.error("Failed to load user", err);
          setUser(null);
        });

      setUserManager(manager);
  }, []);

  const login = () => {
    userManager?.signinRedirect();
  };

  const logout = () => {
    userManager?.signoutRedirect();
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md text-center">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">
            Welcome to HealthNet
          </h2>
          <p className="text-gray-600 mb-6">
            Please sign in to access your account
          </p>
          <button
            onClick={login}
            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded-md transition-colors duration-200"
          >
            Sign In
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-8">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-semibold text-gray-800">
              Welcome, {user.profile.name}
            </h2>
            <button
              onClick={logout}
              className="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200"
            >
              Sign Out
            </button>
          </div>

          {/* add a table of the user's claims */}
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Claim
                </th>
                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Value
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {Object.entries(user.profile).map(([key, value]) => (
                <tr key={key}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {key}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {`${value}`}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          <div className="bg-gray-50 rounded-md p-4">
            <h3 className="text-sm font-medium text-gray-500 mb-2">
              Access Token
            </h3>
            <p className="text-sm text-gray-600 break-all font-mono">
              {user.access_token}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
