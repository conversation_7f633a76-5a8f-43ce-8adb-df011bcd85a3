'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { pretty, render } from '@react-email/render';

export default function EmailTemplatePage({ params }: { params: { id: string } }) {
    const [Component, setComponent] = useState<any>(null);
    const [html, setHtml] = useState<string>('');
    const [isLoading, setIsLoading] = useState(true);
    const [activeTab, setActiveTab] = useState<'preview' | 'html'>('preview');

    useEffect(() => {
        const loadTemplate = async () => {
            try {
                setIsLoading(true);
                const template = await import(`../../../emails/${params.id}`);
                const TemplateComponent = template.default;
                setComponent(() => TemplateComponent);

                if (TemplateComponent) {
                    const rawHtml = await render(
                        <TemplateComponent
                            code="123456"
                            name="<PERSON>"
                            expiresIn="5 minutes"
                        />);

                    const formattedHtml = await pretty(rawHtml);
                    setHtml(formattedHtml);
                }
            } catch (error) {
                console.error('Failed to load template:', error);
            } finally {
                setIsLoading(false);
            }
        };

        loadTemplate();
    }, [params?.id]);

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="mb-6">
                <Link href="/emails" className="text-blue-500 hover:text-blue-700">
                    ← Back to Email Templates
                </Link>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-2xl font-bold mb-4">Email Template Preview</h2>

                <div className="border-b mb-4">
                    <nav className="flex space-x-4">
                        <button
                            onClick={() => setActiveTab('preview')}
                            className={`py-2 px-4 ${activeTab === 'preview' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500'}`}
                        >
                            Preview
                        </button>
                        <button
                            onClick={() => setActiveTab('html')}
                            className={`py-2 px-4 ${activeTab === 'html' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500'}`}
                        >
                            HTML
                        </button>
                    </nav>
                </div>

                {isLoading ? (
                    <div className="text-center py-4">Loading template...</div>
                ) : activeTab === 'preview' ? (
                    Component ? (
                        <div className="border rounded-lg p-4">
                            <Component isPreview />
                        </div>
                    ) : (
                        <div className="text-center py-4 text-red-500">Failed to load template</div>
                    )
                ) : (
                    <div className="border rounded-lg p-4">
                        <textarea
                            readOnly
                            defaultValue={html}
                            className="w-full h-[600px] font-mono text-sm p-4 bg-gray-50 border rounded"
                            onClick={(e) => e.currentTarget.select()}
                        />
                    </div>
                )}
            </div>
        </div>
    );
} 