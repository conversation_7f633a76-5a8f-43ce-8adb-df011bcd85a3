'use client';

import Link from 'next/link';

export default function EmailTemplatesPage() {
    return (
        <div className="container mx-auto px-4 py-8">
            <h1 className="text-2xl font-bold mb-6">Email Templates</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Link href="/emails/2fe" className="block">
                    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                        <h2 className="text-xl font-semibold mb-2">Two Factor Email</h2>
                        <p className="text-gray-600">Template for two-factor authentication emails</p>
                    </div>
                </Link>
                <Link href="/emails/forgotPassword" className="block">
                    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                        <h2 className="text-xl font-semibold mb-2">Forgot Password</h2>
                        <p className="text-gray-600">Template for forgot password</p>
                    </div>
                </Link>
                <Link href="/emails/forgotCTNumberSingle" className="block">
                    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                        <h2 className="text-xl font-semibold mb-2">Forgot CT Number Single</h2>
                        <p className="text-gray-600">Template for forgot CT number</p>
                    </div>
                </Link>
                <Link href="/emails/forgotCTNumberMultiple" className="block">
                    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                        <h2 className="text-xl font-semibold mb-2">Forgot CT Number Multiple</h2>
                        <p className="text-gray-600">Template for forgot CT number Multiple</p>
                    </div>
                </Link>
                {/* Add more email templates here as needed */}
            </div>
        </div>
    );
} 