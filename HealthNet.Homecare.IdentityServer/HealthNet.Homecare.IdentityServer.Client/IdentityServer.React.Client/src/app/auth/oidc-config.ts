import { WebStorageStateStore } from 'oidc-client-ts';

const baseUrl = window.location.origin;

const oidcConfig = {
  authority: 'https://localhost:5001',
  client_id: 'HealthNet.Homecare.LocalReactClient',
  redirect_uri: `${baseUrl}/signin-callback`,
  post_logout_redirect_uri: `${baseUrl}/signout-callback`,
  response_type: 'code',
  scope: 'openid profile udm-api',
  userStore: new WebStorageStateStore({ store: window.sessionStorage }),
};

export default oidcConfig;