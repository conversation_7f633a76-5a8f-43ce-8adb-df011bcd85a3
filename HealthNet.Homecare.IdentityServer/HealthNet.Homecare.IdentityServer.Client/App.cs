using Duende.IdentityModel.Client;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace HealthNet.Homecare.IdentityServer.Client;

public class App(AppOptions options)
{

    public async Task Run()
    {
        while (true)
        {
            Console.Clear();
            Console.WriteLine("Can start testing authorization?");
            Console.WriteLine("Press y to start.");
            Console.WriteLine("Enter q to exit.");
            var key = Console.ReadKey(intercept: true);

            if (key.Key == ConsoleKey.Q)
            {
                Console.WriteLine("Exiting...");
                break;
            }

            if (key.Key == ConsoleKey.Y)
            {
                Console.WriteLine("Starting authorization...");
                await RunApp();
                break;
            }
        }
    }

    private async Task RunApp()
    {
        var clientId = options.IdentityServer.ClientId;
        var clientSecret = options.IdentityServer.ClientSecret;
        var clientScope = options.IdentityServer.ClientScope;

        var accessToken = await GetAccessToken(
            clientId: clientId,
            clientSecret: clientSecret,
            scope: clientScope
        );

        if (string.IsNullOrEmpty(accessToken))
        {
            Console.WriteLine("Access token is null or empty.");
            return;
        }

        var result = await GetApiResponse("https://localhost:7087/identity", accessToken);

        if (string.IsNullOrEmpty(result))
        {
            Console.WriteLine("API response is null or empty.");
            return;
        }

        Console.WriteLine(result);
        Console.ReadLine();
    }

    private static async Task<string?> GetAccessToken(string clientId, string clientSecret, string scope)
    {
        var client = new HttpClient();
        var discoveries = await client.GetDiscoveryDocumentAsync("https://localhost:5001/");
        if (discoveries.IsError)
        {
            Console.WriteLine(discoveries.Error);
            return null;
        }

        var tokenResponse = await client.RequestClientCredentialsTokenAsync(new ClientCredentialsTokenRequest()
        {
            Address = discoveries.TokenEndpoint,
            ClientId = clientId,
            ClientSecret = clientSecret,
            Scope = scope,
        });

        if (tokenResponse.IsError)
        {
            Console.WriteLine(tokenResponse.Error);
            Console.WriteLine(tokenResponse.ErrorDescription);

            return null;
        }

        return tokenResponse.AccessToken!;
    }

    private static async Task<string?> GetApiResponse(string url, string accessToken)
    {
        var apiClient = new HttpClient();
        apiClient.SetBearerToken(accessToken);

        var response = await apiClient.GetAsync(url);

        if (!response.IsSuccessStatusCode)
        {
            return null;
        }

        var content = await response.Content.ReadAsStringAsync();

        var doc = JsonDocument.Parse(content);
        var result = JsonSerializer.Serialize(doc, new JsonSerializerOptions
        {
            WriteIndented = true,
        });

        return result;
    }
}
