using HealthNet.Homecare.IdentityServer.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

var services = new ServiceCollection();

var config = new ConfigurationBuilder()
    .SetBasePath(AppContext.BaseDirectory)
    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
    .Build();

services.AddSingleton<IConfiguration>(config);
services.Configure<IdentityServerOptions>(config.GetSection(IdentityServerOptions.SectionName));

services.AddSingleton<AppOptions>();
services.AddTransient<App>();


using var serviceProvider = services.BuildServiceProvider();
var app = serviceProvider.GetRequiredService<App>();
await app.Run();
