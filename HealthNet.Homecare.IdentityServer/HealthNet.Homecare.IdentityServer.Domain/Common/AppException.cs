namespace HealthNet.Homecare.IdentityServer.Domain.Common;

[Serializable]
public class GenericException : Exception
{
    public GenericException()
    { }

    public GenericException(string message)
        : base(message)
    { }

    public GenericException(string message, Exception innerException)
        : base(message, innerException)
    { }
}

[Serializable]
public class AppException : GenericException
{
    public AppException(string message, Exception innerException = null)
        : base(message, innerException)
    { }
    public AppException(string message, string reason, Exception innerException = null)
        : base(message, innerException)
    {
        Reason = reason;
    }
    public string Reason { get; set; }
}