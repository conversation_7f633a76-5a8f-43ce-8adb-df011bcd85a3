namespace HealthNet.Homecare.IdentityServer.Domain.Common;

public static class AppConstants
{
	public const string Separator = ";";
	public static class Regex
	{
		public const string MaskEmail = @"(?<=[\w]{1})[\w-\._\+%\\]*(?=[\w]{1}@)|(?<=@[\w]{1})[\w-_\+%]*(?=[\w]{1}.)";
	}

	public static class ClaimType
	{
		public const string SessionId = "session";
		public const string Email = "email";
		public const string IsKeepMeLogin = "IsKeepMeLogin";
		public const string IsAdherePredictAdmin = "IsAdherePredictAdmin";
		public const string CompanyNumbers = "CompanyNumbers";
	}
}

public static class EmailTemplates
{
	public const string TwoFactorEmailTemplateName = "TwoFactorEmailTemplate";
}

public static class PatientStatus
{
	public const string Active = "Active";
	public const string Finished = "Finished";
}

public static class UserRole
{
	public const string Patient = "PATIENT";
	public const string TrustPhar = "TRUST PHAR";
}

public static class PortalOptIn
{
	public const string No = "No";
	public const string Yes = "Yes";
	public const string Unknown = "Unknown";
}

public static class ActValidation
{
	public const string Validated = "Validated";
}

public static class AppError
{
	public static class Message
	{
		public const string EmailIsNotExisted = "Your email address does not exist. Please try another email or contact Customer Care on ***********.";
		public const string InvalidAccount = "The account is invalid";
		public const string InactivePatientAccount = "The patient account is inactive";
		public const string InactiveClinicianAccount = "The clinician account is inactive";
		public const string ExistingAccount = "This account already exists.";
		public const string WebServiceDmsTimeOut = "Sorry, your request timed out. Please try again.";
		public const string WebServiceTimeOut = "We couldn't process your request at the present time, please try again in a few minutes.";
		public const string WebServiceIsNotConnected = "We're unable to connect you at this time.\nPlease try again in five minutes. ";
		public const string SqlServerTimeOut = "We couldn't process your request at the present time, please try again in a few minutes.";
		public const string WebServiceFail = "Web service return fail.";
		public const string UpdateDataFail = "Update data fail.";
		public const string EndpointIsEmpty = "Endpoint cannot be empty.";
		public const string LoginFail = "Incorrect CT number or Password entered. Please try again.";
		public const string LoginAttemptFailed = "Incorrect password entered. You have {0} attempt(s) remaining before we suspend your account access for an hour.";
		public const string AccountIsLocked = "Your account has been suspended for an hour";
		public const string ClinicianLoginOnMobile = "This app is only available for patients. Please go to our web portal to log in.";
		public const string InvalidCaptcha = "Invalid Captcha";
		public const string AccountIsUnauthorized = "Account Is Unauthorized";
		public const string DocumentsAreNotAvailable = "The requested documents are not available in the system";
		public const string DataNotExist = "This data does not exist";
		public const string RequiredFiled = "This field is required";
		public const string EmailDataNotExist = "This email data does not exist";
		public const string InvalidKey = "Invalid Key";
		public const string MissingInvoiceFile = "Printing failed. Verify document availability or try again later.";
		public const string GetAddressServiceError = "Address suggestion is not available due to search limitation.";
		public const string RestrictedClinicAccount = "The Account is restricted clinic access.";
		public const string InvalidUserNumberAndEmail = "Invalid CT number or Email address entered.";
		public const string SomethingWentWrong = "Something went wrong. Please try again later";
		public const string EmailNotFound = "Please provide your email address so we can send you the details of your order.";
		public const string NotAllowUpdateDocument = "You aren't allowed update the documen";
		public const string UnavailableCefFile = "The Clinical Evaluation Form is not available yet. Please try again soon";
		public const string OrderNumberInvalidAccount = "Order Number is invalid with user";
		public const string FileInvalidAccount = "File is invalid with user";
		public const string RateLimitError = "Too many requests. Please try again later.";
		public const string InvalidInputValue = "Invalid input values";
		public const string EmailOrMobileNoNotFound = "Please provide your mobile number or e-mail address in the \"My account page\" to be able to confirm or edit your orders.";
		public const string AccountIsNotSetup = "Account is not setup";
		public const string NoValidDeliveryDate = "You cannot confirm this order using the portal, please contact customer support on 08000 833 060";
		public const string NoPermissionToViewData = "You are not authorised to view these details.";
		public const string InvalidVerificationCode = "Invalid verification code. Please try again.";
		public const string InvalidRecoveryCode = "Invalid recovery code. Please try again.";
		public const string InvalidCredentials = "Incorrect CT number or Password entered. Please try again.";
		public const string EmailSendFail = "Failed to send email. Please try again later.";
		public const string UnknownAccount = "We don’t recognize the CT number you entered. Try again or click Forgot CT number to retrieve it.";
	}
	public static class Reason
	{
		public const string TimeOut = "TimeOut";
		public const string BadGateway = "BadGateway";
		public const string InvalidAccount = "InvalidAccount";
		public const string ExistingAccount = "AccountIsCreated";
		public const string ValidationError = "FieldNotMatchRequirement";
	}
}