using Microsoft.AspNetCore.Identity;

namespace HealthNet.Homecare.IdentityServer.Domain.Entities;

public class ApplicationUser : IdentityUser
{
	public string? Firstname { get; set; }
	public string? Surname { get; set; }
	public bool IsPatient { get; set; }
	public string? ClinicianType { get; set; }
	public string? PortalOptIn { get; set; }

	public string? Status { get; set; } = null!;

	public string? ResetPasswordRedirectUrl { get; set; }

	public UserSecuritySettings SecuritySettings { get; set; } = default!;
	public virtual ICollection<UserLoginSession> LoginSessions { get; set; } = [];
	public virtual ICollection<UserDevice> Devices { get; set; } = [];
}
