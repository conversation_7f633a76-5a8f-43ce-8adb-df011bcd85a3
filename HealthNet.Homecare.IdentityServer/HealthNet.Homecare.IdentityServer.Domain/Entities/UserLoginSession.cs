namespace HealthNet.Homecare.IdentityServer.Domain.Entities;

public class UserLoginSession
{
	public Guid Id { get; set; }
	public Guid SessionId { get; set; }
	public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

	public bool RememberMe { get; set; }
	public DateTime? LastMfaVerifiedAt { get; set; }
	public DateTime? MfaGraceUntilUtc { get; set; }
	public bool LoginSucceeded { get; set; }

	public string? LastKnownCountry { get; set; }
	public string? LastKnownCity { get; set; }
	public string? LastKnownBrowser { get; set; }

	public string UserId { get; set; } = null!;
	public virtual ApplicationUser User { get; set; } = new();

	public virtual ICollection<UserOneTimePassword> UserOneTimePasswords { get; set; } = [];
}