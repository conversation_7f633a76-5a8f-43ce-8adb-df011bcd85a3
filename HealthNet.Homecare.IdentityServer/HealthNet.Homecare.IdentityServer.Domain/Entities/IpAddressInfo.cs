namespace HealthNet.Homecare.IdentityServer.Domain.Entities;

public class IpAddressInfo
{
	public Guid Id { get; set; } = Guid.NewGuid();

	public string Ip { get; set; } = default!;

	public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
	public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

	public string? Country { get; set; }
	public string? CountryCode { get; set; }
	public string? Region { get; set; }
	public string? RegionName { get; set; }
	public string? City { get; set; }
	public string? Zip { get; set; }
}
