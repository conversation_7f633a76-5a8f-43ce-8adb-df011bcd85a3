using HealthNet.Homecare.IdentityServer.Domain.Enums;

namespace HealthNet.Homecare.IdentityServer.Domain.Entities;

public class UserSecuritySettings
{
	public Guid Id { get; set; } = Guid.NewGuid();
	public string UserId { get; set; } = string.Empty;

	public bool MfaEnabled { get; set; } = false;
	public MfaType MfaType { get; set; } = MfaType.Email;
	public string? MfaRecoveryCode { get; set; }
	public string MfaDestination { get; set; } = string.Empty;

	public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
	public DateTime? UpdatedAt { get; set; }

	public ApplicationUser User { get; set; } = default!;
}
