using System.Security.Cryptography;
using System.Text;

namespace HealthNet.Homecare.IdentityServer.Domain.Helpers;

public static class CommonFunction
{
    public static string CalculateAge(DateTime birthDate)
    {
        var now = DateTime.Today.Date;

        var days = now.Day - birthDate.Day;
        if (days < 0)
        {
            var newNow = now.AddMonths(-1);
            days += (int)(now - newNow).TotalDays;
            now = newNow;
        }

        var months = now.Month - birthDate.Month;
        if (months < 0)
        {
            months += 12;
            now = now.AddYears(-1);
        }

        var years = now.Year - birthDate.Year;
        if (years != 0) return years + " years";
        if (months == 0)
            return days + " days";
        return months + " months";
    }

    public static string FormatStringToOdataDate(string date, string time = "", string type = "", string dateFormat = "dd/mm/yyyy", bool isShortDate = false)
    {
        if (string.IsNullOrEmpty(date))
        {
            return "";
        }

        if (date.Length != 10)
        {
            return "";
        }

        var arrayDate = new string[3];

        var day = date.Substring(dateFormat.IndexOf('d'), 2);
        var month = date.Substring(dateFormat.IndexOf('m'), 2);
        var year = date.Substring(dateFormat.IndexOf('y'), 4);

        arrayDate[0] = year;
        arrayDate[1] = month;
        arrayDate[2] = day;
        var temp = string.Join("-", arrayDate);

        if (isShortDate) return temp;

        temp += "T";
        if (string.IsNullOrEmpty(time))
        {
            if (type.Equals("TO", StringComparison.CurrentCultureIgnoreCase))
            {
                temp += "23:59:59";
            }
            else
            {
                temp += "00:00:00";
            }
        }
        else
        {
            if (type.Equals("TO", StringComparison.CurrentCultureIgnoreCase))
            {
                temp += time.Trim() + ":59";
            }
            else
            {
                temp += time.Trim() + ":00";
            }
        }
        temp += "z";
        return temp;
    }

    public static string ComputeSha256Hash(string rawData, string key)
    {
        using var sha256Hash = SHA256.Create();
        var bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes($"{rawData}{key}"));

        var builder = new StringBuilder();
        foreach (var t in bytes)
        {
            builder.Append(t.ToString("x2"));
        }
        return builder.ToString();
    }

    public static string EncryptString(string plainText, string key)
    {
        var iv = new byte[16];
        byte[] array;

        using (var aes = Aes.Create())
        {
            aes.Key = Encoding.UTF8.GetBytes(key);
            aes.IV = iv;

            var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);

            using var memoryStream = new MemoryStream();
            using var cryptoStream = new CryptoStream(memoryStream, encrypt, CryptoStreamMode.Write);
            using (var streamWriter = new StreamWriter(cryptoStream))
            {
                streamWriter.Write(plainText);
            }

            array = memoryStream.ToArray();
        }

        return Convert.ToBase64String(array);
    }
    public static string DecryptString(string cipherText, string key)
    {
        var iv = new byte[16];
        var buffer = Convert.FromBase64String(cipherText);

        using var aes = Aes.Create();
        aes.Key = Encoding.UTF8.GetBytes(key);
        aes.IV = iv;
        var decrypt = aes.CreateDecryptor(aes.Key, aes.IV);

        using var memoryStream = new MemoryStream(buffer);
        using var cryptoStream = new CryptoStream(memoryStream, decrypt, CryptoStreamMode.Read);
        using var streamReader = new StreamReader(cryptoStream);
        return streamReader.ReadToEnd();
    }

    public static string GenerateRandomText(bool isAllowedLowerChars = false, bool isAllowNumber = true, bool isAllowedUpperChars = false, int length = 12)
    {
        var stringBuilder = new StringBuilder();
        const string lowerAlphabet = "abcdefghijklmnopqrstuvwxyz";
        const string numeric = "0123456789";
        if (isAllowedLowerChars)
        {
            stringBuilder.Append(lowerAlphabet);
        }
        if (isAllowNumber)
        {
            stringBuilder.Append(numeric);
        }
        if (isAllowedUpperChars)
        {
            stringBuilder.Append(lowerAlphabet.ToUpper());
        }
        return new string(Enumerable.Repeat(stringBuilder.ToString(), length)
                .Select(s => s[RandomNumberGenerator.GetInt32(s.Length)]).ToArray());
    }

    public static string Base64Encode(string plainText)
    {
        var plainTextBytes = Encoding.UTF8.GetBytes(plainText);
        return Convert.ToBase64String(plainTextBytes);
    }
    public static bool BeAValidDate(DateTime date)
    {
        return !date.Equals(default);
    }
}
