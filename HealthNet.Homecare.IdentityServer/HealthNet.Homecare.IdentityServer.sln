
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35919.96
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HealthNet.Homecare.IdentityServer", "HealthNet.Homecare.IdentityServer\HealthNet.Homecare.IdentityServer.csproj", "{9C072518-E306-4F3E-B758-41B8D2DEBD66}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HealthNet.Homecare.IdentityServer.ApiResource", "HealthNet.Homecare.IdentityServer.ApiResource\HealthNet.Homecare.IdentityServer.ApiResource.csproj", "{1B99FA21-4015-4682-DB9C-08F7D8B609D3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HealthNet.Homecare.IdentityServer.Client", "HealthNet.Homecare.IdentityServer.Client\HealthNet.Homecare.IdentityServer.Client.csproj", "{16BE5A98-4AC1-72C5-C02A-31F23F970A26}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HealthNet.Homecare.IdentityServer.Infrastructure", "HealthNet.Homecare.IdentityServer.Infrastructure\HealthNet.Homecare.IdentityServer.Infrastructure.csproj", "{2E2ECBD3-2F21-4A6B-8299-FB1F29364549}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HealthNet.Homecare.IdentityServer.Abstractions", "HealthNet.Homecare.IdentityServer.Abstractions\HealthNet.Homecare.IdentityServer.Abstractions.csproj", "{DA9F7CA2-AC69-4B71-B808-C4571ABA0860}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HealthNet.Homecare.IdentityServer.Domain", "HealthNet.Homecare.IdentityServer.Domain\HealthNet.Homecare.IdentityServer.Domain.csproj", "{EBEF3642-2350-4B17-A4A2-58BA92F8DA6A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9C072518-E306-4F3E-B758-41B8D2DEBD66}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9C072518-E306-4F3E-B758-41B8D2DEBD66}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9C072518-E306-4F3E-B758-41B8D2DEBD66}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9C072518-E306-4F3E-B758-41B8D2DEBD66}.Release|Any CPU.Build.0 = Release|Any CPU
		{1B99FA21-4015-4682-DB9C-08F7D8B609D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1B99FA21-4015-4682-DB9C-08F7D8B609D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1B99FA21-4015-4682-DB9C-08F7D8B609D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1B99FA21-4015-4682-DB9C-08F7D8B609D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{16BE5A98-4AC1-72C5-C02A-31F23F970A26}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{16BE5A98-4AC1-72C5-C02A-31F23F970A26}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{16BE5A98-4AC1-72C5-C02A-31F23F970A26}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{16BE5A98-4AC1-72C5-C02A-31F23F970A26}.Release|Any CPU.Build.0 = Release|Any CPU
		{2E2ECBD3-2F21-4A6B-8299-FB1F29364549}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2E2ECBD3-2F21-4A6B-8299-FB1F29364549}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2E2ECBD3-2F21-4A6B-8299-FB1F29364549}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2E2ECBD3-2F21-4A6B-8299-FB1F29364549}.Release|Any CPU.Build.0 = Release|Any CPU
		{DA9F7CA2-AC69-4B71-B808-C4571ABA0860}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DA9F7CA2-AC69-4B71-B808-C4571ABA0860}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DA9F7CA2-AC69-4B71-B808-C4571ABA0860}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DA9F7CA2-AC69-4B71-B808-C4571ABA0860}.Release|Any CPU.Build.0 = Release|Any CPU
		{EBEF3642-2350-4B17-A4A2-58BA92F8DA6A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EBEF3642-2350-4B17-A4A2-58BA92F8DA6A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EBEF3642-2350-4B17-A4A2-58BA92F8DA6A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EBEF3642-2350-4B17-A4A2-58BA92F8DA6A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {53BBADC5-B923-4D97-855F-DE858B678436}
	EndGlobalSection
EndGlobal
