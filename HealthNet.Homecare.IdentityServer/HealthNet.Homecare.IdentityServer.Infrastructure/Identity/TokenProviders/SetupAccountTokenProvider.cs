using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.Identity.TokenProviders;

public class SetupAccountTokenProvider<TUser>(
	IDataProtectionProvider dataProtectionProvider,
	IOptions<SetupAccountTokenProviderOptions> options,
	ILogger<DataProtectorTokenProvider<TUser>> logger
) : DataProtectorTokenProvider<TUser>(dataProtectionProvider, options, logger) where TUser : class
{
}
