using HealthNet.Homecare.IdentityServer.Domain.Entities;
using Microsoft.AspNetCore.Identity;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.Identity
{
	public class AllowDuplicateEmailValidator : UserValidator<ApplicationUser>
	{
		public AllowDuplicateEmailValidator(IdentityErrorDescriber describer) : base(describer) { }

		public override async Task<IdentityResult> ValidateAsync(UserManager<ApplicationUser> manager, ApplicationUser user)
		{
			// Default validation but skip duplicate email check
			var result = await base.ValidateAsync(manager, user);

			// Remove duplicate email error
			var errors = result.Errors
				.Where(e => e.Code != nameof(IdentityErrorDescriber.DuplicateEmail))
				.ToList();

			return errors.Count == 0 ? IdentityResult.Success : IdentityResult.Failed(errors.ToArray());
		}
	}

}
