using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HealthNet.Homecare.IdentityServer.Infrastructure.EntityFramework.Migrations
{
	/// <inheritdoc />
	public partial class CreateProcedureInsertIdentityWebClient : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			// drop existing procedure if it exists
			migrationBuilder.Sql(@"IF OBJECT_ID(N'[dbo].[InsertIdentityWebClient]', N'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[InsertIdentityWebClient];
    PRINT 'Dropped existing procedure [dbo].[InsertIdentityWebClient]';
END");

			// create new procedure
			migrationBuilder.Sql(@"CREATE PROCEDURE InsertIdentityWebClient
    @ClientIdValue NVARCHAR(200),
    @ClientName NVARCHAR(200),
    @ClientDescription NVARCHAR(1000),
    -- use Duende.IdentityServer.Models Sha256 hash extension method to get hashed secret
    @ClientHashedSecret NVARCHAR(200),

    @ClientBaseUrl NVARCHAR(400),

    @AdditionalGrantTypes NVARCHAR(MAX) = NULL,
    @AdditionalClientScopes NVARCHAR(MAX) = NULL,
        
    @ApiScopeName NVARCHAR(200),
    @ApiScopeDisplayName NVARCHAR(200),
    @ApiScopeDescription NVARCHAR(1000),

    @ApiResourceName NVARCHAR(200),
    @ApiResourceDisplayName NVARCHAR(200),
    @ApiResourceDescription NVARCHAR(1000)
AS
BEGIN
    -- Insert API Scope
    IF NOT EXISTS (SELECT 1 FROM ApiScopes WHERE Name = @ApiScopeName)
    BEGIN
        INSERT INTO ApiScopes (Name, DisplayName, Description, Emphasize, Enabled, Required, ShowInDiscoveryDocument, NonEditable, Created)
        VALUES (@ApiScopeName, @ApiScopeDisplayName, @ApiScopeDescription, 0, 1, 0, 1, 0, GETDATE());
    END

    -- Insert API Resource
    DECLARE @ApiResourceId INT;
    IF NOT EXISTS (SELECT 1 FROM ApiResources WHERE Name = @ApiResourceName)
    BEGIN
        INSERT INTO ApiResources (Name, DisplayName, Description, Enabled, ShowInDiscoveryDocument, NonEditable, RequireResourceIndicator, Created)
        VALUES (@ApiResourceName, @ApiResourceDisplayName, @ApiResourceDescription, 1, 1, 0, 0, GETDATE());
        SET @ApiResourceId = SCOPE_IDENTITY();

        -- Link API Resource to Scope
        INSERT INTO ApiResourceScopes (Scope, ApiResourceId)
        VALUES (@ApiScopeName, @ApiResourceId);
    END
    ELSE
    BEGIN
        SELECT @ApiResourceId = Id FROM ApiResources WHERE Name = @ApiResourceName;
        IF NOT EXISTS (SELECT 1 FROM ApiResourceScopes WHERE Scope = @ApiScopeName AND ApiResourceId = @ApiResourceId)
        BEGIN
            INSERT INTO ApiResourceScopes (Scope, ApiResourceId)
            VALUES (@ApiScopeName, @ApiResourceId);
        END
    END

    -- Insert Client
    DECLARE @ClientDbId INT;
    SELECT @ClientDbId = Id FROM Clients WHERE ClientId = @ClientIdValue;

    IF @ClientDbId IS NULL
    BEGIN
        DECLARE @ClientDbIdTable TABLE (Id INT);

        INSERT INTO Clients (
            Enabled, ClientId, ProtocolType, RequireClientSecret, ClientName, Description, RequireConsent, AllowRememberConsent, AlwaysIncludeUserClaimsInIdToken,
            RequirePkce, AllowPlainTextPkce, RequireRequestObject, AllowAccessTokensViaBrowser, FrontChannelLogoutSessionRequired, BackChannelLogoutSessionRequired,
            AllowOfflineAccess, IdentityTokenLifetime, AccessTokenLifetime, AuthorizationCodeLifetime,
            AbsoluteRefreshTokenLifetime, SlidingRefreshTokenLifetime, RefreshTokenUsage, UpdateAccessTokenClaimsOnRefresh, 
            RefreshTokenExpiration, AccessTokenType, EnableLocalLogin, IncludeJwtId, AlwaysSendClientClaims, RequirePushedAuthorization, RequireDPoP, DPoPValidationMode,
            DPoPClockSkew, Created, DeviceCodeLifetime, NonEditable
        ) 
        OUTPUT INSERTED.Id INTO @ClientDbIdTable(Id)
        VALUES (
            1, -- Enabled
            @ClientIdValue,
            'oidc',
            0, -- RequireClientSecret
            @ClientName,
            @ClientDescription,
            0, -- RequireConsent
            1, -- AllowRememberConsent
            1, -- AlwaysIncludeUserClaimsInIdToken
            1, -- RequirePkce
            0, -- AllowPlainTextPkce
            0, -- RequireRequestObject
            0, -- AllowAccessTokensViaBrowser
            1, -- FrontChannelLogoutSessionRequired
            1, -- BackChannelLogoutSessionRequired
            1, -- AllowOfflineAccess
            300, -- IdentityTokenLifetime
            3600, -- AccessTokenLifetime 
            300, -- AuthorizationCodeLifetime
            2592000, -- AbsoluteRefreshTokenLifetime
            1296000, -- SlidingRefreshTokenLifetime
            0, -- RefreshTokenUsage
            0, -- UpdateAccessTokenClaimsOnRefresh
            1, -- RefreshTokenExpiration
            0, -- AccessTokenType
            1, -- EnableLocalLogin
            1, -- IncludeJwtId
            0, -- AlwaysSendClientClaims
            0, -- RequirePushedAuthorization
            0, -- RequireDPoP
            0, -- DPoPValidationMode
            '00:05:00', -- DPoPClockSkew
            GETDATE(), -- Created
            300, -- DeviceCodeLifetime
            0 -- NonEditable
        );

        SELECT @ClientDbId = Id FROM @ClientDbIdTable;

        -- Insert Client Secret
        INSERT INTO ClientSecrets (ClientId, Value, Type, Description, Expiration, Created)
        VALUES (@ClientDbId, @ClientHashedSecret, 'SharedSecret', CONCAT(@ClientName, ' Secret'), NULL, GETDATE());

        -- Insert CORS Origin
        INSERT INTO ClientCorsOrigins (ClientId, Origin)
        VALUES (@ClientDbId, @ClientBaseUrl);

        -- Insert Redirect URI
        DECLARE @ClientRedirectUri NVARCHAR(500) = CONCAT(@ClientBaseUrl, '/signin-callback');
        INSERT INTO ClientRedirectUris (ClientId, RedirectUri)
        VALUES (@ClientDbId, @ClientRedirectUri);
    END

    -- Insert PostLogoutRedirectUri as base + 'signout-callback'
    DECLARE @ClientPostLogoutRedirectUri NVARCHAR(500) = CONCAT(@ClientBaseUrl, '/signout-callback');
    MERGE ClientPostLogoutRedirectUris AS Target
    USING (
        SELECT @ClientDbId AS ClientId, @ClientPostLogoutRedirectUri AS PostLogoutRedirectUri
    ) AS Source
    ON Target.ClientId = Source.ClientId
    WHEN MATCHED THEN
        UPDATE SET PostLogoutRedirectUri = Source.PostLogoutRedirectUri
    WHEN NOT MATCHED THEN
        INSERT (ClientId, PostLogoutRedirectUri)
        VALUES (Source.ClientId, Source.PostLogoutRedirectUri);

    -- Insert default Grant Types
    INSERT INTO ClientGrantTypes (ClientId, GrantType)
    SELECT @ClientDbId, g.GrantType
    FROM (VALUES
        ('authorization_code')
    ) AS g(GrantType)
    WHERE NOT EXISTS (
        SELECT 1
        FROM ClientGrantTypes cg
        WHERE cg.ClientId = @ClientDbId
          AND cg.GrantType = g.GrantType
    );

    -- Insert additional Grant Types if provided
    IF @AdditionalGrantTypes IS NOT NULL
    BEGIN
        INSERT INTO ClientGrantTypes (ClientId, GrantType)
        SELECT @ClientDbId, LTRIM(RTRIM(value))
        FROM STRING_SPLIT(@AdditionalGrantTypes, ',')
        WHERE LTRIM(RTRIM(value)) NOT IN (
            SELECT GrantType
            FROM ClientGrantTypes
            WHERE ClientId = @ClientDbId
        );
    END

    -- Insert default Client Scopes
    INSERT INTO ClientScopes (ClientId, Scope)
    SELECT @ClientDbId, s.Scope
    FROM (VALUES 
        ('openid'), 
        ('profile'), 
        (@ApiScopeName)
    ) AS s(Scope)
    WHERE NOT EXISTS (
        SELECT 1
        FROM ClientScopes cs
        WHERE cs.ClientId = @ClientDbId
          AND cs.Scope = s.Scope
    );

    -- Insert additional Client Scopes if provided
    IF @AdditionalClientScopes IS NOT NULL
    BEGIN
        INSERT INTO ClientScopes (ClientId, Scope)
        SELECT @ClientDbId, LTRIM(RTRIM(value))
        FROM STRING_SPLIT(@AdditionalClientScopes, ',')
        WHERE LTRIM(RTRIM(value)) NOT IN (
            SELECT Scope
            FROM ClientScopes
            WHERE ClientId = @ClientDbId
        );
    END
END");
		}

		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{

		}
	}
}
