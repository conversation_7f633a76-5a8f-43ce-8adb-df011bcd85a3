CREATE PROCEDURE InsertAdminUser
    @Email NVARCHAR(256),
    @PasswordHash NVARCHAR(MAX)
AS
BEGIN
    DECLARE @AdminRoleId UNIQUEIDENTIFIER = NEWID();
    DECLARE @AdminUserId UNIQUEIDENTIFIER = NEWID();
    DECLARE @NormalizedEmail NVARCHAR(256) = UPPER(@Email);

    -- Check if Admin role already exists
    IF NOT EXISTS (SELECT 1 FROM AspNetRoles WHERE NormalizedName = 'ADMIN')
    BEGIN
        INSERT INTO AspNetRoles (Id, Name, NormalizedName, ConcurrencyStamp)
        VALUES (@AdminRoleId, 'Admin', 'ADMIN', NEWID());
    END
    ELSE
    BEGIN
        SELECT @AdminRoleId = Id FROM AspNetRoles WHERE NormalizedName = 'ADMIN';
    END

    -- Check if user already exists
    IF NOT EXISTS (SELECT 1 FROM AspNetUsers WHERE NormalizedUserName = @NormalizedEmail)
    BEGIN
        INSERT INTO AspNetUsers (
            Id, UserName, NormalizedUserName, Email, NormalizedEmail, Email<PERSON>onfirmed,
            PasswordHash, SecurityStamp, ConcurrencyStamp, LockoutEnabled, AccessFailedCount,
            PhoneNumberConfirmed, TwoFactorEnabled
        )
        VALUES (
            @AdminUserId,        -- Id
            @Email,              -- UserName
            @NormalizedEmail,    -- NormalizedUserName
            @Email,              -- Email
            @NormalizedEmail,    -- NormalizedEmail
            1,                   -- EmailConfirmed
            @PasswordHash,       -- PasswordHash
            NEWID(),             -- SecurityStamp
            NEWID(),             -- ConcurrencyStamp
            1,                   -- LockoutEnabled
            0,                   -- AccessFailedCount
            1,                   -- PhoneNumberConfirmed
            0                    -- TwoFactorEnabled
        );
    END
    ELSE
    BEGIN
        SELECT @AdminUserId = Id FROM AspNetUsers WHERE NormalizedUserName = @NormalizedEmail;
    END

    -- Assign Admin role to user if not already assigned
    IF NOT EXISTS (
        SELECT 1 FROM AspNetUserRoles WHERE UserId = @AdminUserId AND RoleId = @AdminRoleId
    )
    BEGIN
        INSERT INTO AspNetUserRoles (UserId, RoleId)
        VALUES (@AdminUserId, @AdminRoleId);
    END
END
