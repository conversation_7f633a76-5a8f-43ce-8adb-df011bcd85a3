CREATE PROCEDURE InsertIdentityApiClient
    @ClientIdValue NVARCHAR(200),
    @ClientName NVARCHAR(200),
    @ClientDescription NVARCHAR(1000),
    -- use Duende.IdentityServer.Models Sha256 hash extension method to get hashed secret
    @ClientHashedSecret NVARCHAR(200),

    @AdditionalGrantTypes NVARCHAR(MAX) = NULL,
    @AdditionalClientScopes NVARCHAR(MAX) = NULL,

    @ApiScopeName NVARCHAR(200),
    @ApiScopeDisplayName NVARCHAR(200),
    @ApiScopeDescription NVARCHAR(1000),

    @ApiResourceName NVARCHAR(200),
    @ApiResourceDisplayName NVARCHAR(200),
    @ApiResourceDescription NVARCHAR(1000)
AS
BEGIN
    -- Insert API Scope
    IF NOT EXISTS (SELECT 1 FROM ApiScopes WHERE Name = @ApiScopeName)
    BEGIN
        INSERT INTO ApiScopes (Name, DisplayName, Description, Emphasize, Enabled, Required, ShowInDiscoveryDocument, NonEditable, Created)
        VALUES (@ApiScopeName, @ApiScopeDisplayName, @ApiScopeDescription, 0, 1, 0, 1, 0, GETDATE());
    END

    -- Insert API Resource
    DECLARE @ApiResourceId INT;
    IF NOT EXISTS (SELECT 1 FROM ApiResources WHERE Name = @ApiResourceName)
    BEGIN
        INSERT INTO ApiResources (Name, DisplayName, Description, Enabled, ShowInDiscoveryDocument, NonEditable, RequireResourceIndicator, Created)
        VALUES (@ApiResourceName, @ApiResourceDisplayName, @ApiResourceDescription, 1, 1, 0, 0, GETDATE());
        SET @ApiResourceId = SCOPE_IDENTITY();

        -- Link API Resource to Scope
        INSERT INTO ApiResourceScopes (Scope, ApiResourceId)
        VALUES (@ApiScopeName, @ApiResourceId);
    END
    ELSE
    BEGIN
        SELECT @ApiResourceId = Id FROM ApiResources WHERE Name = @ApiResourceName;
        IF NOT EXISTS (SELECT 1 FROM ApiResourceScopes WHERE Scope = @ApiScopeName AND ApiResourceId = @ApiResourceId)
        BEGIN
            INSERT INTO ApiResourceScopes (Scope, ApiResourceId)
            VALUES (@ApiScopeName, @ApiResourceId);
        END
    END

    -- Insert Client
    DECLARE @ClientDbId INT;
    DECLARE @ClientDbIdTable TABLE (Id INT);

    INSERT INTO Clients (
        Enabled, ClientId, ProtocolType, RequireClientSecret, ClientName, Description, RequireConsent, AllowRememberConsent, AlwaysIncludeUserClaimsInIdToken,
        RequirePkce, AllowPlainTextPkce, RequireRequestObject, AllowAccessTokensViaBrowser, FrontChannelLogoutSessionRequired, BackChannelLogoutSessionRequired,
        AllowOfflineAccess, IdentityTokenLifetime, AccessTokenLifetime, AuthorizationCodeLifetime,
        AbsoluteRefreshTokenLifetime, SlidingRefreshTokenLifetime, RefreshTokenUsage, UpdateAccessTokenClaimsOnRefresh, 
        RefreshTokenExpiration, AccessTokenType, EnableLocalLogin, IncludeJwtId, AlwaysSendClientClaims, RequirePushedAuthorization, RequireDPoP, DPoPValidationMode,
        DPoPClockSkew, Created, DeviceCodeLifetime, NonEditable
    ) 
    OUTPUT INSERTED.Id INTO @ClientDbIdTable(Id)
    VALUES (
        1, -- Enabled
        @ClientIdValue,
        'oidc',
        1, -- RequireClientSecret
        @ClientName,
        @ClientDescription,
        0, -- RequireConsent
        1, -- AllowRememberConsent
        1, -- AlwaysIncludeUserClaimsInIdToken
        1, -- RequirePkce
        0, -- AllowPlainTextPkce
        0, -- RequireRequestObject
        0, -- AllowAccessTokensViaBrowser
        1, -- FrontChannelLogoutSessionRequired
        1, -- BackChannelLogoutSessionRequired
        1, -- AllowOfflineAccess
        300, -- IdentityTokenLifetime
        3600, -- AccessTokenLifetime 
        300, -- AuthorizationCodeLifetime
        2592000, -- AbsoluteRefreshTokenLifetime
        1296000, -- SlidingRefreshTokenLifetime
        0, -- RefreshTokenUsage
        0, -- UpdateAccessTokenClaimsOnRefresh
        1, -- RefreshTokenExpiration
        0, -- AccessTokenType
        1, -- EnableLocalLogin
        1, -- IncludeJwtId
        0, -- AlwaysSendClientClaims
        0, -- RequirePushedAuthorization
        0, -- RequireDPoP
        0, -- DPoPValidationMode
        '00:05:00', -- DPoPClockSkew
        GETDATE(), -- Created
        300, -- DeviceCodeLifetime
        0 -- NonEditable
    );

    SELECT @ClientDbId = Id FROM @ClientDbIdTable;

    -- Insert Client Secret
    INSERT INTO ClientSecrets (ClientId, Value, Type, Description, Expiration, Created)
    VALUES (@ClientDbId, @ClientHashedSecret, 'SharedSecret', CONCAT(@ClientName, ' Secret'), NULL, GETDATE());

    -- Insert default Grant Types
    INSERT INTO ClientGrantTypes (ClientId, GrantType)
    SELECT @ClientDbId, g.GrantType
    FROM (VALUES 
        ('client_credentials'), 
        ('authorization_code')
    ) AS g(GrantType)
    WHERE NOT EXISTS (
        SELECT 1
        FROM ClientGrantTypes cg
        WHERE cg.ClientId = @ClientDbId
          AND cg.GrantType = g.GrantType
    );

    -- Insert additional Grant Types if provided
    IF @AdditionalGrantTypes IS NOT NULL
    BEGIN
        INSERT INTO ClientGrantTypes (ClientId, GrantType)
        SELECT @ClientDbId, TRIM(value)
        FROM STRING_SPLIT(@AdditionalGrantTypes, ',')
        WHERE TRIM(value) NOT IN (
            SELECT GrantType
            FROM ClientGrantTypes
            WHERE ClientId = @ClientDbId
        );
    END

    -- Insert default Client Scopes
    INSERT INTO ClientScopes (ClientId, Scope)
    SELECT @ClientDbId, s.Scope
    FROM (VALUES 
        ('openid'), 
        ('profile'), 
        (@ApiScopeName)
    ) AS s(Scope)
    WHERE NOT EXISTS (
        SELECT 1
        FROM ClientScopes cs
        WHERE cs.ClientId = @ClientDbId
          AND cs.Scope = s.Scope
    );

    -- Insert additional Client Scopes if provided
    IF @AdditionalClientScopes IS NOT NULL
    BEGIN
        INSERT INTO ClientScopes (ClientId, Scope)
        SELECT @ClientDbId, TRIM(value)
        FROM STRING_SPLIT(@AdditionalClientScopes, ',')
        WHERE TRIM(value) NOT IN (
            SELECT Scope
            FROM ClientScopes
            WHERE ClientId = @ClientDbId
        );
    END
END
