DECLARE 
    @ClientIdValue NVARCHAR(200) = 'HealthNet.Homecare.Client',
    @ClientName NVARCHAR(200) = 'Console Client',
    @ClientDescription NVARCHAR(1000) = 'Console client for accessing protected API',
    -- use Duende.IdentityServer.Models Sha256 hash extension method to get hashed secret
    @ClientHashedSecret NVARCHAR(200) = 'xSgxYkQl4GJcBIi1/ClFqDO7a9nnqsLW9p6bstiQM+4=',

    @AlwaysIncludeUserClaimsInIdToken BIT = 0,
    @RequirePkce BIT = 1,
    @RequireClientSecret BIT = 1,
    @ClientCorsOrigins NVARCHAR(200) = 'http://localhost:3000',
    @ClientRedirectUri NVARCHAR(200) = 'http://localhost:3000/callback',

    @ApiScopeName NVARCHAR(200) = 'api://HealthNet.Homecare.ApiResource/.default',
    @ApiScopeDisplayName NVARCHAR(200) = 'Weather API',
    @ApiScopeDescription NVARCHAR(1000) = 'Protected Weather API',

    @ApiResourceName NVARCHAR(200) = 'weather-api',
    @ApiResourceDisplayName NVARCHAR(200) = 'Weather API',
    @ApiResourceDescription NVARCHAR(1000) = 'Protected Weather API';

-- Insert API Scope with required fields
INSERT INTO ApiScopes (Name, DisplayName, Description, Emphasize, Enabled, Required, ShowInDiscoveryDocument, NonEditable, Created)
VALUES (@ApiScopeName, @ApiScopeDisplayName, @ApiScopeDescription, 0, 1, 0, 1, 0, GETDATE());

-- Insert API Resource with required fields
INSERT INTO ApiResources (Name, DisplayName, Description, Enabled, ShowInDiscoveryDocument, NonEditable, RequireResourceIndicator, Created)
VALUES (@ApiResourceName, @ApiResourceDisplayName, @ApiResourceDescription, 1, 1, 0, 0, GETDATE());

DECLARE @ApiResourceId INT = SCOPE_IDENTITY();

-- Link API Resource to Scope
INSERT INTO ApiResourceScopes (Scope, ApiResourceId)
VALUES (@ApiScopeName, @ApiResourceId);

-- Insert Client with required fields
DECLARE @ClientDbId INT;
DECLARE @ClientDbIdTable TABLE (Id INT);

INSERT INTO Clients (
    Enabled, ClientId, ProtocolType, RequireClientSecret, ClientName, Description, RequireConsent, AllowRememberConsent, AlwaysIncludeUserClaimsInIdToken,
    RequirePkce, AllowPlainTextPkce, RequireRequestObject, AllowAccessTokensViaBrowser, FrontChannelLogoutSessionRequired, BackChannelLogoutSessionRequired,
    AllowOfflineAccess, IdentityTokenLifetime, AccessTokenLifetime, AuthorizationCodeLifetime,
    AbsoluteRefreshTokenLifetime, SlidingRefreshTokenLifetime, RefreshTokenUsage, UpdateAccessTokenClaimsOnRefresh, 
    RefreshTokenExpiration, AccessTokenType, EnableLocalLogin, IncludeJwtId, AlwaysSendClientClaims, RequirePushedAuthorization, RequireDPoP, DPoPValidationMode,
    DPoPClockSkew, Created, DeviceCodeLifetime, NonEditable
) 
OUTPUT INSERTED.Id INTO @ClientDbIdTable(Id)
VALUES (
    1, -- Enabled
    @ClientIdValue,
    'oidc',
    @RequireClientSecret, -- RequireClientSecret
    @ClientName,
    @ClientDescription,
    0, -- RequireConsent
    1, -- AllowRememberConsent
    @AlwaysIncludeUserClaimsInIdToken,
    @RequirePkce,
    0, -- AllowPlainTextPkce
    0, -- RequireRequestObject
    0, -- AllowAccessTokensViaBrowser
    1, -- FrontChannelLogoutSessionRequired
    1, -- BackChannelLogoutSessionRequired
    1, -- AllowOfflineAccess
    300, -- IdentityTokenLifetime
    3600, -- AccessTokenLifetime 
    300, -- AuthorizationCodeLifetime
    2592000, -- AbsoluteRefreshTokenLifetime
    1296000, -- SlidingRefreshTokenLifetime
    0, -- RefreshTokenUsage
    0, -- UpdateAccessTokenClaimsOnRefresh
    1, -- RefreshTokenExpiration
    0, -- AccessTokenType
    1, -- EnableLocalLogin
    1, -- IncludeJwtId
    0, -- AlwaysSendClientClaims
    0, -- RequirePushedAuthorization
    0, -- RequireDPoP
    0, -- DPoPValidationMode
    '00:05:00', -- DPoPClockSkew
    GETDATE(), -- Created
    300, -- DeviceCodeLifetime
    0 -- NonEditable
);

SELECT @ClientDbId = Id FROM @ClientDbIdTable;

-- Insert Client Secret (hashed)
INSERT INTO ClientSecrets (ClientId, Value, Type, Description, Expiration, Created)
VALUES (@ClientDbId, @ClientHashedSecret, 'SharedSecret', CONCAT(@ClientName, ' Secret'), NULL, GETDATE());

-- Insert Allowed Grant Type 
INSERT INTO ClientGrantTypes (ClientId, GrantType)
VALUES (@ClientDbId, 'client_credentials');

-- Insert Allowed Scope 
INSERT INTO ClientScopes (ClientId, Scope)
VALUES (@ClientDbId, @ApiScopeName);

-- Insert Cors Origin
INSERT INTO ClientCorsOrigins (ClientId, Origin)
VALUES (@ClientDbId, @ClientCorsOrigins );

-- Insert Redirect Uri
INSERT INTO ClientRedirectUris (ClientId, RedirectUri)
VALUES (@ClientDbId, @ClientRedirectUri);
