using Duende.IdentityServer.Models;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HealthNet.Homecare.IdentityServer.Infrastructure.EntityFramework.Migrations
{
	/// <inheritdoc />
	public partial class AddAdherePredictClientConfiguration : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			var password = "23<6A]Q91|IX.ly@";
			var hashedPassword = password.Sha256().Replace("'", "''"); // escape single quotes

			migrationBuilder.Sql(@$"EXEC InsertIdentityWebClient
    @ClientIdValue = 'HealthNet.AdherePredict.WebClient',
    @ClientName = 'Adhere Predict Client',
    @ClientDescription = 'Client for Adhere Predict web frontend',
    @ClientHashedSecret = '{hashedPassword}',
    @ClientBaseUrl = 'https://adherepredict-frontend-staging-d0bb8fc30856.herokuapp.com',
    @AdditionalGrantTypes = NULL,
    @AdditionalClientScopes = NULL,
    @ApiScopeName = 'adhp-api',
    @ApiScopeDisplayName = 'Adhere Predict API',
    @ApiScopeDescription = 'API scope for Adhere Predict client',
    @ApiResourceName = 'HealthNet.AdherePredict.DataAPI',
    @ApiResourceDisplayName = 'Adhere Predict Resource',
    @ApiResourceDescription = 'Protected API for Adhere Predict client';");
		}

		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{

		}
	}
}
