using HealthNet.Homecare.IdentityServer.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.EntityFramework.Configurations
{
	public class UserDeviceConfiguration : IEntityTypeConfiguration<UserDevice>
	{
		public void Configure(EntityTypeBuilder<UserDevice> builder)
		{
			builder.HasKey(x => x.Id);

			builder.HasOne(x => x.User)
				.WithMany(u => u.Devices)
				.HasForeignKey(x => x.UserId)
				.IsRequired()
				.OnDelete(DeleteBehavior.Cascade);
		}
	}
}
