using HealthNet.Homecare.IdentityServer.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.EntityFramework.Configurations
{
	public class UserOneTimePasswordConfiguration : IEntityTypeConfiguration<UserOneTimePassword>
	{
		public void Configure(EntityTypeBuilder<UserOneTimePassword> builder)
		{
			builder.Has<PERSON><PERSON>(x => x.Id);

			builder.HasOne(x => x.LoginSession)
				.WithMany(s => s.UserOneTimePasswords)
				.HasForeignKey(x => x.SessionId)
				.IsRequired()
				.OnDelete(DeleteBehavior.Cascade);
		}
	}
}
