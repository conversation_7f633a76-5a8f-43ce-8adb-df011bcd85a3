using HealthNet.Homecare.IdentityServer.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.EntityFramework.Configurations;

public class CommunicationTemplateConfiguration : IEntityTypeConfiguration<CommunicationTemplate>
{
    public void Configure(EntityTypeBuilder<CommunicationTemplate> builder)
    {
        builder.HasKey(x => x.Id);

        builder.Property(e => e.Name)
            .IsRequired();

        builder.HasIndex(e => e.Name)
            .IsUnique();

        builder.Property(e => e.Subject)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(e => e.Template)
            .IsRequired();
    }
}
