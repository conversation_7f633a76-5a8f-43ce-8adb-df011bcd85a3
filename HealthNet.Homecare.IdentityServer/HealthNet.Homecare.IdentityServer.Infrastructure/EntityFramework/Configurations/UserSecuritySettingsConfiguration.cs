using HealthNet.Homecare.IdentityServer.Domain.Entities;
using HealthNet.Homecare.IdentityServer.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.EntityFramework.Configurations;

public class UserSecuritySettingsConfiguration : IEntityTypeConfiguration<UserSecuritySettings>
{
	public void Configure(EntityTypeBuilder<UserSecuritySettings> builder)
	{
		builder.HasKey(x => x.Id);

		builder.Property(x => x.MfaType)
			.HasConversion<byte>()
			.IsRequired()
			.HasDefaultValue(MfaType.Email);

		builder.Property(x => x.MfaRecoveryCode)
			.HasMaxLength(256);

		builder.Property(x => x.CreatedAt)
			.IsRequired();

		builder.HasOne(x => x.User)
			.WithOne(x => x.SecuritySettings)
			.HasForeignKey<UserSecuritySettings>(x => x.UserId)
			.OnDelete(DeleteBehavior.Cascade);
	}
}
