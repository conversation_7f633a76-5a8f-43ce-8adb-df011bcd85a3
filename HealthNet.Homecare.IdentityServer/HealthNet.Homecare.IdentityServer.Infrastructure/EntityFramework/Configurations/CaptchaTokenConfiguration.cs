using HealthNet.Homecare.IdentityServer.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.EntityFramework.Configurations;

public class CaptchaTokenConfiguration : IEntityTypeConfiguration<CaptchaToken>
{
    public void Configure(EntityTypeBuilder<CaptchaToken> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Id)
            .ValueGeneratedOnAdd()
            .IsRequired();
    }
}
