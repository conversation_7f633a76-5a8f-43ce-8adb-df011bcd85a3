using HealthNet.Homecare.IdentityServer.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.EntityFramework.Configurations
{
	public class UserLoginSessionConfiguration : IEntityTypeConfiguration<UserLoginSession>
	{
		public void Configure(EntityTypeBuilder<UserLoginSession> builder)
		{
			builder.<PERSON><PERSON><PERSON>(x => x.Id);

			builder.HasOne(x => x.User)
				.WithMany(u => u.LoginSessions)
				.HasForeignKey(x => x.UserId)
				.IsRequired()
				.OnDelete(DeleteBehavior.Cascade);
		}
	}
}
