using HealthNet.Homecare.IdentityServer.Abstractions.EntityFramework.Repositories;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.EntityFramework.Repositories;

public class GenericRepository<TEntity, TId>(
	ApplicationDbContext context
) : IGenericRepository<TEntity, TId> where TEntity : class
{
	public DbContext DbContext { get; } = context;
	public IQueryable<TEntity> DbSet { get; } = context.Set<TEntity>();

	public void Add(TEntity entity)
	{
		DbContext.Set<TEntity>().Add(entity);
	}

	public async Task AddAsync(TEntity entity)
	{
		await DbContext.Set<TEntity>().AddAsync(entity);
	}

	public void AddRange(IEnumerable<TEntity> entities)
	{
		DbContext.Set<TEntity>().AddRange(entities);
	}

	public async Task AddRangeAsync(IEnumerable<TEntity> entities)
	{
		await DbContext.Set<TEntity>().AddRangeAsync(entities);
	}

	public void Delete(TEntity entity)
	{
		DbContext.Set<TEntity>().Remove(entity);
	}

	public void DeleteRange(IEnumerable<TEntity> entities)
	{
		DbContext.Set<TEntity>().RemoveRange(entities);
	}

	public async Task<TEntity> FirstAsync(Expression<Func<TEntity, bool>> predicate)
	{
		return await DbSet.FirstAsync(predicate);
	}

	public async Task<TEntity> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, params Expression<Func<TEntity, object>>[] includes)
	{
		IQueryable<TEntity> query = DbSet;

		foreach (var include in includes)
		{
			query = query.Include(include);
		}

		return await query.FirstOrDefaultAsync(predicate);
	}

	public async Task<TEntity> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, Func<IQueryable<TEntity>, IQueryable<TEntity>>? include = null)
	{
		IQueryable<TEntity> query = DbSet;

		if (include != null)
		{
			query = include(query);
		}

		return await query.FirstOrDefaultAsync(predicate);
	}

	public async Task<TEntity> SingleAsync(Expression<Func<TEntity, bool>> predicate)
	{
		return await DbSet.SingleAsync(predicate);
	}

	public TEntity SingleOrDefault(Expression<Func<TEntity, bool>> predicate)
	{
		return DbSet.SingleOrDefault(predicate);
	}

	public async Task<TEntity> SingleOrDefaultAsync(Expression<Func<TEntity, bool>> predicate)
	{
		return await DbSet.SingleOrDefaultAsync(predicate);
	}
	public async Task<TEntity> FindAsync(object[] keyValues)
	{
		return await DbContext.Set<TEntity>().FindAsync(keyValues);
	}
	public async Task<IReadOnlyList<TEntity>> ListAllAsync()
	{
		return await DbSet.ToListAsync();
	}

	public async Task<IReadOnlyList<TEntity>> ListAsync(Expression<Func<TEntity, bool>> predicate)
	{
		return await DbSet.Where(predicate).ToListAsync();
	}
	public void Update(TEntity entity)
	{
		DbContext.Entry(entity).State = EntityState.Modified;
	}

	public void UpdateRange(IEnumerable<TEntity> entities)
	{
		DbContext.UpdateRange(entities);
	}

	public async Task<bool> SaveChangesAsync()
	{
		var result = await DbContext.SaveChangesAsync();
		return result > 0;
	}
}
