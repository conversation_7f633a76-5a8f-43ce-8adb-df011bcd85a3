using HealthNet.Homecare.IdentityServer.Domain.Common;
using System.ComponentModel;
using System.Globalization;
using System.Net.Mail;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.Extensions;

public static class StringExtension
{
	public static bool IsEmail(this string text)
	{
		try
		{
			var mailAddress = new MailAddress(text);

			return !string.IsNullOrWhiteSpace(mailAddress.Address);
		}
		catch
		{
			return false;
		}
	}
	public static T ConvertTo<T>(this string input)
	{
		try
		{
			var converter = TypeDescriptor.GetConverter(typeof(T));
			return (T)converter.ConvertFromString(input);
		}
		catch (NotSupportedException)
		{
			return default;
		}
	}

	public static int ConvertToInt(this string input)
	{
		try
		{
			int.TryParse(input, out var value);
			return value;
		}
		catch (NotSupportedException)
		{
			return 0;
		}
	}
	public static bool EqualIgnoreCase(this string source, string toCheck)
	{
		return string.Equals(source, toCheck, StringComparison.OrdinalIgnoreCase);
	}

	public static List<int> ToListInt(this string input, char[] splitCharacter)
	{
		var result = new List<int>();

		try
		{
			var list = input.Split(splitCharacter, StringSplitOptions.RemoveEmptyEntries).ToList();
			foreach (var item in list)
			{
				result.Add(int.Parse(item));
			}
			return result;
		}
		catch (NotSupportedException)
		{
			return [];
		}
	}

	public static string AddFilter(this string input, string filter)
	{
		return string.IsNullOrWhiteSpace(filter) ? input : $"{input}&{filter}";
	}

	public static string EscapeEncodeString(this string input)
	{
		if (string.IsNullOrEmpty(input)) return input;

		input = input.Replace("'", "''");
		input = input.Replace("+", "%2B");
		input = input.Replace("/", "%2F");
		input = input.Replace("%", "%25");
		input = input.Replace("#", "%23");
		input = input.Replace("&", "%26");
		input = input.Replace("?", "%3F");

		return input;
	}
	public static string ComputeSha512Hash(this string rawData)
	{
		using var sha512Hash = SHA512.Create();
		var bytes = sha512Hash.ComputeHash(Encoding.UTF8.GetBytes(rawData));
		var builder = new StringBuilder();

		foreach (var t in bytes)
		{
			builder.Append(t.ToString("x2"));
		}
		return builder.ToString();
	}

	public static string ParseFromCurrency(this decimal number, string format)
	{
		return number.ToString(format, CultureInfo.InvariantCulture);
	}

	public static bool IsJsonValid(this string? json)
	{
		if (string.IsNullOrWhiteSpace(json))
			return false;

		try
		{
			using var jsonDoc = JsonDocument.Parse(json);
			return true;
		}
		catch (JsonException)
		{
			return false;
		}
	}

	public static bool ToBoolean(this string input)
	{
		return input.ToUpper().Equals("YES") || input.ToUpper().Equals("TRUE");
	}

	public static string MaskEmail(this string email)
	{
		if (string.IsNullOrEmpty(email))
			return string.Empty;
		if (!email.Contains('@'))
			return new string('*', email.Length);
		return email.Split('@')[0].Length < 4 ? @"*@*.*" : Regex.Replace(email, AppConstants.Regex.MaskEmail, m => new string('*', m.Length), RegexOptions.None, TimeSpan.FromMilliseconds(300));
	}

	public static string MaskMobile(this string phoneNumber)
	{
		if (string.IsNullOrEmpty(phoneNumber)) return string.Empty;
		return phoneNumber[..3] + new string('*', phoneNumber.Length - 7) + phoneNumber[^4..];
	}
}
