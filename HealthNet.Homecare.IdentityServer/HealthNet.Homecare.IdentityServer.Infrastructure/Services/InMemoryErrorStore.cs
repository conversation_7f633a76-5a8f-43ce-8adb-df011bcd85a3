using Duende.IdentityServer.Models;
using HealthNet.Homecare.IdentityServer.Abstractions.Services;
using Microsoft.Extensions.Caching.Memory;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.Services;

public class InMemoryErrorStore(IMemoryCache cache) : IErrorStore
{
    private readonly IMemoryCache _cache = cache;
    private readonly TimeSpan _expiration = TimeSpan.FromMinutes(10);

    public Task<ErrorMessage?> RetrieveAsync(string errorId)
    {
        _cache.TryGetValue(errorId, out ErrorMessage? message);
        return Task.FromResult(message);
    }

    public Task<string> StoreAsync(ErrorMessage message)
    {
        var errorId = Guid.NewGuid().ToString("N");

        _cache.Set(errorId, message, _expiration);
        return Task.FromResult(errorId);
    }
}
