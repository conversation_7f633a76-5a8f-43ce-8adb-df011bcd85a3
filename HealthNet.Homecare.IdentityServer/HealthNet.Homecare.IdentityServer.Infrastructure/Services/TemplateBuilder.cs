using Duende.IdentityModel;
using HealthNet.Homecare.IdentityServer.Abstractions.DTO;
using HealthNet.Homecare.IdentityServer.Abstractions.EntityFramework.Repositories;
using HealthNet.Homecare.IdentityServer.Domain.Common;
using HealthNet.Homecare.IdentityServer.Domain.Entities;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.Services;

public class TemplateBuilder(IGenericRepository<CommunicationTemplate, Guid> communicationTemplateRepository)
{
	private readonly IGenericRepository<CommunicationTemplate, Guid> _communicationTemplateRepository = communicationTemplateRepository;

	public async Task<TemplateResultDto> BuildTemplateAsync(string templateName, IDictionary<string, string>? values = null)
	{
		var template = await GetTemplateAsync(templateName);
		var templateContent = RenderTemplateContent(template, values);

		return new TemplateResultDto
		{
			Content = templateContent,
			Subject = template.Subject,
		};
	}

	public async Task<CommunicationTemplate> GetTemplateAsync(string templateName)
	{
		var template = await _communicationTemplateRepository.FirstOrDefaultAsync(t => t.Name == templateName);
		if (template == null)
		{
			throw new ArgumentException($"Template with name '{templateName}' not found.");
		}

		return template;
	}

	public string RenderTemplateContent(CommunicationTemplate template, IDictionary<string, string>? values = null)
	{
		var result = template.Template;
		if (values is null)
		{
			return result;
		}

		foreach (var (key, value) in values)
		{
			result = result.Replace($"{key}", value);
		}
		return result;
	}

	public async Task<TemplateResultDto> GetMfaTemplate(string code, string baseUrl)
	{
		var emailTemplate = await BuildTemplateAsync(
			EmailTemplates.TwoFactorEmailTemplateName,
			new Dictionary<string, string>
			{
					{ "{LOGO_URL}", $"{baseUrl}/images/logo.svg" },
					{ "{ILLUSTRATION_URL}", $"{baseUrl}/images/email-illustration.png" },
					{ "{CODE}", code }
			}
		);
		return emailTemplate;
	}
}
