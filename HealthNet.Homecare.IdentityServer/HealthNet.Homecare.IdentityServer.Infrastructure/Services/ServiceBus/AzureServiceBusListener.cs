using Azure.Messaging.ServiceBus;
using Azure.Messaging.ServiceBus.Administration;
using HealthNet.Homecare.IdentityServer.Abstractions.DTO.ServiceBus;
using HealthNet.Homecare.IdentityServer.Abstractions.Enums;
using HealthNet.Homecare.IdentityServer.Infrastructure.Configurations;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using System.Text.Json;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.Services.ServiceBus;

public class AzureServiceBusListener : BackgroundService
{
	private readonly ServiceBusProcessor _processor;
	private readonly IServiceScopeFactory _scopeFactory;
	private readonly ILogger<AzureServiceBusListener> _logger;
	private readonly AzureServiceBusOptions _config;

	public AzureServiceBusListener(IOptions<AzureServiceBusOptions> options, IServiceScopeFactory scopeFactory, ILogger<AzureServiceBusListener> logger)
	{
		_config = options.Value;
		_scopeFactory = scopeFactory;
		_logger = logger;

		EnsureQueueExistsAsync(_config.ConnectionString, _config.IdentityServerQueue).GetAwaiter().GetResult();

		var client = new ServiceBusClient(_config.ConnectionString);

		_processor = client.CreateProcessor(_config.IdentityServerQueue, new ServiceBusProcessorOptions
		{
			MaxConcurrentCalls = 5,
			AutoCompleteMessages = false
		});
	}

	private async Task EnsureQueueExistsAsync(string connectionString, string queueName)
	{
		var adminClient = new ServiceBusAdministrationClient(connectionString);

		if (!await adminClient.QueueExistsAsync(queueName))
		{
			_logger.LogInformation($"[ServiceBus] Queue '{queueName}' does not exist. Creating...");
			await adminClient.CreateQueueAsync(new CreateQueueOptions(queueName));
			_logger.LogInformation($"[ServiceBus] Queue '{queueName}' created.");
		}
		else
		{
			_logger.LogInformation($"[ServiceBus] Queue '{queueName}' already exists.");
		}

		var queueProperties = adminClient.GetQueueAsync(queueName).GetAwaiter().GetResult();

		if (queueProperties.Value.MaxDeliveryCount != 2)
		{
			queueProperties.Value.MaxDeliveryCount = 2;
			adminClient.UpdateQueueAsync(queueProperties).GetAwaiter().GetResult();
			_logger.LogInformation($"[ServiceBus] Updated MaxDeliveryCount to 4 for queue: {queueName}");
		}
	}

	protected override async Task ExecuteAsync(CancellationToken stoppingToken)
	{
		_processor.ProcessMessageAsync += HandleMessageAsync;
		_processor.ProcessErrorAsync += args =>
		{
			_logger.LogError($"[ServiceBus] Error message: {args.Exception.Message}; Exception: {args.Exception}");
			return Task.CompletedTask;
		};
		await _processor.StartProcessingAsync(stoppingToken);
	}

	private async Task HandleMessageAsync(ProcessMessageEventArgs args)
	{
		var messageType = ServiceBusMessageType.Default;
		if (string.IsNullOrEmpty(args.Message.To) || !Enum.TryParse(args.Message.To, out messageType))
		{
			_logger.LogWarning($"[ServiceBus] Unknown message type: {args.Message.To}");
			return;
		}

		var json = args.Message.Body.ToString();
		using var scope = _scopeFactory.CreateScope();
		var receiveSyncUserMfaCommandHandler = scope.ServiceProvider.GetRequiredService<ReceiveSyncUserMfaCommandHandler>();
		var policy = Policy
			.Handle<Exception>()
			.RetryAsync(_config.RetryAttempts, (ex, attempt) => _logger.LogInformation($"Retry {attempt}: {ex.Message}"));
		try
		{
			switch (messageType)
			{
				case ServiceBusMessageType.SyncUserMfa:
					var command = JsonSerializer.Deserialize<SyncUserMfaCommand>(json)!;
					await policy.ExecuteAsync(() => receiveSyncUserMfaCommandHandler.Handle(command, args.CancellationToken));
					break;
				default:
					_logger.LogWarning($"[ServiceBus] Default message type: {args.Message.To}");
					break;
			}
			await args.CompleteMessageAsync(args.Message);
		}
		catch (Exception ex)
		{
			_logger.LogError($"[DLQ] Sending to dead-letter: {ex.Message}");
			await args.DeadLetterMessageAsync(args.Message, "ProcessingFailed", ex.Message);
		}
	}

	public override async Task StopAsync(CancellationToken cancellationToken)
	{
		await _processor.StopProcessingAsync();
		await _processor.DisposeAsync();
	}
}
