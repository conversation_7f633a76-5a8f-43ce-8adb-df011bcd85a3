using HealthNet.Homecare.IdentityServer.Abstractions.DTO.ServiceBus;
using HealthNet.Homecare.IdentityServer.Abstractions.EntityFramework.Repositories;
using HealthNet.Homecare.IdentityServer.Domain.Entities;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.Services.ServiceBus;

public class ReceiveSyncUserMfaCommandHandler(IGenericRepository<UserSecuritySettings, Guid> securitySettingsRepository)
{
	private readonly IGenericRepository<UserSecuritySettings, Guid> _securitySettingsRepository = securitySettingsRepository;

	public async Task Handle(SyncUserMfaCommand request, CancellationToken cancellationToken)
	{
		var securitySettings = await _securitySettingsRepository.FirstOrDefaultAsync(
			x => x.User.UserName == request.UserName
		);
		if (securitySettings == null)
		{
			throw new InvalidOperationException($"Security settings for user '{request.UserName}' not found.");
		}

		if (string.IsNullOrEmpty(request.RecoveryCode))
		{
			throw new ArgumentException("Recovery code cannot be null or empty.", nameof(request.RecoveryCode));
		}

		securitySettings.MfaEnabled = request.Enabled;
		securitySettings.MfaDestination = request.Destination;
		securitySettings.MfaType = request.Type;

		securitySettings.MfaRecoveryCode = request.RecoveryCode;

		securitySettings.UpdatedAt = DateTime.UtcNow;

		_securitySettingsRepository.Update(securitySettings);
		await _securitySettingsRepository.SaveChangesAsync();
	}
}
