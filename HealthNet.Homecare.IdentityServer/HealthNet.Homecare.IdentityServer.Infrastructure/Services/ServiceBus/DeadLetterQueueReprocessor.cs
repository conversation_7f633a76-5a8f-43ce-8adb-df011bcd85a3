using Azure.Messaging.ServiceBus;
using Azure.Messaging.ServiceBus.Administration;
using HealthNet.Homecare.IdentityServer.Abstractions.DTO.ServiceBus;
using HealthNet.Homecare.IdentityServer.Abstractions.Enums;
using HealthNet.Homecare.IdentityServer.Infrastructure.Configurations;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using System.Text.Json;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.Services.ServiceBus;

public class DeadLetterQueueReprocessor : BackgroundService
{
	private readonly ServiceBusProcessor _dlqProcessor;
	private readonly ServiceBusSender _failedSender;
	private readonly IServiceScopeFactory _scopeFactory;
	private readonly ILogger<DeadLetterQueueReprocessor> _logger;
	private readonly AzureServiceBusOptions _config;
	private readonly string _failedQueue;

	public DeadLetterQueueReprocessor(IOptions<AzureServiceBusOptions> options, IServiceScopeFactory scopeFactory, ILogger<DeadLetterQueueReprocessor> logger)
	{
		_config = options.Value;
		_scopeFactory = scopeFactory;
		_logger = logger;

		var client = new ServiceBusClient(_config.ConnectionString);
		var queueName = _config.IdentityServerQueue;
		_failedQueue = $"failed-{queueName}";

		_dlqProcessor = client.CreateProcessor($"{queueName}/$DeadLetterQueue", new ServiceBusProcessorOptions
		{
			AutoCompleteMessages = false
		});

		EnsureQueueExistsAsync(_config.ConnectionString, _failedQueue).GetAwaiter().GetResult();
		_failedSender = client.CreateSender(_failedQueue);
	}

	private async Task EnsureQueueExistsAsync(string connectionString, string queueName)
	{
		var adminClient = new ServiceBusAdministrationClient(connectionString);

		if (!await adminClient.QueueExistsAsync(queueName))
		{
			_logger.LogInformation($"[ServiceBus] Queue '{queueName}' does not exist. Creating...");
			await adminClient.CreateQueueAsync(new CreateQueueOptions(queueName));
			_logger.LogInformation($"[ServiceBus] Queue '{queueName}' created.");
		}
		else
		{
			_logger.LogInformation($"[ServiceBus] Queue '{queueName}' already exists.");
		}
	}

	protected override async Task ExecuteAsync(CancellationToken stoppingToken)
	{
		_dlqProcessor.ProcessMessageAsync += async args =>
		{
			var messageType = ServiceBusMessageType.Default;
			if (string.IsNullOrEmpty(args.Message.To) || !Enum.TryParse(args.Message.To, out messageType))
			{
				_logger.LogWarning($"[DLQ] Unknown message type: {args.Message.To}");
				await args.CompleteMessageAsync(args.Message);
				return;
			}

			var json = args.Message.Body.ToString();
			using var scope = _scopeFactory.CreateScope();
			var handler = scope.ServiceProvider.GetRequiredService<ReceiveSyncUserMfaCommandHandler>();

			var policy = Policy
				.Handle<Exception>()
				.RetryAsync(_config.RetryAttempts, (ex, attempt) =>
					_logger.LogInformation($"[DLQ] Retry {attempt}: {ex.Message}")
				);

			try
			{
				switch (messageType)
				{
					case ServiceBusMessageType.SyncUserMfa:
						var command = JsonSerializer.Deserialize<SyncUserMfaCommand>(json)!;
						_logger.LogInformation($"[DLQ] Reprocessing SyncUserMfaCommand: {command.UserName}");
						await policy.ExecuteAsync(() => handler.Handle(command, args.CancellationToken));
						break;

					default:
						_logger.LogWarning($"[DLQ] Unsupported message type: {args.Message.To}");
						break;
				}
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "[DLQ] Reprocessing failed, forwarding to failed queue.");

				var failedMessage = new ServiceBusMessage(args.Message.Body)
				{
					TimeToLive = TimeSpan.FromDays(7),
					To = args.Message.To
				};

				// Preserve original metadata
				if (args.Message.DeadLetterReason != null)
					failedMessage.ApplicationProperties["OriginalDeadLetterReason"] = args.Message.DeadLetterReason;
				if (args.Message.DeadLetterErrorDescription != null)
					failedMessage.ApplicationProperties["OriginalDeadLetterDescription"] = args.Message.DeadLetterErrorDescription;

				failedMessage.ApplicationProperties["ErrorMessage"] = ex.Message;

				await _failedSender.SendMessageAsync(failedMessage);
				_logger.LogInformation($"[DLQ] Message forwarded to {_failedQueue}");
			}

			await args.CompleteMessageAsync(args.Message);
		};

		_dlqProcessor.ProcessErrorAsync += args =>
		{
			_logger.LogError(args.Exception, "[DLQ] Error message: {Message}", args.Exception.Message);
			return Task.CompletedTask;
		};

		await _dlqProcessor.StartProcessingAsync(stoppingToken);
	}

	public override async Task StopAsync(CancellationToken cancellationToken)
	{
		await _dlqProcessor.StopProcessingAsync();
		await _dlqProcessor.DisposeAsync();
	}
}
