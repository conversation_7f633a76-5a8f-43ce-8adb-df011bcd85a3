using Azure.Messaging.ServiceBus;
using Azure.Messaging.ServiceBus.Administration;
using HealthNet.Homecare.IdentityServer.Abstractions.DTO.ServiceBus;
using HealthNet.Homecare.IdentityServer.Abstractions.Enums;
using HealthNet.Homecare.IdentityServer.Abstractions.Services;
using HealthNet.Homecare.IdentityServer.Domain.Entities;
using HealthNet.Homecare.IdentityServer.Infrastructure.Configurations;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.Services.ServiceBus;

public class ServiceBusPublisher(
	ILogger<ServiceBusPublisher> logger,
	IOptions<AzureServiceBusOptions> options
) : IServiceBusPublisher
{
	private readonly AzureServiceBusOptions config = options.Value;
	private readonly ILogger<ServiceBusPublisher> _logger = logger;

	public async Task<bool> PublishSyncUserMfaAsync(string username, UserSecuritySettings securitySettings)
	{
		if (securitySettings is null)
		{
			return false;
		}

		try
		{
			var json = JsonSerializer.Serialize(
				new SyncUserMfaCommand(
					username,
					securitySettings.MfaEnabled,
					securitySettings.MfaType,
					securitySettings.MfaDestination,
					securitySettings.MfaRecoveryCode
				)
			);

			await EnsureQueueExistsAsync(config.ConnectionString, config.UserDataManagementQueue);

			var client = new ServiceBusClient(config.ConnectionString);

			var sender = client.CreateSender(config.UserDataManagementQueue);

			await sender.SendMessageAsync(new ServiceBusMessage(json) { To = ServiceBusMessageType.SyncUserMfa.ToString() });
			return true;
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Failed to publish SyncUserMfa message.");
			return false;
		}
	}

	private async Task EnsureQueueExistsAsync(string connectionString, string queueName)
	{
		var adminClient = new ServiceBusAdministrationClient(connectionString);

		if (!await adminClient.QueueExistsAsync(queueName))
		{
			_logger.LogInformation($"[ServiceBus] Queue '{queueName}' does not exist. Creating...");
			await adminClient.CreateQueueAsync(new CreateQueueOptions(queueName));
			_logger.LogInformation($"[ServiceBus] Queue '{queueName}' created.");
		}
		else
		{
			_logger.LogInformation($"[ServiceBus] Queue '{queueName}' already exists.");
		}
	}
}
