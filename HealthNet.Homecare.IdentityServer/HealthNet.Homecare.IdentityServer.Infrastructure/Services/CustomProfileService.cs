using Duende.IdentityServer.Models;
using Duende.IdentityServer.Services;
using HealthNet.Homecare.IdentityServer.Domain.Entities;
using Microsoft.AspNetCore.Identity;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.Services;

public class CustomProfileService(UserManager<ApplicationUser> userManager) : IProfileService
{
	private readonly UserManager<ApplicationUser> _userManager = userManager;

	public async Task GetProfileDataAsync(ProfileDataRequestContext context)
	{
		var user = await _userManager.GetUserAsync(context.Subject);
		if (user is null) return;

		var claims = await _userManager.GetClaimsAsync(user);

		context.IssuedClaims.AddRange(claims);
		context.IssuedClaims.AddRange(context.Subject.Claims);
	}

	public async Task IsActiveAsync(IsActiveContext context)
	{
		var user = await _userManager.GetUserAsync(context.Subject);
		context.IsActive = user != null;
	}
}
