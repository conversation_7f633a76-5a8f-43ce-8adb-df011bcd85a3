using HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.Communication;
using HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.Contact.Dto;
using HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.OData;
using HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.OData.Enums;
using HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.OData.Models;
using HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.WebUtilities;
using HealthNet.Homecare.DigitalTools.SharedKernel.Abstractions.Communication;
using HealthNet.Homecare.IdentityServer.Abstractions.DTO;
using HealthNet.Homecare.IdentityServer.Abstractions.DTO.Contact;
using HealthNet.Homecare.IdentityServer.Abstractions.EntityFramework.Repositories;
using HealthNet.Homecare.IdentityServer.Abstractions.Extensions;
using HealthNet.Homecare.IdentityServer.Abstractions.Services;
using HealthNet.Homecare.IdentityServer.Domain.Common;
using HealthNet.Homecare.IdentityServer.Domain.Entities;
using HealthNet.Homecare.IdentityServer.Domain.Enums;
using HealthNet.Homecare.IdentityServer.Domain.Helpers;
using HealthNet.Homecare.IdentityServer.Infrastructure.Configurations;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.Services;

public class ProfileManagementService(
	IEmailSender emailSender,
	IODataClient oDataClient,
	IWebUtilitiesProxy webUtilitiesProxy,
	ILogger<ProfileManagementService> logger,
	IHttpContextAccessor httpContextAccessor,
	IServiceBusPublisher serviceBusPublisher,
	IGenericRepository<UserLoginSession, Guid> userLoginSessionRepository,
	IGenericRepository<UserSecuritySettings, Guid> userSecuritySettingsRepository,
	IOptions<ContactAccountAuthenticationOptions> contactAccountAuthenticationOptions,
	LoginService loginService,
	TemplateBuilder templateBuilder,
	ICommunicationMessageSender messageSender,
	UserManager<ApplicationUser> userManager
)
{
	private const string CreatePasswordEmailTemplateName = "CreatePasswordEmailTemplate";
	private const string ResetPasswordEmailTemplateName = "ResetPasswordEmailTemplate";
	private const string ForgotUsernameManyAccountsEmailTemplateName = "ForgotUsernameManyAccountsEmailTemplate";
	private const string ForgotUsernameSingleAccountEmailTemplateName = "ForgotUsernameSingleAccountEmailTemplate";

	private const string WaitingForPasswordResetContactStatus = "WaitingForPasswordReset";

	private readonly IEmailSender _emailSender = emailSender;
	private readonly IODataClient _oDataClient = oDataClient;
	private readonly ILogger<ProfileManagementService> _logger = logger;
	private readonly IWebUtilitiesProxy _webUtilitiesProxy = webUtilitiesProxy;
	private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
	private readonly IServiceBusPublisher _serviceBusPublisher = serviceBusPublisher;
	private readonly IGenericRepository<UserLoginSession, Guid> _userLoginSessionRepository = userLoginSessionRepository;
	private readonly IGenericRepository<UserSecuritySettings, Guid> _userSecuritySettingsRepository = userSecuritySettingsRepository;
	private readonly ICommunicationMessageSender _messageSender = messageSender;

	private readonly LoginService _loginService = loginService;
	private readonly TemplateBuilder _templateBuilder = templateBuilder;
	private readonly UserManager<ApplicationUser> _userManager = userManager;
	private readonly ContactAccountAuthenticationOptions _authOptions = contactAccountAuthenticationOptions.Value;

	public async Task<bool> ForgotPasswordAsync(string username, string email, string? resetPasswordRedirectUrl = null)
	{
		var isUserNameAndEmailValid = await _webUtilitiesProxy.CheckContactNoAndEmailValidAsync(username, email);
		if (!isUserNameAndEmailValid.Data)
		{
			return false;
		}

		var result = new LoginResultDto();
		var contact = await GetContactsByUsername(username);
		var verifiedContact = VerifyContactInfo(result, contact);
		if (verifiedContact is null)
		{
			return false;
		}

		verifiedContact.Status = WaitingForPasswordResetContactStatus;

		var sessionId = Guid.NewGuid();
		var loginRequestDto = new LoginRequestDto()
		{
			UserName = username,
			SessionId = sessionId,
		};
		var user = await _loginService.AddLoginInformationAsync(loginRequestDto, verifiedContact);
		if (!string.IsNullOrEmpty(resetPasswordRedirectUrl))
		{
			user.ResetPasswordRedirectUrl = resetPasswordRedirectUrl;
			await _userManager.UpdateAsync(user);
		}

		await SyncUserAsync(user, verifiedContact);

		var token = await _userManager.GeneratePasswordResetTokenAsync(user);
		var encodedSession = _loginService.GetSessionIdEncoded(user.UserName, sessionId);

		var request = _httpContextAccessor.HttpContext?.Request;
		var baseUrl = $"{request?.Scheme}://{request?.Host}";
		var emailTemplate = await _templateBuilder.BuildTemplateAsync(ResetPasswordEmailTemplateName, new Dictionary<string, string>()
		{
			{"{NAME}", user.Firstname ?? ""},
			{"{LOGO_URL}", $"{baseUrl}/images/logo.svg"},
			{"{ILLUSTRATION_URL}", $"{baseUrl}/images/email-illustration.png"},
			{"{RESET_PASSWORD_URL}", $"{baseUrl}/Account/ResetPassword?UserId={encodedSession}&ResetToken={Uri.EscapeDataString(token)}"},
		});

		try
		{
			await _emailSender.SendEmailAsync(
				[verifiedContact.Email!],
				emailTemplate.Subject,
				emailTemplate.Content
			);
		}
		catch (Exception)
		{
			_logger.LogError("Failed to send reset password email to {Email} for user {Username}.", verifiedContact.Email!, user.UserName);
			return false;
		}

		return true;
	}

	public async Task<bool> ResetPasswordAsync(ApplicationUser user, string resetPasswordToken, string newPassword)
	{
		if (user is null || string.IsNullOrEmpty(resetPasswordToken) || string.IsNullOrEmpty(newPassword))
		{
			return false;
		}

		var loginResult = new LoginResultDto();
		var contact = await GetContactsByUsername(user.UserName!);
		var verifiedContact = VerifyContactInfo(loginResult, contact);
		if (verifiedContact is null)
		{
			return false;
		}

		var password = await _webUtilitiesProxy.ChangePasswordByEmailAsync(user.Email!, user.UserName!);

		var changePasswordResult = await _webUtilitiesProxy.ChangePasswordByUserAsync(
			user.UserName!,
			CommonFunction.ComputeSha256Hash(password.Data ?? "", _authOptions.Key),
			CommonFunction.ComputeSha256Hash(newPassword, _authOptions.Key)
		);

		if (!changePasswordResult.Data)
		{
			return false;
		}

		var result = await _userManager.ResetPasswordAsync(user, resetPasswordToken, newPassword);
		return result.Succeeded;
	}

	public async Task<bool> ForgotUsernameAsync(string email, string? loginUrl = null)
	{
		var getContactNoFromEmailResult = await _webUtilitiesProxy.GetContactNoFromEmailAsync(email);
		if (!getContactNoFromEmailResult.IsSuccess || string.IsNullOrEmpty(getContactNoFromEmailResult.Data))
		{
			return false;
		}

		var usernames = getContactNoFromEmailResult.Data.Split(',').ToList();
		var usernamesChunkSize = 300;
		var usernamesChunks = SplitUsernamesIntoChunks(usernames, usernamesChunkSize);

		var contacts = new List<ContactDto>();
		foreach (var usernameChunk in usernamesChunks)
		{
			string contactNos = string.Join(", ", usernameChunk);
			var contactsFromUsernames = await GetContactListByUsernames(contactNos);
			contacts.AddRange(contactsFromUsernames);
		}

		if (contacts.Count == 0)
		{
			return false;
		}

		var emailTemplate = await GetForgotUsernameEmailTemplate(contacts, loginUrl);

		try
		{
			await _emailSender.SendEmailAsync(
				[email],
				emailTemplate.Subject,
				emailTemplate.Content
			);
		}
		catch (Exception)
		{
			_logger.LogError("Failed to send forgot username email to {Email}", email);
			return false;
		}

		return true;
	}

	public async Task<bool> SetupAccountAsync(string username, string email, string? returnUrl = null)
	{
		var contacts = await GetContactsByUsername(username);
		var contact = contacts?.FirstOrDefault();
		if (contact is null)
		{
			return false;
		}

		if (string.IsNullOrEmpty(contact.Email) || !contact.Email.Equals(email, StringComparison.OrdinalIgnoreCase))
		{
			return false;
		}

		var sessionId = Guid.NewGuid();
		var loginRequestDto = new LoginRequestDto()
		{
			UserName = username,
			SessionId = sessionId,
		};
		var user = await _loginService.AddLoginInformationAsync(loginRequestDto, contact);

		await SyncUserAsync(user, contact);

		var token = await _userManager.GenerateUserTokenAsync(user, "SetupAccount", "Setup");
		var encodedSession = _loginService.GetSessionIdEncoded(user.UserName, sessionId);

		var request = _httpContextAccessor.HttpContext?.Request;
		var baseUrl = $"{request?.Scheme}://{request?.Host}";
		var setupAccountUrl = $"{baseUrl}/Account/Setup?UserId={encodedSession}&Token={Uri.EscapeDataString(token)}&Step=5";
		if (!string.IsNullOrEmpty(returnUrl))
		{
			setupAccountUrl = QueryHelpers.AddQueryString(setupAccountUrl, "ReturnUrl", returnUrl);
		}

		var emailTemplate = await _templateBuilder.BuildTemplateAsync(CreatePasswordEmailTemplateName, new Dictionary<string, string>()
		{
			{"{NAME}", user.Firstname ?? ""},
			{"{LOGO_URL}", $"{baseUrl}/images/logo.svg"},
			{"{ILLUSTRATION_URL}", $"{baseUrl}/images/email-illustration.png"},
			{"{CREATE_PASSWORD_URL}", setupAccountUrl},
		});

		try
		{
			await _emailSender.SendEmailAsync(
				[contact.Email!],
				emailTemplate.Subject,
				emailTemplate.Content
			);
		}
		catch (Exception)
		{
			_logger.LogError("Failed to send setup account email to {Email} for user {Username}.", contact.Email, user.UserName);
			return false;
		}

		return (await _webUtilitiesProxy.UpdatePortalOptInAsync(username, 1)).Data;
	}

	public async Task<bool> VerifySetupAccountToken(ApplicationUser user, string token)
	{
		return await _userManager.VerifyUserTokenAsync(user, "SetupAccount", "Setup", token);
	}

	public async Task<bool> VerifyResetTokenAsync(ApplicationUser user, string resetToken)
	{
		if (user is null || string.IsNullOrEmpty(resetToken))
		{
			return false;
		}

		try
		{
			var result = await _userManager.VerifyUserTokenAsync(
				user,
				_userManager.Options.Tokens.PasswordResetTokenProvider,
				"ResetPassword",
				resetToken
			);

			return result;
		}
		catch
		{
			return false;
		}
	}

	public async Task<bool> CreateNewPasswordAsync(ApplicationUser user, string setupAccountToken, string newPassword)
	{
		if (user is null || string.IsNullOrEmpty(setupAccountToken) || string.IsNullOrEmpty(newPassword))
		{
			return false;
		}

		var isSetupAccountTokenValid = await VerifySetupAccountToken(user!, setupAccountToken);
		if (!isSetupAccountTokenValid)
		{
			return false;
		}

		var loginResult = new LoginResultDto();
		var contact = await GetContactsByUsername(user.UserName!);
		var verifiedContact = VerifyContactInfo(loginResult, contact);
		if (verifiedContact is null)
		{
			return false;
		}

		var password = await _webUtilitiesProxy.ChangePasswordByEmailAsync(user.Email!, user.UserName!);

		var changePasswordResult = await _webUtilitiesProxy.ChangePasswordByUserAsync(
			user.UserName!,
			CommonFunction.ComputeSha256Hash(password.Data ?? "", _authOptions.Key),
			CommonFunction.ComputeSha256Hash(newPassword, _authOptions.Key)
		);

		if (!changePasswordResult.Data)
		{
			return false;
		}

		if (await _userManager.HasPasswordAsync(user))
		{
			var removePasswordResult = await _userManager.RemovePasswordAsync(user);
			if (!removePasswordResult.Succeeded)
			{
				return false;
			}
		}

		var result = await _userManager.AddPasswordAsync(user, newPassword);
		return result.Succeeded;
	}

	public async Task<bool> SetupMfaAsync(ApplicationUser user, MfaType mfaType, string mfaDestination)
	{
		if (user is null)
		{
			return false;
		}

		if (user.SecuritySettings is null)
		{
			// Create new settings if they don't exist
			user.SecuritySettings = new UserSecuritySettings
			{
				User = user,
				MfaEnabled = true,
				MfaType = mfaType,
				MfaDestination = mfaDestination,
			};

			await _userSecuritySettingsRepository.AddAsync(user.SecuritySettings);
		}
		else
		{
			// Update the existing settings
			user.SecuritySettings.MfaEnabled = true;
			user.SecuritySettings.MfaType = mfaType;
			user.SecuritySettings.MfaDestination = mfaDestination;
			user.SecuritySettings.UpdatedAt = DateTime.UtcNow;

			_userSecuritySettingsRepository.Update(user.SecuritySettings);
		}

		var updateResult = await _userSecuritySettingsRepository.SaveChangesAsync();
		if (!updateResult)
		{
			return false;
		}

		var publishResult = await _serviceBusPublisher.PublishSyncUserMfaAsync(user.UserName!, user.SecuritySettings);
		if (!publishResult)
		{
			_logger.LogInformation($"Failed to publish SyncUserMfa message for user {user.UserName}.");
		}

		return await _loginService.SendMfaCodeAsync(user);
	}

	public async Task<bool> VerifyMfaTokenAsync(ApplicationUser user, string token)
	{
		var tokenProvider = user?.SecuritySettings?.MfaType switch
		{
			MfaType.Email => TokenOptions.DefaultEmailProvider,
			MfaType.Phone => TokenOptions.DefaultPhoneProvider,
			MfaType.WhatsApp => TokenOptions.DefaultPhoneProvider,
			MfaType.Unknown => TokenOptions.DefaultEmailProvider,
			_ => null,
		};
		if (tokenProvider is null)
		{
			return false;
		}

		return await _userManager.VerifyTwoFactorTokenAsync(user!, tokenProvider, token);
	}

	public async Task<string?> GenerateRecoveryCodeAsync(ApplicationUser user)
	{
		var isMfaEnabled = user?.SecuritySettings?.MfaEnabled ?? false;
		if (!isMfaEnabled)
		{
			return null;
		}

		var recoveryCodes = await _userManager.GenerateNewTwoFactorRecoveryCodesAsync(user!, 1);
		var recoveryCode = recoveryCodes?.FirstOrDefault();

		var hashedRecoveryCode = CommonFunction.ComputeSha256Hash(recoveryCode ?? string.Empty, _authOptions.Key);

		user!.SecuritySettings.MfaRecoveryCode = hashedRecoveryCode;
		var updateResult = await _userManager.UpdateAsync(user);
		if (!updateResult.Succeeded)
		{
			return null;
		}

		return recoveryCode;
	}

	public bool VerifyRecoveryCode(ApplicationUser user, string recoveryCode)
	{
		var hashedRecoveryCode = CommonFunction.ComputeSha256Hash(recoveryCode, _authOptions.Key);
		var storedRecoveryCode = user?.SecuritySettings?.MfaRecoveryCode ?? string.Empty;

		return hashedRecoveryCode == storedRecoveryCode;
	}

	public async Task<bool> VerifyUsernameAsync(string username, bool isMobile = false)
	{
		var contacts = await GetContactsByUsername(username);
		var contact = contacts?.FirstOrDefault();

		if (contact is null)
		{
			return false;
			// throw new AppException(AppError.Message.InvalidAccount, AppError.Reason.InvalidAccount);
		}

		var isPatient = !contact?.OrganizationalLevelCode?.Equals(Domain.Common.UserRole.Patient) ?? false;
		if (isMobile && isPatient)
		{
			return false;
			// throw new AppException(AppError.Message.ClinicianLoginOnMobile, AppError.Reason.InvalidAccount);
		}

		var contactInfos = await GetContactByCompanyNumber(contact?.CompanyNo ?? "");
		var contactInfo = contactInfos.FirstOrDefault();

		if (!(contactInfo is { PortalOptIn: PortalOptIn.Yes }) || contact is { PortalOptIn: PortalOptIn.No })
		{
			return false;
			// throw new AppException(AppError.Message.RestrictedClinicAccount);
		}

		return true;
	}

	public async Task<bool> VerifyUsernameAndDateOfBirth(string username, DateTime dateOfBirth)
	{
		var contacts = await GetContactByUsernameAndDob(username, dateOfBirth);
		var contact = contacts.FirstOrDefault();

		if (contact is null)
		{
			return false;
			// throw new AppException(AppError.Message.InvalidAccount, AppError.Reason.InvalidAccount);
		}

		var isExistingAccount = contact.PortalOptIn?.Equals(PortalOptIn.Yes) ?? false;
		if (isExistingAccount)
		{
			return false;
			// throw new AppException(AppError.Message.ExistingAccount, AppError.Reason.ExistingAccount);
		}

		if (string.IsNullOrEmpty(contact.Email))
		{
			return false;
			// throw new AppException(AppError.Message.EmailIsNotExisted);
		}

		var contactInfos = await _oDataClient.GetResponseAsync(ODataRequest<ContactDto>.Contact(
			filters: [ContactField.No.ToDynamicFilter(contact.CompanyNo, ODataOperator.EqualTo)]
		));
		var contactInfo = contactInfos.Data?.Items?.FirstOrDefault();
		if (contactInfo is { PortalOptIn: PortalOptIn.No })
		{
			return false;
			// throw new AppException(AppError.Message.RestrictedClinicAccount);
		}

		return true;
	}

	public async Task<bool> VerifyUsernameAndEmail(string username, string email)
	{
		var isUserNameAndEmailValid = await _webUtilitiesProxy.CheckContactNoAndEmailValidAsync(username, email);

		return isUserNameAndEmailValid.IsSuccess && isUserNameAndEmailValid.Data;
	}

	public async Task<ContactDto?> GetContactByUsername(string username)
	{
		var contacts = await GetContactsByUsername(username);
		return contacts?.FirstOrDefault();
	}

	public async Task<ApplicationUser?> GetUserFromSession(string encodedSession)
	{
		try
		{
			var (username, sessionId) = _loginService.GetSessionIdDecoded(encodedSession);
			var userSession = await _userLoginSessionRepository.FirstOrDefaultAsync(
				s => s.SessionId == sessionId && s.User.UserName == username,
				s => s.Include(u => u.User).ThenInclude(u => u.SecuritySettings)
			);

			return userSession?.User;
		}
		catch
		{
			return null;
		}
	}

	#region Private

	private async Task<IEnumerable<ContactDto>> GetContactsByUsername(string username) =>
		(await _oDataClient.GetResponseAsync(ODataRequest<ContactDto>.Contact(filters: GetContactFilter(username)))).Data?.Items ?? [];

	private static IEnumerable<DynamicFilter> GetContactFilter(string value) =>
		[ContactField.No.ToDynamicFilter(value, ODataOperator.EqualTo)];

	private async Task<IEnumerable<ContactDto>> GetContactListByUsernames(string usernames) =>
		(await _oDataClient.GetResponseAsync(ODataRequest<ContactDto>.Contact(filters: GetUsernameListFilter(usernames)))).Data?.Items ?? [];

	private static IEnumerable<DynamicFilter> GetUsernameListFilter(string usernames) =>
		[ContactListFields.No.ToDynamicFilter(usernames, ODataOperator.LogicalOr, subOperator: ODataOperator.EqualTo)];

	private async Task<IEnumerable<ContactDto>> GetContactByUsernameAndDob(string username, DateTime birthDate)
	{
		var response = await _oDataClient.GetResponseAsync(ODataRequest<ContactDto>.Contact(
			filters: [
				ContactField.No.ToDynamicFilter(username, ODataOperator.EqualTo),
				ContactField.DOB.ToDynamicFilter(birthDate.ToString("yyyy-MM-dd"), ODataOperator.Date, null, ODataOperator.EqualTo),
				ContactField.Type.ToDynamicFilter("Person", ODataOperator.EqualTo),
				ContactField.Status.ToDynamicFilter("Active", ODataOperator.EqualTo),
			]
		));

		return response.IsSuccess
			? response.Data?.Items ?? []
			: [];
	}

	private async Task<IEnumerable<ContactDto>> GetContactByCompanyNumber(string companyNo)
	{
		var response = await _oDataClient.GetResponseAsync(ODataRequest<ContactDto>.Contact(
			filters: [ContactField.No.ToDynamicFilter(companyNo, ODataOperator.EqualTo)]
		));

		return response.IsSuccess
			? response.Data?.Items ?? []
			: [];
	}

	private static ContactDto? VerifyContactInfo(LoginResultDto result, IEnumerable<ContactDto> contacts)
	{
		var contact = contacts.FirstOrDefault();
		if (contact is null)
		{
			result.Errors.Add(AppError.Message.LoginFail);
			return null;
		}

		if (contact.IsPatientType && (contact.Status?.Contains(DigitalTools.IntegrationNAV.Proxies.Features.OData.PatientStatus.Finished) ?? false))
		{
			result.Errors.Add(AppError.Message.InactivePatientAccount);
		}
		else if (!contact.IsPatientType && contact.Status != DigitalTools.IntegrationNAV.Proxies.Features.OData.PatientStatus.Active)
		{
			result.Errors.Add(AppError.Message.InactiveClinicianAccount);
		}

		return contact;
	}

	private async Task<TemplateResultDto> GetForgotUsernameEmailTemplate(IEnumerable<ContactDto> contacts, string? loginUrl = null)
	{
		var hasMultipleAccounts = contacts.Count() > 1;

		var firstContact = contacts.FirstOrDefault(x => !string.IsNullOrEmpty(x.FirstName)) ?? contacts.First();

		var templateName = hasMultipleAccounts
			? ForgotUsernameManyAccountsEmailTemplateName
			: ForgotUsernameSingleAccountEmailTemplateName;

		var userNameFormatted = $"{firstContact.No} / {firstContact.SearchName}";
		if (hasMultipleAccounts)
		{
			var multipleContactsUsernameTemplate =
			@"<p style=""font-size:14px;line-height:24px;margin-top:16px;margin-bottom:16px"">
				<a href=""{LOGIN_URL}"" style=""color:#003F6B;text-decoration-line:none;font-size:22px;text-align:center;font-weight:700;text-decoration:underline"" target=""_blank"">
					{USERNAME}
				</a>
			</p>";

			var template = new CommunicationTemplate()
			{
				Template = multipleContactsUsernameTemplate,
			};

			userNameFormatted = string.Join("", contacts.Select(contact =>
			{
				var usernameLoginUrl = QueryHelpers.AddQueryString(loginUrl ?? "", "ctNumber", contact.No);
				return _templateBuilder.RenderTemplateContent(template, new Dictionary<string, string>()
				{
					{"{USERNAME}", $"{contact.No} / {contact.SearchName}"},
					{"{LOGIN_URL}", usernameLoginUrl ?? ""},
				});
			}));
		}
		else
		{
			loginUrl = QueryHelpers.AddQueryString(loginUrl ?? "", "ctNumber", firstContact.No);
		}

		var request = _httpContextAccessor.HttpContext?.Request;
		var baseUrl = $"{request?.Scheme}://{request?.Host}";
		var emailTemplate = await _templateBuilder.BuildTemplateAsync(templateName, new Dictionary<string, string>()
		{
			{"{FIRST_NAME}", firstContact.FirstName ?? ""},
			{"{LOGIN_URL}", loginUrl ?? "" },
			{"{LOGO_URL}", $"{baseUrl}/images/logo.svg"},
			{"{ILLUSTRATION_URL}", $"{baseUrl}/images/email-illustration.png"},
			{"{USERNAMES}", userNameFormatted},
		});

		return emailTemplate;
	}

	private static List<List<string>> SplitUsernamesIntoChunks(IEnumerable<string> usernames, long chunkSize)
	{
		var result = new List<List<string>>();
		var chunk = new List<string>();

		foreach (var item in usernames)
		{
			chunk.Add(item);
			if (chunk.Count == chunkSize)
			{
				result.Add(chunk);
				chunk = [];
			}
		}

		if (chunk.Count > 0)
		{
			result.Add(chunk);
		}

		return result;
	}

	private async Task SyncUserAsync(ApplicationUser user, ContactDto contact)
	{
		if (user is null || contact is null || contact.EqualsUser(user)) return;

		user.Firstname = contact.FirstName;
		user.Surname = contact.Surname;
		user.Email = contact.Email;
		user.PhoneNumber = contact.MobilePhoneNo;
		user.IsPatient = contact.IsPatientType;
		user.PortalOptIn = contact.PortalOptIn;
		user.Status = contact.Status;

		var updateResult = await _userManager.UpdateAsync(user);
		if (!updateResult.Succeeded)
		{
			_logger.LogWarning(
				"Failed to update user information for username: {Username} and email: {Email}.",
				user.UserName, user.Email
			);
		}
	}

	#endregion
}
