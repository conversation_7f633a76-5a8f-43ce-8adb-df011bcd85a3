namespace HealthNet.Homecare.IdentityServer.Infrastructure.Configurations;

public class CaptchaServiceOptions
{
    public bool IsVerifyCaptcha { get; set; }
    public CaptchaOption TurnstileCaptcha { get; set; } = default!;
    public TimeSpan CaptchaTokenLifetime { get; set; }

    public const string SectionName = "CaptchaService";
}

public class CaptchaOption
{
    public string Url { get; set; } = string.Empty;
    public string SecretKey { get; set; } = string.Empty;
    public string SiteKey { get; set; } = string.Empty;
}
