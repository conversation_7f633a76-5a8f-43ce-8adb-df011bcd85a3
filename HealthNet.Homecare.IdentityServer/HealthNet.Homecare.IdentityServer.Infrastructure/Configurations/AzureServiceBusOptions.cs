namespace HealthNet.Homecare.IdentityServer.Infrastructure.Configurations;

public class AzureServiceBusOptions
{
	public const string SectionName = "AzureServiceBus";
	public string ConnectionString { get; set; } = string.Empty;
	public string UserDataManagementQueue { get; set; } = string.Empty;
	public string IdentityServerQueue { get; set; } = string.Empty;
	public int RetryAttempts { get; set; } = 0;
}
