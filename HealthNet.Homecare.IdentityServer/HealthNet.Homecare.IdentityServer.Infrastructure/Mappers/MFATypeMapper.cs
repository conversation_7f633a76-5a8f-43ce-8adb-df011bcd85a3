using HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.Communication.Dto;
using HealthNet.Homecare.IdentityServer.Domain.Enums;

namespace HealthNet.Homecare.IdentityServer.Infrastructure.Mappers;

public static class MfaTypeMapper
{
	public static MessageSendType? ToMessageSendType(MfaType mfaType)
	{
		return mfaType switch
		{
			MfaType.Email => MessageSendType.Email,
			MfaType.Phone => MessageSendType.Phone,
			MfaType.WhatsApp => MessageSendType.WhatsApp,
			_ => null
		};
	}
}
