using System.Text.Json.Serialization;

namespace HealthNet.Homecare.IdentityServer.Abstractions.DTO.Contact;

public class ContactHospitalRelationDto
{
	public string? No { get; set; }

	[JsonPropertyName("Company_Hospital_Name")]
	public string? CompanyHospitalName { get; set; }

	[JsonPropertyName("Company_Contact_No")]
	public string? CompanyContactNo { get; set; }

	public string? Hospital { get; set; }

	[JsonPropertyName("User_Type")]
	public string? UserType { get; set; }

	[JsonPropertyName("Adhere_Predict")]
	public bool? AdherePredict { get; set; }
}
