using HealthNet.Homecare.IdentityServer.Abstractions.Enums;
using HealthNet.Homecare.IdentityServer.Abstractions.Extensions;
using HealthNet.Homecare.IdentityServer.Domain.Common;
using System.Text.Json.Serialization;

namespace HealthNet.Homecare.IdentityServer.Abstractions.DTO.Contact;

public class ContactDto
{
	public string No { get; set; }
	[JsonPropertyName("Organizational_Level_Code")]
	public string? OrganizationalLevelCode { get; set; }

	public bool IsPatientType => OrganizationalLevelCode?.ToUpper().Contains(UserRole.Patient) ?? false;

	[JsonPropertyName("Company_No")]
	public string? CompanyNo { get; set; }

	[JsonPropertyName("First_Name")]
	public string? FirstName { get; set; }
	public string? Surname { get; set; }
	public string? Status { get; set; }
	public string SearchName => $"{FirstName} {Surname}".ToUpper();

	public int StatusValue => (int)(Status?.GetValueFromName<ContactStatusEnum>() ?? ContactStatusEnum.On_Hold_Other);

	[JsonPropertyName("Portal_Opt_In")]
	public string? PortalOptIn { get; set; }

	[JsonPropertyName("Mobile_Phone_No")]
	public string? MobilePhoneNo { get; set; }

	[JsonPropertyName("E_Mail")]
	public string? Email { get; set; }
}
