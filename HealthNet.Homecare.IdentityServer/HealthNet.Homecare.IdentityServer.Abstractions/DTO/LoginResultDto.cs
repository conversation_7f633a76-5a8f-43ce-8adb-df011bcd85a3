namespace HealthNet.Homecare.IdentityServer.Abstractions.DTO;

public class LoginResultDto
{
	public string Id { get; set; } = null!;
	public string? Email { get; set; }
	public string? UserName { get; set; }
	public string? DisplayName { get; set; }
	public Guid SessionId { get; set; }
	public bool IsLogged { get; set; }
	public string SendOtpMethod { get; set; } = string.Empty;
	public bool IsKeptLogin { get; set; }
	public bool IsTwoFactorRequired { get; set; }
	public bool IsSetupAccountRequired { get; set; }
	public bool IsAdherePredictAdmin { get; set; } = false;
	public List<string> CompanyHospitals { get; set; } = [];
	public List<string> Errors { get; set; } = [];
}
