using System.Text.Json.Serialization;

namespace HealthNet.Homecare.IdentityServer.Abstractions.DTO.API;

public class IpApiResponseDto
{
	[JsonPropertyName("status")]
	public string? Status { get; init; }

	[JsonPropertyName("message")]
	public string? Message { get; init; }

	[JsonPropertyName("country")]
	public string? Country { get; init; }

	[JsonPropertyName("countryCode")]
	public string? CountryCode { get; init; }

	[JsonPropertyName("region")]
	public string? Region { get; init; }

	[JsonPropertyName("regionName")]
	public string? RegionName { get; init; }

	[JsonPropertyName("city")]
	public string? City { get; init; }

	[JsonPropertyName("zip")]
	public string? Zip { get; init; }
}
