using System.ComponentModel.DataAnnotations;

namespace HealthNet.Homecare.IdentityServer.Abstractions.Enums
{
	public enum ContactStatusEnum
	{
		[Display(Name = "Active")]
		Active,
		[Display(Name = "On Hold")]
		On_Hold,
		[Display(Name = "Finished Treatment Complete")]
		Finished_Treatment_Complete,
		[Display(Name = "Finished Change Of Treatment")]
		Finished_Change_Of_Treatment,
		[Display(Name = "Finished Change Of Homecare Provider")]
		Finished_Change_Of_Homecare_Provider,
		[Display(Name = "Finished Deceased")]
		Finished_Deceased,
		[Display(Name = "Finished Adverse Effect")]
		Finished_Adverse_Effect,
		[Display(Name = "Finished Patient Moved Centre")]
		Finished_Patient_Moved_Centre,
		[Display(Name = "On Hold - Awaiting script")]
		On_Hold_Awaiting_script,

		[Display(Name = "On Hold - Funding")]
		On_Hold_Funding,

		[Display(Name = "On Hold - Hospital request")]
		On_Hold_Hospital_request,

		[Display(Name = "On Hold - Initial Registration")]
		On_Hold_Initial_Registration,

		[Display(Name = "On Hold - No Contact")]
		On_Hold_No_Contact,

		[Display(Name = "On Hold - Patient request")]
		On_Hold_Patient_request,

		[Display(Name = "On Hold - Other")]
		On_Hold_Other,

		[Display(Name = "Finished - Dormant Account")]
		Finished_Dormant_Account,
	}
}
