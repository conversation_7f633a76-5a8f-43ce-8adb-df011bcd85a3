using HealthNet.Homecare.IdentityServer.Abstractions.DTO.Contact;
using HealthNet.Homecare.IdentityServer.Domain.Entities;

namespace HealthNet.Homecare.IdentityServer.Abstractions.Extensions;

public static class ContactDtoExtensions
{
	public static bool EqualsUser(this ContactDto contact, ApplicationUser user)
	{
		return string.Equals(user.Firstname, contact.FirstName, StringComparison.Ordinal) &&
			   string.Equals(user.Surname, contact.Surname, StringComparison.Ordinal) &&
			   string.Equals(user.Email, contact.Email, StringComparison.Ordinal) &&
			   string.Equals(user.PhoneNumber, contact.MobilePhoneNo, StringComparison.Ordinal) &&
			   string.Equals(user.PortalOptIn, contact.PortalOptIn, StringComparison.Ordinal) &&
			   string.Equals(user.Status, contact.Status, StringComparison.Ordinal) &&
			   user.IsPatient == contact.IsPatientType;
	}
}
