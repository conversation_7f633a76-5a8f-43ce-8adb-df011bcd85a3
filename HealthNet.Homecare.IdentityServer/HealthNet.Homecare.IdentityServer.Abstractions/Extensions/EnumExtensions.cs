using System.ComponentModel.DataAnnotations;

namespace HealthNet.Homecare.IdentityServer.Abstractions.Extensions
{
	public static class EnumExtensions
	{
		public static T GetValueFromName<T>(this string name) where T : Enum
		{
			var type = typeof(T);

			foreach (var field in type.GetFields())
			{
				if (Attribute.GetCustomAttribute(field, typeof(DisplayAttribute)) is DisplayAttribute attribute && attribute.Name.Equals(name))
				{
					return (T)field.GetValue(null);

				}

				if (field.Name == name)
				{
					return (T)field.GetValue(null);
				}
			}

			return default;
		}
	}
}
