using System.Linq.Expressions;

namespace HealthNet.Homecare.IdentityServer.Abstractions.EntityFramework.Repositories;

public interface IGenericRepository<TEntity, TId> where TEntity : class
{
	#region ADD
	Task AddAsync(TEntity entity);
	void Add(TEntity entity);
	Task AddRangeAsync(IEnumerable<TEntity> entities);
	void AddRange(IEnumerable<TEntity> entities);
	#endregion

	#region DELETE
	void Delete(TEntity entity);
	void DeleteRange(IEnumerable<TEntity> entities);
	#endregion

	#region GET
	Task<TEntity> FirstAsync(Expression<Func<TEntity, bool>> predicate);
	Task<TEntity?> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, params Expression<Func<TEntity, object>>[] includes);
	Task<TEntity?> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, Func<IQueryable<TEntity>, IQueryable<TEntity>>? include = null);
	Task<TEntity> SingleAsync(Expression<Func<TEntity, bool>> predicate);
	Task<TEntity?> SingleOrDefaultAsync(Expression<Func<TEntity, bool>> predicate);
	TEntity? SingleOrDefault(Expression<Func<TEntity, bool>> predicate);
	Task<IReadOnlyList<TEntity>> ListAsync(Expression<Func<TEntity, bool>> predicate);
	Task<TEntity> FindAsync(object[] keyValues);
	Task<IReadOnlyList<TEntity>> ListAllAsync();
	#endregion

	void Update(TEntity entity);
	void UpdateRange(IEnumerable<TEntity> entities);
	Task<bool> SaveChangesAsync();
}
