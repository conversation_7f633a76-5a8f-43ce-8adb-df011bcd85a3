using HealthNet.Homecare.IdentityServer.Domain.Common;
using HealthNet.Homecare.IdentityServer.Infrastructure.Services;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Reflection;

namespace HealthNet.Homecare.IdentityServer.Attributes;

public class ValidateCaptchaPageFilter(CaptchaService captchaService) : IAsyncPageFilter
{
	private const string CaptchaTokenField = "Input.CaptchaToken";

	private readonly CaptchaService _captchaService = captchaService;

	public Task OnPageHandlerSelectionAsync(PageHandlerSelectedContext context) => Task.CompletedTask;

	public async Task OnPageHandlerExecutionAsync(PageHandlerExecutingContext context, PageHandlerExecutionDelegate next)
	{
		var validateCaptchaAttributes = context.HandlerMethod?.MethodInfo.GetCustomAttributes<ValidateCaptchaAttribute>();
		var hasValidateCaptchaAttributes = validateCaptchaAttributes is not null && validateCaptchaAttributes.Any();

		if (!hasValidateCaptchaAttributes)
		{
			await next();
			return;
		}

		string? captchaToken = null;
		if (context.HttpContext.Request.HasFormContentType &&
			context.HttpContext.Request.Form.TryGetValue(CaptchaTokenField, out var formValue))
		{
			captchaToken = formValue.ToString();
		}

		if (string.IsNullOrWhiteSpace(captchaToken) || !await _captchaService.VerifyToken(captchaToken))
		{
			context.ModelState.AddModelError(string.Empty, AppError.Message.InvalidCaptcha);
		}

		await next();
	}
}