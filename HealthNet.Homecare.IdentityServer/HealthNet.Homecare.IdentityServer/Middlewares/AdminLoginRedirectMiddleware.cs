using HealthNet.Homecare.IdentityServer.Constants;
using Microsoft.AspNetCore.Authorization;

namespace HealthNet.Homecare.IdentityServer.Middlewares;

public class AdminLoginRedirectMiddleware(RequestDelegate next)
{
    private readonly RequestDelegate _next = next;

    public async Task InvokeAsync(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLower();

        if (path is null)
        {
            await _next(context);
            return;
        }

        var isAdminPath = AdminContants.AdminFolders.Any(folder => path.StartsWith(folder.ToLower()));
        var isAdminLoginPage = path.StartsWith(AdminContants.LoginPage.ToLower());

        if (isAdminPath && !isAdminLoginPage)
        {
            var endpoint = context.GetEndpoint();
            var requiresAuth = endpoint?.Metadata.GetMetadata<AuthorizeAttribute>() is not null;
            var isAuthenticated = context.User.Identity?.IsAuthenticated == true;

            if (requiresAuth && !isAuthenticated)
            {
                context.Response.Redirect(AdminContants.LoginPage);
                return;
            }
        }

        await _next(context);
    }
}
