using Duende.IdentityServer.Models;
using HealthNet.Homecare.IdentityServer.Abstractions.Services;
using HealthNet.Homecare.IdentityServer.Configurations;
using HealthNet.Homecare.IdentityServer.Domain.Common;
using Microsoft.Extensions.Options;

namespace HealthNet.Homecare.IdentityServer.Middlewares;

public class ExceptionHandlerMiddleware(
    RequestDelegate next,
    IErrorStore errorStore,
    IOptions<ErrorOptions> errorOptions
)
{
    private readonly RequestDelegate _next = next;
    private readonly IErrorStore _errorStore = errorStore;
    private readonly ErrorOptions _errorOptions = errorOptions.Value;

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (AppException ex)
        {
            await HandleExceptionAsync(context, ex, "app_exception");
        }
        catch (GenericException ex)
        {
            await HandleExceptionAsync(context, ex, "generic_exception");
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex, "unhandled_exception");
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception ex, string errorCode)
    {
        var errorMessage = new ErrorMessage
        {
            Error = errorCode,
            ErrorDescription = _errorOptions.ShowErrorDescription ? ex.ToString() : null
        };

        var errorId = await _errorStore.StoreAsync(errorMessage);

        context.Response.Redirect($"/home/<USER>");
    }
}
