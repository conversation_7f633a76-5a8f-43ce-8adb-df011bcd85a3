namespace HealthNet.Homecare.IdentityServer.Middlewares
{
	using HealthNet.Homecare.IdentityServer.CSP;
	using Microsoft.AspNetCore.Http;
	using Microsoft.Extensions.Hosting;
	using System.Text;
	using System.Threading.Tasks;

	public class CspHeaderMiddleware
	{
		private readonly RequestDelegate _next;
		private readonly IHostEnvironment _env;

		public CspHeaderMiddleware(RequestDelegate next, IHostEnvironment env)
		{
			_next = next;
			_env = env;
		}

		public async Task Invoke(HttpContext context, ICspNonceService nonceService)
		{
			var nonce = nonceService.Nonce;

			var csp = new StringBuilder();
			csp.Append("default-src 'self'; ");
			csp.Append($"script-src 'self' 'nonce-{nonce}' https://challenges.cloudflare.com https://*.cloudflare.com; ");
			csp.Append("style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; ");
			csp.Append("font-src 'self' https://fonts.gstatic.com; ");
			csp.Append("img-src 'self' data: https://*.cloudflare.com; ");
			csp.Append("frame-src https://challenges.cloudflare.com; ");
			csp.Append("object-src 'none'; ");
			csp.Append("base-uri 'self'; ");
			csp.Append("frame-ancestors 'none'; ");

			var currentHost = context.Request.Host.Value;
			var connectSources = new List<string> {
				"'self'",
				"https://*.cloudflare.com"
			};

			if (_env.IsDevelopment())
			{
				connectSources.Add("http://localhost:*");
				connectSources.Add("ws://localhost:*");
				connectSources.Add("wss://localhost:*");
			}
			else
			{
				connectSources.Add($"https://{currentHost}");
				connectSources.Add($"wss://{currentHost}");
				connectSources.Add($"ws://{currentHost}");
			}

			csp.Append($"connect-src {string.Join(" ", connectSources)}; ");

			var cspHeader = csp.ToString().Replace("\r", "").Replace("\n", " ");
			context.Response.Headers["Content-Security-Policy"] = cspHeader;
			context.Response.Headers["X-Content-Security-Policy"] = cspHeader;

			context.Response.Headers.TryAdd("X-Content-Type-Options", "nosniff");
			context.Response.Headers.TryAdd("X-Frame-Options", "DENY");
			context.Response.Headers.TryAdd("Referrer-Policy", "no-referrer");

			await _next(context);
		}
	}
}