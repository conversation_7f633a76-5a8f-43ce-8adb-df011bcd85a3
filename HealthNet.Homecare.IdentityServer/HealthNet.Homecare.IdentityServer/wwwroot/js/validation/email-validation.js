$(document).ready(function () {
    const $emailInputs = $('#email');
    const emailRegex = window.HealthNetUtils.EMAIL_REGEX;

    function validateEmail($input, showError = false) {
        const value = $input.val().trim();
        const $validationMessage = $input.closest('.form-group').find('.field-validation-error');
        const $validationSummary = $input.closest('form').find('.validation-summary-errors');
        const isLoginPage = $input.closest('form').hasClass('login-form');

        if (!value) {
            $input.removeClass('valid invalid');
            if (showError) {
                $validationMessage.text('').hide();
                if (!isLoginPage) {
                    $validationSummary.hide();
                }
            }
            return false;
        }

        if (emailRegex.test(value)) {
            $input.removeClass('invalid').addClass('valid');
            if (showError) {
                $validationMessage.text('').hide();
                if (!isLoginPage) {
                    $validationSummary.hide();
                }
            }
            return true;
        } else {
            $input.removeClass('valid').addClass('invalid');
            if (showError) {
                $validationMessage.text('Incorrect format. Please enter a valid email address.').show();
                if (!isLoginPage) {
                    $validationSummary.hide();
                }
            }
            return false;
        }
    }

    // Clear error on input (don't show validation errors while typing)
    $emailInputs.on('input', function () {
        const $input = $(this);
        const $validationMessage = $input.closest('.form-group').find('.field-validation-error');
        const $validationSummary = $input.closest('form').find('.validation-summary-errors');
        const isLoginPage = $input.closest('form').hasClass('login-form');
        $input.removeClass('invalid');
        $validationMessage.text('').hide();
        if (!isLoginPage) {
            $validationSummary.hide();
        }
    });

    // Validate on blur (show errors)
    $emailInputs.on('blur', function () {
        validateEmail($(this), true);
    });

    // Prevent form submission if email is invalid (show errors on submit)
    $emailInputs.closest('form').on('submit', function (e) {
        const $form = $(this);
        const $emailInput = $form.find('#email');
        const $validationSummary = $form.find('.validation-summary-errors');

        if ($emailInput.length > 0) {
            const isValid = validateEmail($emailInput, true);
            if (!isValid) {
                // Hide validation summary when field validation error is shown
                const $fieldValidationMessage = $emailInput.closest('.form-group').find('.field-validation-error');
                const isLoginPage = $form.hasClass('login-form');
                if ($fieldValidationMessage.is(':visible') && $fieldValidationMessage.text().trim() !== '' && !isLoginPage) {
                    $validationSummary.hide();
                }
                e.preventDefault();
                return false;
            }
        }
    });

    // Initial validation if field has value (don't show errors initially)
    $emailInputs.each(function () {
        const $input = $(this);
        if ($input.val()) {
            validateEmail($input, false);
        }
    });
});
