$(document).ready(function () {
    const $ctNumberInputs = $('.ct-number-input');
    const ctNumberRegex = window.HealthNetUtils.CT_NUMBER_REGEX;

    function validateCTNumber($input, showError = false) {
        const value = $input.val().trim();
        const $validationMessage = $input.closest('.form-group').find('.field-validation-error');
        const $validationSummary = $input.closest('form').find('.validation-summary-errors');
        const isLoginPage = $input.closest('form').hasClass('login-form');

        if (!value) {
            $input.removeClass('valid invalid');
            if (showError) {
                $validationMessage.text('').hide();
                if (!isLoginPage) {
                    $validationSummary.hide();
                }
            }
            return false;
        }

        if (ctNumberRegex.test(value)) {
            $input.removeClass('invalid').addClass('valid');
            if (showError) {
                $validationMessage.text('').hide();
                if (!isLoginPage) {
                    $validationSummary.hide();
                }
            }
            return true;
        } else {
            $input.removeClass('valid').addClass('invalid');
            if (showError) {
                $validationMessage.text('Incorrect format. It should start with the letters “CT” followed by 6 digits.').show();
                if (!isLoginPage) {
                    $validationSummary.hide();
                }
            }
            return false;
        }
    }

    // Clear error on input (don't show validation errors while typing)
    $ctNumberInputs.on('input', function () {
        const $input = $(this);
        const $validationMessage = $input.closest('.form-group').find('.field-validation-error');
        const $validationSummary = $input.closest('form').find('.validation-summary-errors');
        const isLoginPage = $input.closest('form').hasClass('login-form');
        $input.removeClass('invalid');
        $validationMessage.text('').hide();
        if (!isLoginPage) {
            $validationSummary.hide();
        }
    });

    // Validate on blur (show errors)
    $ctNumberInputs.on('blur', function () {
        validateCTNumber($(this), true);
    });

    // Prevent form submission if CT number is invalid (show errors on submit)
    $ctNumberInputs.closest('form').on('submit', function (e) {
        const $form = $(this);
        const $ctInput = $form.find('.ct-number-input');
        const $validationSummary = $form.find('.validation-summary-errors');

        if ($ctInput.length > 0) {
            const isValid = validateCTNumber($ctInput, true);
            if (!isValid) {
                // Hide validation summary when field validation error is shown
                const $fieldValidationMessage = $ctInput.closest('.form-group').find('.field-validation-error');
                const isLoginPage = $form.hasClass('login-form');
                if ($fieldValidationMessage.is(':visible') && $fieldValidationMessage.text().trim() !== '' && !isLoginPage) {
                    $validationSummary.hide();
                }
                e.preventDefault();
                return false;
            }
        }
    });

    // Initial validation if field has value (don't show errors initially)
    $ctNumberInputs.each(function () {
        const $input = $(this);
        if ($input.val()) {
            validateCTNumber($input, false);
        }
    });
});