$(document).ready(function () {
    const $passwordInput = $('#newPassword');
    const $confirmInput = $('#confirmPassword');
    const isPatient = $('#IsPatient').val() === 'True';
    const requiredLength = isPatient ? 8 : 12;

    if (!$passwordInput.length || !$confirmInput.length) return;

    const requirements = [
        { id: 'length-check', test: p => p.length >= requiredLength },
        { id: 'uppercase-check', test: p => /[A-Z]/.test(p) },
        { id: 'lowercase-check', test: p => /[a-z]/.test(p) },
        { id: 'number-check', test: p => /[0-9]/.test(p) },
        { id: 'special-check', test: p => /[@#$%^&*!]/.test(p) }
    ];

    // Update the length requirement text based on isPatient
    const $lengthCheckLi = $('#length-check');
    if ($lengthCheckLi.length) {
        const lengthText = isPatient ? 'Must be at least 8 characters long' : 'Must be at least 12 characters long';
        $lengthCheckLi.contents().filter(function () {
            return this.nodeType === 3;
        }).replaceWith(' ' + lengthText);
    }

    function validatePassword(showErrors = false) {
        const pwd = $passwordInput.val();
        let isValid = true;

        // Reset validation state
        $passwordInput.removeClass('error valid');
        $passwordInput.closest('.form-group').find('.field-validation-error').text('').hide();

        requirements.forEach(r => {
            const $li = $(`#${r.id}`);
            if ($li.length) {
                const $icon = $li.find('img.checkmark');
                if (r.test(pwd)) {
                    $li.addClass('valid');
                    $icon.attr('src', '/images/check-green.svg');
                } else {
                    $li.removeClass('valid');
                    $icon.attr('src', '/images/check.svg');
                    isValid = false;
                }
            }
        });

        // Show validation state if user has started typing or on blur
        if (pwd || showErrors) {
            if (!pwd) {
                $passwordInput.addClass('error');
                $passwordInput.closest('.form-group').find('.field-validation-error')
                    .text('Password is required')
                    .show();
            } else if (!isValid && showErrors) {
                $passwordInput.addClass('error');
                $passwordInput.closest('.form-group').find('.field-validation-error')
                    .text('Please meet all password requirements')
                    .show();
            } else if (isValid) {
                $passwordInput.addClass('valid');
            }
        }

        validateMatch(showErrors);
        return isValid;
    }

    function validateMatch(showErrors = false) {
        const pwd = $passwordInput.val();
        const confirmPwd = $confirmInput.val();

        // Reset validation state
        $confirmInput.removeClass('error valid');
        $confirmInput.closest('.form-group').find('.field-validation-error').text('').hide();

        if (confirmPwd || showErrors) {
            if (!confirmPwd) {
                $confirmInput.addClass('error');
                $confirmInput.closest('.form-group').find('.field-validation-error')
                    .text('Confirm password is required')
                    .show();
                return false;
            } else if (pwd !== confirmPwd) {
                $confirmInput.addClass('error');
                $confirmInput.closest('.form-group').find('.field-validation-error')
                    .text('Passwords do not match')
                    .show();
                return false;
            } else {
                $confirmInput.addClass('valid');
                return true;
            }
        }
        return pwd === confirmPwd;
    }

    function checkAllRequirements() {
        const pwd = $passwordInput.val();
        return requirements.every(r => r.test(pwd));
    }

    // Bind events
    $passwordInput.on('input', () => validatePassword(false));
    $passwordInput.on('blur', () => validatePassword(true));
    $confirmInput.on('input', () => validateMatch(false));
    $confirmInput.on('blur', () => validateMatch(true));

    // Form submission handling
    $('form').on('submit', function (e) {
        const passwordValid = validatePassword(true);
        const matchValid = validateMatch(true);

        if (!passwordValid || !matchValid) {
            e.preventDefault();
        }
    });
}); 