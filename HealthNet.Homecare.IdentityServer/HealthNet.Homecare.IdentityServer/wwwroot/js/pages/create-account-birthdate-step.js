$(document).ready(function () {
    const $birthDay = $('#birthDay');
    const $birthMonth = $('#birthMonth');
    const $birthYear = $('#birthYear');
    const $form = $('form');

    function showError(message) {
        const $validationSummary = $('.validation-summary-errors');
        const $ul = $validationSummary.find('ul');

        // Clear existing errors
        $ul.empty();

        // Add new error
        $ul.append(`<li>${message}</li>`);

        // Show the validation summary
        $validationSummary.show();
    }

    function clearErrors() {
        const $validationSummary = $('.validation-summary-errors');
        $validationSummary.hide();
        $validationSummary.find('ul').empty();
    }

    function validateDate() {
        const day = parseInt($birthDay.val());
        const month = parseInt($birthMonth.val());
        const year = parseInt($birthYear.val());

        // Check if all fields are filled
        if (!$birthDay.val() || !$birthMonth.val() || !$birthYear.val()) {
            showError('Fields required');
            return false;
        }

        // Create date object and check if it's valid
        const date = new Date(year, month - 1, day);
        const isValid = date.getDate() === day &&
            date.getMonth() === month - 1 &&
            date.getFullYear() === year;

        // Check if date is in the past
        const isPast = date < new Date();

        // Check if year is within valid range
        const currentYear = new Date().getFullYear();
        const isYearValid = year >= 1900 && year <= currentYear;

        if (!isValid || !isPast || !isYearValid) {
            showError('Please enter your date of birth in the correct format e.g 01012020');
            return false;
        }

        clearErrors();
        return true;
    }

    function validateNumberInput(e, maxValue, maxLength) {
        // Allow: backspace, delete, tab, escape, enter
        if ([8, 9, 13, 27, 46].indexOf(e.keyCode) !== -1) {
            return true;
        }

        // Allow only numbers
        if (!/^\d$/.test(e.key)) {
            e.preventDefault();
            return false;
        }

        const $input = $(e.target);
        const currentValue = $input.val();
        const newValue = currentValue + e.key;

        // Check if adding this digit would exceed maxLength
        if (newValue.length > maxLength) {
            e.preventDefault();
            return false;
        }

        // Check if the new value would exceed maxValue
        if (parseInt(newValue) > maxValue) {
            e.preventDefault();
            return false;
        }

        return true;
    }

    // Add keypress validation
    $birthDay.on('keypress', function (e) {
        validateNumberInput(e, 31, 2);
    });

    $birthMonth.on('keypress', function (e) {
        validateNumberInput(e, 12, 2);
    });

    $birthYear.on('keypress', function (e) {
        const currentYear = new Date().getFullYear();
        validateNumberInput(e, currentYear, 4);
    });

    // Add input event listeners to all date fields
    $birthDay.add($birthMonth).add($birthYear).on('input', function () {
        const $this = $(this);
        const currentValue = $this.val().replace(/[^0-9]/g, '');

        // Ensure only numbers are entered
        $this.val(currentValue);

        // Apply max length restrictions and handle auto-focus
        if ($this.attr('id') === 'birthDay') {
            if (currentValue.length > 2) {
                $this.val(currentValue.slice(0, 2));
            } else if (currentValue.length === 2 && parseInt(currentValue) <= 31) {
                $birthMonth.focus();
            }
        } else if ($this.attr('id') === 'birthMonth') {
            if (currentValue.length > 2) {
                $this.val(currentValue.slice(0, 2));
            } else if (currentValue.length === 2 && parseInt(currentValue) <= 12) {
                $birthYear.focus();
            }
        } else if ($this.attr('id') === 'birthYear' && currentValue.length > 4) {
            $this.val(currentValue.slice(0, 4));
        }

        // Validate ranges
        if ($this.attr('id') === 'birthDay' && parseInt($this.val()) > 31) {
            $this.val(31);
        } else if ($this.attr('id') === 'birthMonth' && parseInt($this.val()) > 12) {
            $this.val(12);
        }

        // Clear errors when user starts typing
        clearErrors();
    });

    // Add form submit validation
    $form.on('submit', function (e) {
        if (!validateDate()) {
            e.preventDefault();
        }
    });
}); 