$(document).ready(function () {
    const $radioButtons = $('input[name="Step7.AuthMethod"]');
    const $inputContainers = $('.email-input-container, .sms-input-container, .whatsapp-input-container');
    const $emailInput = $('#emailInput');
    const $phoneInput = $('#phoneInput');
    const $whatsappInput = $('#whatsappInput');
    const $emailValidationMessage = $('[data-valmsg-for="Step7.Email"]');
    const $phoneValidationMessage = $('[data-valmsg-for="Step7.Phone"]');
    const $whatsappValidationMessage = $('[data-valmsg-for="Step7.WhatsApp"]');
    const $submitButton = $('.login-button');
    const emailRegex = window.HealthNetUtils.EMAIL_REGEX;
    const phoneRegex = window.HealthNetUtils.PHONE_REGEX;

    // Disable submit button by default
    $submitButton.prop('disabled', true);

    function validateInput($input, regex, $validationMessage, errorMessage) {
        const value = $input.val();

        if (!value) {
            $input.removeClass('valid invalid');
            $validationMessage.text('').hide();
            return false;
        }

        if (regex.test(value)) {
            $input.removeClass('invalid').addClass('valid');
            $validationMessage.text('').hide();
            return true;
        } else {
            $input.removeClass('valid').addClass('invalid');
            $validationMessage.text(errorMessage).show();
            return false;
        }
    }

    function validateEmail() {
        if ($emailInput.val().trim()) {
            return validateInput(
                $emailInput,
                emailRegex,
                $emailValidationMessage,
                'Incorrect format. Please enter a valid email address.'
            );
        }
        return false;
    }

    function validatePhone($input, $validationMessage) {
        if ($input.val().trim()) {
            return validateInput(
                $input,
                phoneRegex,
                $validationMessage,
                'Incorrect format. Please enter a valid UK phone number (e.g. +44 20 7123 4567 or 07123 456789)'
            );
        }
        return false;
    }

    function showSelectedMethod(method) {
        $inputContainers.removeClass('active').hide();
        if (method) {
            const $selectedContainer = $(`.${method}-input-container`);
            $selectedContainer.addClass('active').show();

            // Handle input requirements
            $inputContainers.find('input').prop('required', false);
            const $input = $selectedContainer.find('input');

            // Reset validation state
            $input.removeClass('valid invalid');
            $('[data-valmsg-for]').text('').hide();

            // Enable submit button when a method is selected
            $submitButton.prop('disabled', false);
        } else {
            // No method selected, all inputs should be hidden and not required
            $inputContainers.find('input').prop('required', false);
            // Disable submit button when no method is selected
            $submitButton.prop('disabled', true);
        }
    }

    // Show initial selected method if any
    const $checkedRadio = $radioButtons.filter(':checked');
    showSelectedMethod($checkedRadio.length ? $checkedRadio.val() : null);

    // Handle radio button changes
    $radioButtons.on('change', function () {
        showSelectedMethod($(this).val());
    });

    // Add validation only on blur
    $emailInput.on('blur', function () {
        if ($(this).val().trim()) {
            validateEmail();
        }
    });

    $phoneInput.on('blur', function () {
        if ($(this).val().trim()) {
            validatePhone($phoneInput, $phoneValidationMessage);
        }
    });

    $whatsappInput.on('blur', function () {
        if ($(this).val().trim()) {
            validatePhone($whatsappInput, $whatsappValidationMessage);
        }
    });

    // Handle form validation
    $('form').on('submit', function (e) {
        const selectedMethod = $('input[name="Step7.AuthMethod"]:checked').val();
        if (!selectedMethod) {
            e.preventDefault();
            return;
        }

        const $input = $(`.${selectedMethod}-input-container input`);
        if (!$input.length || !$input.val().trim()) {
            e.preventDefault();
            $input.focus();
            return;
        }

        // Additional validation based on method
        let isValid = false;
        if (selectedMethod === 'email') {
            isValid = validateEmail();
        } else if (selectedMethod === 'sms') {
            isValid = validatePhone($phoneInput, $phoneValidationMessage);
        } else if (selectedMethod === 'whatsapp') {
            isValid = validatePhone($whatsappInput, $whatsappValidationMessage);
        }

        if (!isValid) {
            e.preventDefault();
            $(`.${selectedMethod}-input-container input`).focus();
        }
    });
});