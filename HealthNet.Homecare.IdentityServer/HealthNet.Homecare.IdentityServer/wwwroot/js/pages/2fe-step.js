$(document).ready(function () {
    var $inputs = $('#code-inputs .verification-code-input');
    var $hidden = $('#inputCode');
    var $submitButton = $('button[type="submit"]:not([skip-validation])');
    var $codeRow = $('#code-inputs');
    var $form = $('.verification-code-form');

    function updateHidden() {
        var code = $inputs.map(function () { return $(this).val(); }).get().join('');
        $hidden.val(code);

        // Enable/disable submit button based on whether all inputs are filled
        var allFilled = $inputs.toArray().every(function (input) {
            return $(input).val().length === 1;
        });
        $submitButton.prop('disabled', !allFilled);
    }

    function clearError() {
        $codeRow.removeClass('has-error');
        $('.field-validation-error').hide();
    }

    // Focus first input on load
    $inputs.first().focus();

    // Typing a digit: auto-advance
    $inputs.on('input', function (e) {
        clearError();
        var $this = $(this);
        var val = $this.val();
        // Only allow digits
        if (!/^[0-9]$/.test(val)) {
            $this.val('');
            updateHidden();
            return;
        }
        var idx = $inputs.index(this);
        if (val && idx < $inputs.length - 1) {
            $inputs.eq(idx + 1).focus().select();
        }
        updateHidden();
    });

    // Pasting a code: fill all inputs
    $inputs.on('paste', function (e) {
        clearError();
        var paste = (e.originalEvent || e).clipboardData.getData('text');
        if (/^[0-9]{6}$/.test(paste)) {
            for (var i = 0; i < 6; i++) {
                $inputs.eq(i).val(paste[i]);
            }
            $inputs.last().focus();
            updateHidden();
            e.preventDefault();
        }
    });

    // Backspace/delete: move to previous input if empty
    $inputs.on('keydown', function (e) {
        clearError();
        var $this = $(this);
        var idx = $inputs.index(this);
        if ((e.key === 'Backspace' || e.key === 'Delete')) {
            if (!$this.val() && idx > 0) {
                $inputs.eq(idx - 1).focus().val('');
                updateHidden();
                e.preventDefault();
            }
        } else if (e.key === 'ArrowLeft' && idx > 0) {
            $inputs.eq(idx - 1).focus().select();
            e.preventDefault();
        } else if (e.key === 'ArrowRight' && idx < $inputs.length - 1) {
            $inputs.eq(idx + 1).focus().select();
            e.preventDefault();
        }
    });

    // On focus: select content
    $inputs.on('focus', function () {
        clearError();
        $(this).select();
    });

    // Form submission
    $form.on('submit', function (e, v) {
        const skipValidation = e.originalEvent?.submitter?.attributes?.getNamedItem('skip-validation')?.value ?? false;
        if (skipValidation) {
            return true;
        }
        var code = $hidden.val();
        if (!code || code.length !== 6) {
            $codeRow.addClass('has-error');
            $('.field-validation-error').show();
            return false;
        }
        return true;
    });

    // Update hidden on page load (in case of autofill)
    updateHidden();
}); 