$(document).ready(function () {
    const { $emailInput, $submitButton } = window.HealthNetUtils.getCommonFormElements();
    
    window.HealthNetUtils.setupEmailFormSubmissionLoading($emailInput, $submitButton, null);

    // Handle back link
    $('#back-link').on('click', function (e) {
        const email = $emailInput.val();
        if (email) {
            const link = new URL($(this).attr('href'), window.location.origin);
            link.searchParams.set("Email", email);
            $(this).attr('href', link.toString());
        }
    });
});