$(document).ready(function () {
    // Recovery code functionality
    const $copyButton = $('.copy-button');
    const $copySuccess = $('.copy-success');
    const $confirmCheckbox = $('.confirm-checkbox');
    const $doneButton = $('.login-button');

    // Disable submit button by default
    $doneButton.prop('disabled', true);

    // Handle copy button click
    $copyButton.on('click', function () {
        const code = $('.code-display').text().trim();
        navigator.clipboard.writeText(code).then(() => {
            $copySuccess.fadeIn();
        });
    });

    // Handle close button in success message
    $('.close-button').on('click', function () {
        $copySuccess.fadeOut();
    });

    // Handle checkbox change
    $confirmCheckbox.on('change', function () {
        const isChecked = $(this).is(':checked');
        $doneButton
            .prop('disabled', !isChecked)
            .toggleClass('disabled', !isChecked);
    });
});
