$(document).ready(function () {
    const passwordInputs = $('input[type="password"]');

    passwordInputs.each(function () {
        const input = this;
        const toggleButton = $(input).next('.password-toggle-button');

        if (toggleButton.length) {
            // Remove the inline onclick handler
            toggleButton.removeAttr('onclick');

            // Add click event listener
            toggleButton.on('click', function () {
                const icon = $(this).find('img');

                if (input.type === 'password') {
                    input.type = 'text';
                    icon.attr('src', '/images/eye.svg');
                } else {
                    input.type = 'password';
                    icon.attr('src', '/images/eye-off.svg');
                }
            });
        }
    });
}); 