$(document).ready(function () {
    function initializeButtonValidation(buttonName, requiredFields) {
        const $button = $(`button[name="${buttonName}"]`);
        if (!$button.length) return;

        const fields = $button.data('required-fields').split(',');

        function validateFields() {
            const allFieldsFilled = fields.every(fieldId => {
                const $field = $(`#${fieldId}`);
                return $field.length && $field.val().trim() !== '';
            });
            $button[0].disabled = !allFieldsFilled;
        }

        fields.forEach(fieldId => {
            const $field = $(`#${fieldId}`);
            if ($field.length) {
                $field.on('input', validateFields);
            }
        });

        validateFields();
    }

    // Initialize for all buttons with required fields except those with data-skip-validation
    $('button[data-required-fields]:not([data-skip-validation])').each(function () {
        const $button = $(this);
        const buttonName = $button.attr('name');
        const requiredFields = $button.data('required-fields');
        if (buttonName && requiredFields) {
            initializeButtonValidation(buttonName, requiredFields);
        }
    });
}); 