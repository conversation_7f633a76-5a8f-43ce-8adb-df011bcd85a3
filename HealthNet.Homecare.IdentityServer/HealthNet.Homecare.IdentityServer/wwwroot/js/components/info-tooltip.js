(function () {
    function initializeTooltips() {
        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        const tooltipMap = new Map();

        tooltipTriggerList.forEach(triggerEl => {
            // Initialize tooltip
            const tooltip = new bootstrap.Tooltip(triggerEl, {
                trigger: 'hover focus',
                html: true
            });

            tooltipMap.set(triggerEl, tooltip);

            // Handle click
            triggerEl.onclick = function (e) {
                e.preventDefault();
                e.stopPropagation();
                tooltip.toggle();
            };
        });

        // Handle click outside
        document.addEventListener('click', function (event) {
            tooltipMap.forEach((tooltip, triggerEl) => {
                const tooltipId = triggerEl.getAttribute('aria-describedby');
                const tooltipEl = tooltipId && document.getElementById(tooltipId);

                if (!triggerEl.contains(event.target) &&
                    (!tooltipEl || !tooltipEl.contains(event.target))) {
                    tooltip.hide();
                }
            });
        });
    }

    // Initialize on page load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeTooltips);
    } else {
        initializeTooltips();
    }
})();
