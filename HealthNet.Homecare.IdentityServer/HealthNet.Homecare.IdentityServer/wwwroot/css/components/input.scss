@use '../variables/colors' as *;
@use '../variables/typography' as *;
@use '../variables/spacing' as *;

.form-group {
    margin-bottom: $form-group-spacing;
    width: 100%;

    .form-label {
        display: flex;
        align-items: center;
        font-family: $font-primary;
        font-size: $font-size-sm;
        font-weight: $font-weight-medium;
        color: $text-secondary;
        margin-bottom: 6px;

        .info-icon {
            position: relative;
            top: -1px;
            left: 6px;
        }
    }

    .form-control {
        border: 1px solid $border-color;
        border-radius: 8px;
        padding: $input-padding;
        width: 100%;
        font-size: $font-size-base;
        transition: border-color 0.2s;
        height: 48px;

        &:focus {
            border-color: $info-color;
            outline: none;
            border-width: 2px;
            box-shadow: none;
        }

        &.error,
        &.invalid {
            border-color: $error-color;
            border-width: 2px;

            &:focus {
                border-color: $error-color;
            }
        }
    }

    .field-validation-error {
        display: none;
        color: $validation-text-color;
        font-size: $font-size-sm;
        margin-top: $spacing-1;
        font-weight: $font-weight-normal;
        position: relative;
        min-height: 20px;
        padding-left: $spacing-6;

        &:not(:empty) {
            display: flex;
            align-items: center;
        }

        &::before {
            content: '';
            display: none;
            width: 18px;
            height: 18px;
            background: url('/images/error-warning.svg') no-repeat center center;
            background-size: contain;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
        }

        &:not(:empty)::before {
            display: inline-block;
        }
    }
}