.password-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.password-toggle-button {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    min-width: 48px;
    min-height: 48px;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    // Prevent button from receiving focus to avoid keyboard dismissal
    &:focus {
        outline: none;
    }

    // Improve touch interaction
    &:hover {
        background-color: rgba(0, 0, 0, 0.04);
    }

    &:active {
        background-color: rgba(0, 0, 0, 0.08);
    }

    // Ensure touch target meets accessibility guidelines (44px minimum)
    @media (max-width: 768px) {
        min-width: 48px;
        min-height: 48px;
        padding: 12px;
    }
}

.password-toggle-icon {
    width: 16px;
    height: 16px;
    pointer-events: none;
}

// Add padding to input when password toggle is present
input[type="password"] {
    padding-right: 36px;
}