@use '../variables/colors' as *;

.info-icon {
    width: 16px;
    height: 16px;
    margin-left: 4px;
    cursor: help;
    display: inline-block;
    vertical-align: middle;
}

.tooltip {
    opacity: 1 !important;
    pointer-events: auto !important;
    z-index: 1070 !important;
    border-radius: 8px !important;
    top: -7px !important;

    .tooltip-inner {
        background-color: $bg-white !important;
        color: $text-primary !important;
        padding: 8px 12px !important;
        max-width: 300px !important;
        text-align: left !important;
        font-size: 14px !important;
        line-height: 1.4 !important;
        border-radius: 8px !important;
        box-shadow: 0px 2px 2px -1px rgba(10, 13, 18, 0.04),
            0px 4px 6px -2px rgba(10, 13, 18, 0.03),
            0px 12px 16px -4px rgba(10, 13, 18, 0.08) !important;
    }

    .arrow {
        bottom: 0 !important;
        width: 0.8rem !important;
        height: 0.4rem !important;

        &::before {
            border-top-color: $bg-white !important;
            border-width: 0.4rem 0.4rem 0 !important;
        }
    }
}