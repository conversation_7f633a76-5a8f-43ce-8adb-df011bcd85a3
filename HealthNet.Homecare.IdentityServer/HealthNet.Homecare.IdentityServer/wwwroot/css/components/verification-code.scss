@use '../variables/colors' as *;
@use '../variables/typography' as *;
@use '../variables/spacing' as *;

.validation-summary-errors,
.validation-summary-valid {
    width: 100%;
    margin-bottom: $spacing-6;
}

.validation-summary-errors {
    background: $error-bg;
    border: 1px solid $error-color;
    color: $error-color;
    padding: $spacing-2 $spacing-4;
    border-radius: 8px;
    font-family: $font-primary;
    font-size: $font-size-sm;
    font-weight: $font-weight-normal;
    line-height: $line-height-normal;

    ul {
        margin: 0;
        padding-left: 0;
        list-style: none;

        li {
            position: relative;
            padding-left: $spacing-6;
            min-height: 20px;
            display: flex;
            align-items: center;

            &::before {
                content: '';
                display: inline-block;
                width: 18px;
                height: 18px;
                background: url('/images/error-warning.svg') no-repeat center center;
                background-size: contain;
                position: absolute;
                left: 0;
            }
        }
    }

    &.simple-error {
        background: none;
        border: none;
        padding: 0;
        margin-top: $spacing-2;
        text-align: center;
        color: $validation-text-color;
    }

    &.show-on-mobile {
        display: none;

        @media (max-width: 767px) {
            display: block;
        }
    }

    &.show-on-desktop {
        display: none;

        @media (min-width: 768px) {
            display: block;
        }
    }
}