@use '../variables/colors' as *;
@use '../variables/typography' as *;
@use '../variables/spacing' as *;

.authentication-methods {
    width: 100%;
    margin-bottom: $spacing-8;

    .select-method-label {
        display: block;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-secondary;
        margin-bottom: $spacing-4;
    }

    .radio-group {
        display: flex;
        flex-direction: column;
        gap: $spacing-4;
        margin-bottom: 12px;
    }

    .radio-item {
        display: flex;
        flex-direction: column;
        position: relative;
        padding: $spacing-4;
        border: 1px solid $border-color;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            border-color: #0572C4;
        }

        input[type="radio"] {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;

            &:checked~[data-method] {
                display: block;
                margin-top: $spacing-4;
            }

            &:checked+label::before {
                border-color: #0572C4;
                background-color: #0572C4;
            }

            &:checked+label::after {
                transform: scale(1);
            }
        }

        label {
            padding-left: $spacing-8;
            font-size: $font-size-base;
            font-weight: $font-weight-medium;
            color: $text-secondary;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 2px;
                width: 20px;
                height: 20px;
                border: 1px solid #D9D9D9;
                border-radius: 50%;
                background-color: $white;
                transition: all 0.2s ease;
            }

            &::after {
                content: '';
                position: absolute;
                left: 6px;
                top: 8px;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background-color: $white;
                transform: scale(0);
                transition: transform 0.2s ease;
            }
        }

        .method-description {
            padding-left: $spacing-8;
            font-size: $font-size-base;
            color: $text-secondary;
            transition: all 0.2s ease;
        }
    }

    .email-input-container,
    .sms-input-container,
    .whatsapp-input-container {
        display: none;
        margin-top: $spacing-4;
        padding-left: $spacing-8;

        &.active {
            display: block;
        }

        .form-control {
            background-color: $white;
        }
    }
}