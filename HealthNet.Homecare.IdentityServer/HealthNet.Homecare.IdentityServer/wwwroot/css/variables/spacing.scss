// Spacing scale
$spacing-0: 0;
$spacing-1: 0.25rem; // 4px
$spacing-2: 0.5rem; // 8px
$spacing-3: 0.75rem; // 12px
$spacing-4: 1rem; // 16px
$spacing-5: 1.25rem; // 20px
$spacing-6: 1.5rem; // 24px
$spacing-8: 2rem; // 32px
$spacing-10: 2.5rem; // 40px
$spacing-11: 2.75rem; // 44px
$spacing-12: 3rem; // 48px
$spacing-16: 4rem; // 64px
$spacing-20: 5rem; // 80px
$spacing-24: 6rem; // 96px

// Container padding
$container-padding: $spacing-4;
$container-padding-lg: $spacing-6;

// Form spacing
$form-spacing: $spacing-4;
$form-group-spacing: $spacing-2;
$input-padding: $spacing-3 $spacing-4;

// Button spacing
$button-padding: $spacing-3 $spacing-6;
$button-padding-sm: $spacing-2 $spacing-4;
$button-padding-lg: $spacing-4 $spacing-8;