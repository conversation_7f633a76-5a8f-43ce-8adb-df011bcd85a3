@use '../variables/colors' as *;
@use '../variables/typography' as *;
@use '../variables/spacing' as *;

.verification-code-row {
    display: flex;
    flex-wrap: nowrap;
    gap: 8px !important;
    align-items: center;
    justify-content: center;
}

.verification-code-description {
    color: $text-secondary;
    font-size: $font-size-base;
    font-weight: $font-weight-normal;
    margin-bottom: $spacing-3;
}

.verification-code-input {
    width: 53.33333206176758px;
    height: 68px;
    font-size: 48px;
    text-align: center;
    line-height: 60px;
    box-sizing: border-box;
    border-radius: 10px;
    border: 1px solid #D9D9D9;
    padding: 0;
    font-weight: 500;
    color: #1F1F1F;

    &:focus {
        border-color: $info-color;
        outline: none;
        border-width: 2px;
        box-shadow: none;
    }

    &::placeholder {
        color: #D9D9D9;
        font-weight: 500;
        font-size: 48px;
        text-align: center;
        line-height: 60px;
        vertical-align: middle;
    }
}

.verification-code-field-label {
    display: flex;
    align-items: center;
    font-family: $font-primary;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $text-secondary;
    margin-bottom: $spacing-2;
    width: 100%;
    justify-content: flex-start;
}

.verification-code-form {
    .verification-code-row {
        &.has-error {
            .verification-code-input {
                border-color: $error-color !important;
                border-width: 2px !important;
            }
        }
    }

    .field-validation-error {
        color: $validation-text-color;
        font-size: $font-size-sm;
        font-weight: $font-weight-normal;
        margin-top: $spacing-2;
        display: block;
        text-align: center;
    }
}

