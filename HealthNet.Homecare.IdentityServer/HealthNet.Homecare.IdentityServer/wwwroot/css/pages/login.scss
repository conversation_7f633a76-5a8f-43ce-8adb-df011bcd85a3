@use '../variables/colors' as *;
@use '../variables/typography' as *;
@use '../variables/spacing' as *;

.login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    padding: $spacing-6;
    background: url('/images/login-background.png') no-repeat center center fixed;
    background-size: cover;
}

.login-card {
    width: 450px;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: $bg-white;
    border-radius: 16px;
    padding: $spacing-12 45px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.login-logo {
    text-align: center;
    margin-bottom: $spacing-6;
}

.login-heading {
    font-family: $font-primary;
    font-weight: $font-weight-bold;
    font-size: $font-size-2xl;
    line-height: $line-height-tight;
    letter-spacing: $letter-spacing-normal;
    text-align: center;
    margin-bottom: $spacing-8;
    color: $blue;
}

.login-form {
    width: 100%;
}

.remember-forgot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $form-spacing;
    width: 100%;
}

.form-check {
    display: flex;
    align-items: center;
    gap: $spacing-2;
}

.form-check-input {
    appearance: none;
    -webkit-appearance: none;
    background-color: $bg-white;
    border: 1px solid $border-color;
    width: 16px;
    height: 16px;
    border-radius: 4px;
    margin-top: 0;
    display: inline-block;
    position: relative;
    cursor: pointer;
    vertical-align: middle;
    transition: border-color 0.2s, box-shadow 0.2s;

    &:checked {
        background-color: $blue;
        border-color: $blue;

        &::after {
            content: '';
            display: block;
            position: absolute;
            top: 50%;
            left: 50%;
            width: 4px;
            height: 8px;
            border: solid $bg-white;
            border-width: 0 2px 2px 0;
            transform: translate(-50%, -60%) rotate(45deg);
        }
    }
}

.form-check-label {
    font-size: $font-size-sm;
    color: $text-secondary;
    font-weight: $font-weight-medium;
    margin-left: $spacing-1;
}

.forgot-ct-number {
    margin-bottom: $spacing-5;
    margin-top: -$spacing-2;
}

.forgot-link {

    a {
        font-size: $font-size-sm;
        color: $link-color;
        text-decoration: none;
        font-weight: $font-weight-semibold;

        &:hover {
            text-decoration: underline;
            color: $link-hover-color;
        }
    }
}

.login-button {
    width: 100%;
    margin-bottom: 32px;
}

.help-link {
    font-size: $font-size-sm;
    color: $text-muted;
    font-weight: $font-weight-normal;
    text-align: center;

    a {
        color: $link-color;
        font-weight: $font-weight-semibold;
    }
}

input.input-validation-error,
input:invalid,
select.input-validation-error,
textarea.input-validation-error {
    border-color: $error-color !important;
    border-width: 2px !important;
}

@media (max-width: 600px) {
    body {
        background: #fff !important;
        background-image: none !important;
    }

    .login-container {
        align-items: start;
    }

    .login-container,
    .login-card {
        background: #fff;
        width: 100%;
        padding: 16px;
    }

    .login-card {
        width: 100%;
        min-width: unset;
        max-width: 100%;
        border-radius: 0;
        box-shadow: none;
        padding: 0;
        align-items: stretch;
    }

    .login-help-top-right-mobile {
        display: flex !important;
        justify-content: flex-end !important;
        align-items: center !important;
        margin-bottom: $spacing-5 !important;

        span {
            color: #094D83;
            font-size: $font-size-sm;
            font-weight: $font-weight-semibold;
        }

    }

    .login-help-top-right {
        display: none !important;
    }
}

.cf-turnstile {
    width: 100%;
    margin-bottom: $spacing-4 !important;
}

.login-help-top-right {
    position: absolute;
    top: 6%;
    right: 3%;
    z-index: 10;
    font-size: $font-size-base;
    color: $white;
    display: flex;
    align-items: center;
    gap: 6px;

    a {
        color: $white;
        text-decoration: none;
        font-weight: $font-weight-semibold;
        transition: color 0.2s;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 6px;
    }
}

.login-help-top-right-mobile {
    display: none;
}