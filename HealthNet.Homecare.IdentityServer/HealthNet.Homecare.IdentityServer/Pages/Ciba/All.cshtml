@page
@model HealthNet.Homecare.IdentityServer.Pages.Ciba.AllModel
@{
}

<div class="ciba-page">
    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h2>Pending Backchannel Login Requests</h2>
                </div>
                <div class="card-body">
                    @if (Model.Logins.Any())
                    {
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>Id</th>
                                    <th>Client Id</th>
                                    <th>Binding Message</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var login in Model.Logins)
                                {
                                    <tr>
                                        <td>@login.InternalId</td>
                                        <td>@login.Client.ClientId</td>
                                        <td>@login.BindingMessage</td>
                                        <td>
                                            <a asp-page="Consent" asp-route-id="@login.InternalId" class="btn btn-primary">Process</a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    }
                    else
                    {
                        <div>No Pending Login Requests</div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>