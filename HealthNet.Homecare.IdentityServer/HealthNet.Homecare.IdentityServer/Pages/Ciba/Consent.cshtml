@page
@model HealthNet.Homecare.IdentityServer.Pages.Ciba.Consent
@{
}

<div class="ciba-consent">
    <div class="lead">
        @if (Model.View.ClientLogoUrl != null)
        {
            <div class="client-logo"><img src="@Model.View.ClientLogoUrl"></div>
        }
        <h1>
            @Model.View.ClientName
            <small class="text-muted">is requesting your permission</small>
        </h1>
        
        <h3>Verify that this identifier matches what the client is displaying: <em  class="text-primary">@Model.View.BindingMessage</em></h3>

        <p>Uncheck the permissions you do not wish to grant.</p>
    </div>

    <div class="row">
        <div class="col-sm-8">
            <partial name="_ValidationSummary" />
        </div>
    </div>

    <form asp-page="/Ciba/Consent">
        <input type="hidden" asp-for="Input.Id" />
        <div class="row">
            <div class="col-sm-8">
                @if (Model.View.IdentityScopes.Any())
                {
                    <div class="form-group">
                        <div class="card">
                            <div class="card-header">
                                <span class="glyphicon glyphicon-user"></span>
                                Personal Information
                            </div>
                            <ul class="list-group list-group-flush">
                                @foreach (var scope in Model.View.IdentityScopes)
                                {
                                    <partial name="_ScopeListItem" model="@scope" />
                                }
                            </ul>
                        </div>
                    </div>
                }

                @if (Model.View.ApiScopes.Any())
                {
                    <div class="form-group">
                        <div class="card">
                            <div class="card-header">
                                <span class="glyphicon glyphicon-tasks"></span>
                                Application Access
                            </div>
                            <ul class="list-group list-group-flush">
                                @foreach (var scope in Model.View.ApiScopes)
                                {
                                    <partial name="_ScopeListItem" model="scope" />
                                }
                            </ul>
                        </div>
                    </div>
                }

                <div class="form-group">
                    <div class="card">
                        <div class="card-header">
                            <span class="glyphicon glyphicon-pencil"></span>
                            Description
                        </div>
                        <div class="card-body">
                            <input class="form-control" placeholder="Description or name of device" asp-for="Input.Description" autofocus>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-4">
                <button name="Input.button" value="yes" class="btn btn-primary" autofocus>Yes, Allow</button>
                <button name="Input.button" value="no" class="btn btn-secondary">No, Do Not Allow</button>
            </div>
            <div class="col-sm-4 col-lg-auto">
                @if (Model.View.ClientUrl != null)
                {
                    <a class="btn btn-outline-info" href="@Model.View.ClientUrl">
                        <span class="glyphicon glyphicon-info-sign"></span>
                        <strong>@Model.View.ClientName</strong>
                    </a>
                }
            </div>
        </div>
    </form>
</div>
