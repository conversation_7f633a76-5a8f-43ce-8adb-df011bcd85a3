using System.ComponentModel.DataAnnotations;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.ResetPassword;

public class InputModel
{
	[Required(ErrorMessage = "Password is required")]
	[StringLength(100, MinimumLength = 8, ErrorMessage = "Password must be at least 8 characters")]
	[DataType(DataType.Password)]
	public string? NewPassword { get; set; }

	[Required(ErrorMessage = "Confirm password is required")]
	[Compare("NewPassword", ErrorMessage = "Passwords do not match")]
	[DataType(DataType.Password)]
	public string? ConfirmPassword { get; set; }
}
