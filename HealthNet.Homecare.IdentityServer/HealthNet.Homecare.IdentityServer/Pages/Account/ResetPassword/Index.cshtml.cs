using HealthNet.Homecare.IdentityServer.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.ResetPassword;

[AllowAnonymous]
public class IndexModel(ProfileManagementService profileManagementService) : PageModel
{
	private readonly ProfileManagementService _profileManagementService = profileManagementService;

	[BindProperty]
	public InputModel Input { get; set; } = new();

	[BindProperty(SupportsGet = true)]
	public string UserId { get; set; } = string.Empty;

	[BindProperty(SupportsGet = true)]
	public string ResetToken { get; set; } = string.Empty;

	[BindProperty(SupportsGet = true)]
	public bool IsPatient { get; set; }

	[BindProperty(SupportsGet = true)]
	public string? ResetPasswordRedirectUrl { get; set; } = string.Empty;

	public async Task<IActionResult> OnGetAsync()
	{
		var user = await _profileManagementService.GetUserFromSession(UserId);

		if (user is null)
		{
			ModelState.AddModelError(string.Empty, "Invalid or expired reset link.");
			return Page();
		}

		var isTokenValid = await _profileManagementService.VerifyResetTokenAsync(user, ResetToken);
		if (!isTokenValid)
		{
			return RedirectToPage("../ForgotPassword/Index", new
			{
				step = 2,
				CTNumber = user.UserName,
				Email = user.Email,
				IsLinkExpired = true,
				ReturnUrl = user.ResetPasswordRedirectUrl
			});
		}

		IsPatient = user?.IsPatient ?? false;
		ResetPasswordRedirectUrl = user?.ResetPasswordRedirectUrl;
		return Page();
	}

	public async Task<IActionResult> OnPostAsync()
	{
		if (!ModelState.IsValid)
		{
			return Page();
		}

		if (!ValidatePassword())
		{
			return Page();
		}

		var user = await _profileManagementService.GetUserFromSession(UserId);
		if (user is null)
		{
			ModelState.AddModelError(string.Empty, "Invalid or expired reset link.");
			return Page();
		}

		var result = await _profileManagementService.ResetPasswordAsync(user, ResetToken, Input.NewPassword!);
		if (!result)
		{
			ModelState.AddModelError(string.Empty, "Invalid or expired reset link.");
			return Page();
		}

		return RedirectToPage("../PasswordResetSuccess/Index", new { returnUrl = ResetPasswordRedirectUrl });
	}

	private bool ValidatePassword()
	{
		if (!IsPatient)
		{
			if (Input.NewPassword!.Length < 12)
			{
				ModelState.AddModelError("Input.NewPassword", "Business user passwords must be at least 12 characters.");
				return false;
			}

			return true;
		}

		var password = Input.NewPassword!;
		bool hasUpper = password.Any(char.IsUpper);
		bool hasLower = password.Any(char.IsLower);
		bool hasDigit = password.Any(char.IsDigit);
		bool hasSpecial = password.Any(c => "!@#$%&*.".Contains(c));

		if (!hasUpper || !hasLower || !hasDigit || !hasSpecial)
		{
			ModelState.AddModelError("Input.NewPassword", "Password must contain uppercase, lowercase, digit, and special character (!@#$%&*.).");
			return false;
		}

		return true;
	}
}
