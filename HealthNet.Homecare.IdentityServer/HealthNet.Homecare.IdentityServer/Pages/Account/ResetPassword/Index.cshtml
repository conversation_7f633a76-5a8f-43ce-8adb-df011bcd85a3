@page
@model HealthNet.Homecare.IdentityServer.Pages.Account.ResetPassword.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input

<div class="login-container">
	<div class="login-help-top-right">
		<a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
			<img src="~/images/help.svg" alt="Help" class="help-icon-desktop" />
			<span>Need help?</span>
		</a>
	</div>
	<div class="login-card">
		<div class="login-help-top-right-mobile">
			<a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
				<img src="~/images/help-blue.svg" alt="Help" class="help-icon-mobile" />
				<span>Help</span>
			</a>
		</div>
		<div class="login-logo">
			<img src="~/images/password-logo.png" alt="Logo" />
		</div>
		<h1 class="create-account-heading">Create password</h1>
		<form method="post" class="create-account-form" autocomplete="off">
			<input type="hidden" asp-for="ResetToken" />
			<input type="hidden" asp-for="IsPatient" id="IsPatient" />
			<input type="hidden" asp-for="ResetPasswordRedirectUrl" />

			<div class="create-account-form-group">
				<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
						 model="@(new HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input.InputModel {
                             Id = "newPassword",
                             Name = "Input.NewPassword",
                             Type = "password",
							 Label = "New password",
                         })" />
				<ul class="password-requirements" id="password-requirements">
					<li id="length-check"><img src="/images/check.svg" class="checkmark" alt="Check" width="20" height="20" /> @(Model.IsPatient ? "Must be at least 8 characters long" : "Must be at least 12 characters long")</li>
					<li id="uppercase-check"><img src="/images/check.svg" class="checkmark" alt="Check" width="20" height="20" /> Must contain at least one capital letter</li>
					<li id="lowercase-check"><img src="/images/check.svg" class="checkmark" alt="Check" width="20" height="20" /> Must contain at least one lower case letter</li>
					<li id="number-check"><img src="/images/check.svg" class="checkmark" alt="Check" width="20" height="20" /> Must contain at least one number (0-9)</li>
					<li id="special-check"><img src="/images/check.svg" class="checkmark" alt="Check" width="20" height="20" /> Must contain at least one special character (@Html.Raw("!@#$%&*.")) </li>
				</ul>
			</div>
			<div class="create-account-form-group">
				<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
						 model="@(new HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input.InputModel {
                             Id = "confirmPassword",
                             Name = "Input.ConfirmPassword",
                             Type = "password",
							 Label = "Confirm password",
                         })" />
			</div>
			<div asp-validation-summary="ModelOnly" class="validation-summary-errors simple-error"></div>
			<partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
					 model="@(new ButtonModel {
                         Text = "Create",
                         ButtonType = "submit",
                         ButtonStyle = "primary",
                         AdditionalClasses = "login-button",
                         Name = "Input.Button",
                         Value = "update",
                         IsDisabled = false
                     })" />
		</form>
	</div>
</div>

@section scripts {
	@Html.ScriptWithNonce(Url.Content("~/js/pages/password-validation.js"))
}