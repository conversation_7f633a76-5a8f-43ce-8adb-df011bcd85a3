@model HealthNet.Homecare.IdentityServer.Pages.Account.Setup.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using Microsoft.AspNetCore.WebUtilities
@{
	ViewData["Title"] = "Password Created";
	var loginUrl = Model.WebPortalLoginRedirectUrl;
	var setupTwoFactorUrl = "/Account/Setup/?Step=7";
	if (!string.IsNullOrEmpty(Model.Step6.UserId))
	{
		setupTwoFactorUrl = QueryHelpers.AddQueryString(setupTwoFactorUrl, "UserId", Model.Step6.UserId);
	}
}

<div class="login-logo">
	<img src="~/images/success-icon.svg" alt="Logo" />
</div>

<h1 class="create-account-heading">Password Created</h1>
<span class="create-account-description">
	Your password has been successfully created.
</span>

<form method="post" class="create-account-form">
	<input type="hidden" asp-for="ReturnUrl" />
	<input type="hidden" asp-for="ViewModel.IsPatient" />
	<input type="hidden" asp-for="Step6.UserId" />
	<a href="@loginUrl" class="btn btn-primary login-button">Go to Log in</a>
</form>

<div class="create-account-password-created-setup-2fa-section">
	<div class="create-account-password-created-setup-2fa-section-divider">
		<div class="create-account-password-created-setup-2fa-section-divider-line"></div>
		<span class="create-account-password-created-setup-2fa-section-divider-text">OR</span>
		<div class="create-account-password-created-setup-2fa-section-divider-line"></div>
	</div>

	<div class="create-account-password-created-setup-2fa-section-content">
		<div class="create-account-password-created-setup-2fa-section-title">Make your account extra safe</div>
		<div class="create-account-password-created-setup-2fa-section-link">
			<a href="@setupTwoFactorUrl">Set up two-factor authentication</a>
		</div>
	</div>
</div>

