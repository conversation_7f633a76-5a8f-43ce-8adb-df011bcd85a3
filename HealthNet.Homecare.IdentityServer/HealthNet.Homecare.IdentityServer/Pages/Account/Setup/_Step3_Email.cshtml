@model HealthNet.Homecare.IdentityServer.Pages.Account.Setup.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input
@using Microsoft.AspNetCore.WebUtilities
@{
	var previousStepUrl = "/Account/Setup";
	if (Model.Step3.DateOfBirth.HasValue)
	{
		previousStepUrl = QueryHelpers.AddQueryString(previousStepUrl, "Step", "2");
		previousStepUrl = QueryHelpers.AddQueryString(previousStepUrl, "BirthDate", $"{Model.Step2.BirthDay}-{Model.Step2.BirthMonth}-{Model.Step2.BirthYear}");
	}
	else
	{
		previousStepUrl = QueryHelpers.AddQueryString(previousStepUrl, "Step", "1");
	}

	if (!string.IsNullOrEmpty(Model.ReturnUrl))
	{
		previousStepUrl = QueryHelpers.AddQueryString(previousStepUrl, "ReturnUrl", Model.ReturnUrl);
	}

	if (!string.IsNullOrEmpty(Model.Step3.CTNumber))
	{
		previousStepUrl = QueryHelpers.AddQueryString(previousStepUrl, "CTNumber", Model.Step3.CTNumber);
	}
}

<div class="login-logo">
	<img src="~/images/email-logo.png" alt="Logo" />
</div>
<h1 class="create-account-heading">Set up account</h1>
<span class="create-account-description">Enter your email address and we'll send you a link to create password.</span>
<form method="post" asp-page-handler="Step3" class="create-account-form">
	<input type="hidden" asp-for="ViewModel.CurrentStep" />
	<input type="hidden" asp-for="ViewModel.IsPatient" />
	<input type="hidden" asp-for="Step3.CTNumber" />
	<input type="hidden" asp-for="Step3.DateOfBirth" />
	<input type="hidden" asp-for="ReturnUrl" />

	<div class="create-account-email-container">
		<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
				 model="@(new InputModel {
                     Id = "email",
                     Name = "Step3.Email",
                     Label = "Email address",
                     Value = Model.Step3.Email ?? ""
                 })" />
	</div>
	<div asp-validation-summary="All" class="validation-summary-errors simple-error" style="display: @(Model.ModelState.ErrorCount > 0 ? "block" : "none")"></div>
	<partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
			 model="@(new ButtonModel {
                 Text = "Send link to email",
                 ButtonType = "submit",
                 ButtonStyle = "primary",
                 AdditionalClasses = "login-button",
                 Name = "action",
                 Value = "next",
                 RequiredFields = new[] { "email" }
             })" />
	<div class="create-account-help-link">
		<a href="@previousStepUrl"><img src="~/images/arrow-left.svg" alt="Back" /> Back</a>
	</div>
</form> 