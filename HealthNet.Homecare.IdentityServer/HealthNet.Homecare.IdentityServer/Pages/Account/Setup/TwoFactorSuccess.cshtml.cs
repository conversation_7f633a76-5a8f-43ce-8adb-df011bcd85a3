using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.Setup;

[AllowAnonymous]
public class TwoFactorSuccessModel : PageModel
{
	[BindProperty(SupportsGet = true)]
	public string RecoveryCode { get; set; } = string.Empty;

	[BindProperty(SupportsGet = true)]
	public string UserId { get; set; } = string.Empty;

	public void OnGet(string userId, string recoveryCode)
	{
		UserId = userId ?? string.Empty;
		RecoveryCode = recoveryCode ?? string.Empty;
	}
}
