using HealthNet.Homecare.IdentityServer.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.Setup;

public class Step7InputModel
{
	[Required(ErrorMessage = "Please select an authentication method")]
	public string AuthMethod { get; set; } = string.Empty;

	[EmailAddress(ErrorMessage = "Please enter a valid email address")]
	public string? Email { get; set; } = string.Empty;

	[Phone(ErrorMessage = "Please enter a valid phone number")]
	public string? Phone { get; set; } = string.Empty;

	[Phone(ErrorMessage = "Please enter a valid WhatsApp number")]
	public string? WhatsApp { get; set; } = string.Empty;

	public string? UserId { get; set; }

	public MfaType MfaType => AuthMethod?.ToLowerInvariant() switch
	{
		"email" => MfaType.Email,
		"sms" => MfaType.Phone,
		"whatsapp" => MfaType.WhatsApp,
		_ => MfaType.Unknown
	};
}
