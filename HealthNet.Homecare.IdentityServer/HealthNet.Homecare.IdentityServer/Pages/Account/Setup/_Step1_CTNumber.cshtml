@model HealthNet.Homecare.IdentityServer.Pages.Account.Setup.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input
@using Microsoft.AspNetCore.WebUtilities
@{
	var loginUrl = "/Account/Login";
	var forgotCTNumberUrl = $"/Account/ForgotCTNumber";
	if (!string.IsNullOrEmpty(Model.ReturnUrl))
	{
		loginUrl = QueryHelpers.AddQueryString(loginUrl, "ReturnUrl", Model.ReturnUrl);
		forgotCTNumberUrl = QueryHelpers.AddQueryString(forgotCTNumberUrl, "ReturnUrl", Model.ReturnUrl);
	}
	else
	{
		loginUrl = Model.WebPortalLoginRedirectUrl;
	}
}

<div class="login-logo">
	<img src="~/images/logo.svg" alt="Logo" />
</div>
<h1 class="create-account-heading">Set up account</h1>
<span class="create-account-description">Start by entering your CT number. This can be found on any delivery note or in your welcome email. It starts with CT followed by 6 digits. (e.g CT123456)</span>
@if (Model.Step1.IsSetupRequired)
{
	<div class="alert-container">
		<div class="alert-icon">
			<img src="~/images/info-circle-warning.svg" alt="Warning" />
		</div>
		<div class="alert-content">
			<div class="alert-title">
				<div>It looks like you don’t have an account set up yet.</div>
			  <img src="~/images/close.svg" alt="close" class="alert-close-icon" />
			</div>
			<div class="alert-message">Click continue to start setting up your account.</div>
		</div>
	</div>
}
<form method="post" asp-page-handler="Step1" class="create-account-form">
	<input type="hidden" asp-for="Step1.IsSetupRequiredMessageSeen" />
	<input type="hidden" asp-for="ViewModel.CurrentStep" />
	<input type="hidden" asp-for="ViewModel.IsPatient" />
	<input type="hidden" asp-for="ReturnUrl" />

	<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
			 model="@(new InputModel {
                 Id = "username",
                 Name = "Step1.CTNumber",
                 Label = "CT number",
                 Autofocus = true,
                 Value = Model.Step1.CTNumber ?? "",
                 AdditionalClasses = "ct-number-input",
             })" />
	<div asp-validation-summary="All" class="validation-summary-errors simple-error" style="display: @(Model.ModelState.ErrorCount > 0 ? "block" : "none")"></div>
	<div class="forgot-link mb-3">
		<a href="@forgotCTNumberUrl">Forgot CT number?</a>
	</div>
	<partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
			 model="@(new ButtonModel {
                 Text = "Continue",
                 ButtonType = "submit",
                 ButtonStyle = "primary",
                 AdditionalClasses = "login-button",
                 Name = "action",
                 Value = "next",
                 RequiredFields = new[] { "username" }
             })" />
	<div class="create-account-help-link">
		Already have an account? <a href="@loginUrl">Log in</a>
	</div>
</form>
