@model HealthNet.Homecare.IdentityServer.Pages.Account.Setup.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input
@using Microsoft.AspNetCore.WebUtilities
@{
	var firstStepUrl = "/Account/Setup/?Step=1";
	if (!string.IsNullOrEmpty(Model.ReturnUrl))
	{
		firstStepUrl = QueryHelpers.AddQueryString(firstStepUrl, "ReturnUrl", Model.ReturnUrl);
	}

	if (!string.IsNullOrEmpty(Model.Step2.CTNumber))
	{
		firstStepUrl = QueryHelpers.AddQueryString(firstStepUrl, "CTNumber", Model.Step2.CTNumber);
	}
}

<div class="login-logo">
	<img src="~/images/user-logo.png" alt="Logo" />
</div>
<h1 class="create-account-heading">Set up account</h1>
<div class="create-account-ct-number"><img src="~/images/shield-tick.svg" alt="ct-number" />@Model.Step2.CTNumber</div>
<span class="create-account-description">Enter birthdate</span>
<form method="post" asp-page-handler="Step2" class="create-account-form">
	<input type="hidden" asp-for="ViewModel.CurrentStep" />
    <input type="hidden" asp-for="ViewModel.IsPatient" />
	<input type="hidden" asp-for="Step2.CTNumber" />
	<input type="hidden" asp-for="ReturnUrl" />

	<div class="create-account-form-group">
		<div class="row">
			<div class="col">
				<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
						 model="@(new InputModel {
                             Label = "Day",
                             Id = "birthDay",
                             Name = "Step2.BirthDay",
                             Placeholder = "DD",
                             Type = "number",
                             MaxLength = 2,
                             Min = 1,
                             Max = 31,
                             Value = Model.Step2.BirthDay?.ToString() ?? ""
                         })" />
			</div>
			<div class="col">
				<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
						 model="@(new InputModel {
                             Label = "Month",
                             Id = "birthMonth",
                             Name = "Step2.BirthMonth",
                             Placeholder = "MM",
                             Type = "number",
                             MaxLength = 2,
                             Min = 1,
                             Max = 12,
                             Value = Model.Step2.BirthMonth?.ToString() ?? ""
                         })" />
			</div>
			<div class="col">
				<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
						 model="@(new InputModel {
                             Label = "Year",
                             Id = "birthYear",
                             Name = "Step2.BirthYear",
                             Placeholder = "YYYY",
                             Type = "number",
                             MaxLength = 4,
                             Min = 1900,
                             Max = DateTime.Now.Year,
                             Value = Model.Step2.BirthYear?.ToString() ?? ""
                         })" />
			</div>
		</div>
	</div>
	<div asp-validation-summary="All" class="validation-summary-errors simple-error" style="display: @(Model.ModelState.ErrorCount > 0 ? "block" : "none")"></div>
	<partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
			 model="@(new ButtonModel {
                 Text = "Continue",
                 ButtonType = "submit",
                 ButtonStyle = "primary",
                 AdditionalClasses = "login-button",
                 Name = "action",
                 Value = "next",
                 IsDisabled = false,
                AdditionalAttributes = new Dictionary<string, string> { { "data-skip-validation", "true" } }
             })" />
	<div class="create-account-help-link">
		<a href="@firstStepUrl"><img src="~/images/arrow-left.svg" alt="Back" /> Back</a>
	</div>
</form>

