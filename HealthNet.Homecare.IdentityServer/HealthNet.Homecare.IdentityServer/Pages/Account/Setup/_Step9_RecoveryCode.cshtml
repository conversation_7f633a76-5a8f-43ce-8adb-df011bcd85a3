@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using Microsoft.AspNetCore.WebUtilities
@model HealthNet.Homecare.IdentityServer.Pages.Account.Setup.IndexModel
@{
	var previousStepUrl = "/Account/Setup/?Step=7";
	if (!string.IsNullOrEmpty(Model.Step9.UserId))
	{
		previousStepUrl = QueryHelpers.AddQueryString(previousStepUrl, "UserId", Model.Step9.UserId);
	}
}

<div class="login-logo">
	<img src="~/images/password-logo.png" alt="Key Icon" />
</div>
<h1 class="create-account-heading">Save your new recovery code</h1>
<span class="create-account-description">
	This is a recovery code as back up in in case you can’t access your selected authentication method. Keep it somewhere safe.
</span>

<form method="post" asp-page-handler="Step9" autocomplete="off" class="create-account-form">
	<input type="hidden" asp-for="ViewModel.IsPatient" />
	<input type="hidden" asp-for="ReturnUrl" />
	<input type="hidden" asp-for="Step9.UserId" />
	<div class="code-section">
		<div class="code-display">@Model.Step9.RecoveryCode</div>
		<button type="button" class="copy-button">
			<img src="~/images/copy.svg" alt="Copy" /> Copy
		</button>
	</div>
	<div id="copySuccess" class="copy-success" style="display: none;">
		<img src="~/images/success-check.svg" alt="Success" />
		Code copied to clipboard
		<button type="button" class="close-button">×</button>
	</div>
	<div class="form-check recovery-code-checkbox">
		<input class="form-check-input confirm-checkbox" type="checkbox" />
		<label class="form-check-label">
			I confirm I have safely saved this code
		</label>
	</div>
	<partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
			model="@(new ButtonModel {
				Text = "Done",
				ButtonType = "submit",
				ButtonStyle = "primary",
				AdditionalClasses = "login-button",
				Name = "Input.Button",
				IsDisabled = true
			})" />
</form>
<div class="create-account-help-link mt-1">
	<a href="@previousStepUrl">
		<img src="~/images/arrow-left.svg" alt="Back" /> Back
	</a>
</div>
