@page
@model HealthNet.Homecare.IdentityServer.Pages.Account.Setup.TwoFactorSuccessModel
@{
    ViewData["Title"] = "Code Verified";
}

<div class="create-account-container">
    <div class="login-help-top-right">
        <a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
            <img src="~/images/help.svg" alt="Help" class="help-icon-desktop" />
            <span>Need help?</span>
        </a>
    </div>
    <div class="login-card">
        <div class="login-help-top-right-mobile">
            <a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
                <img src="~/images/help-blue.svg" alt="Help" class="help-icon-mobile" />
                <span>Help</span>
            </a>
        </div>
        <div class="verification-success-container">
            <div class="success-icon-container">
                <img src="~/images/tick-animation.gif" alt="Success" class="success-tick-icon" />
            </div>
            <h1 class="create-account-heading">Code verified!</h1>
        </div>
    </div>
</div>

@Html.InlineScriptWithNonce(@"
    setTimeout(function() {
        window.location.href = '/Account/Setup?Step=9&UserId=" + Model.UserId + "&RecoveryCode=" + Model.RecoveryCode + @"';
    }, 2000); // Redirect after 2 seconds
")