using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.Setup;

public class Step2InputModel
{
	[Required]
	public int? BirthDay { get; set; }
	[Required]
	public int? BirthMonth { get; set; }
	[Required]
	public int? BirthYear { get; set; }

	public DateTime DateOfBirth { get; set; }

	[HiddenInput]
	public string CTNumber { get; set; } = string.Empty;
}
