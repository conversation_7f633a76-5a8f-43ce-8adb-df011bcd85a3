@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@model HealthNet.Homecare.IdentityServer.Pages.Account.Setup.IndexModel
@{
}

<div class="login-logo">
	<img src="~/images/password-logo.png" alt="Logo" />
</div>
<h1 class="create-account-heading">Create password</h1>
<form method="post" asp-page-handler="Step5" class="create-account-form" autocomplete="off">
	<input type="hidden" asp-for="Step5.IsPatient" id="IsPatient" />
	<input type="hidden" asp-for="ViewModel.IsPatient" />
	<input type="hidden" asp-for="Step5.UserId" />
	<input type="hidden" asp-for="Step5.Token" />

	<div class="create-account-form-group">
		<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
				 model="@(new HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input.InputModel {
						Id = "newPassword",
						Name = "Step5.NewPassword",
						Type = "password",
						Label = "New password",
					})" />
		<ul class="password-requirements" id="password-requirements">
			<li id="length-check"><img src="/images/check.svg" class="checkmark" alt="Check" width="20" height="20" />Must be at least 8 characters long</li>
			<li id="uppercase-check"><img src="/images/check.svg" class="checkmark" alt="Check" width="20" height="20" /> Must contain at least one capital letter</li>
			<li id="lowercase-check"><img src="/images/check.svg" class="checkmark" alt="Check" width="20" height="20" /> Must contain at least one lower case letter</li>
			<li id="number-check"><img src="/images/check.svg" class="checkmark" alt="Check" width="20" height="20" /> Must contain at least one number (0-9)</li>
			<li id="special-check"><img src="/images/check.svg" class="checkmark" alt="Check" width="20" height="20" /> Must contain at least one special character (@Html.Raw("!@#$%&*.")) </li>
		</ul>
	</div>
	<div class="create-account-form-group">
		<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
				 model="@(new HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input.InputModel {
						Id = "confirmPassword",
						Name = "Step5.ConfirmPassword",
						Type = "password",
						Label = "Confirm password",
					})" />
	</div>
	<div asp-validation-summary="ModelOnly" class="validation-summary-errors simple-error"></div>
	<partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
			 model="@(new ButtonModel {
					Text = "Create",
					ButtonType = "submit",
					ButtonStyle = "primary",
					AdditionalClasses = "login-button",
					Name = "Input.Button",
					Value = "update",
					IsDisabled = false
				})" />
</form>

@section scripts {
	@Html.ScriptWithNonce(Url.Content("~/js/pages/password-validation.js"))
}