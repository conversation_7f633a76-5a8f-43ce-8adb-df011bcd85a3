using HealthNet.Homecare.IdentityServer.Domain.Common;
using HealthNet.Homecare.IdentityServer.Domain.Enums;
using HealthNet.Homecare.IdentityServer.Infrastructure.Configurations;
using HealthNet.Homecare.IdentityServer.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Options;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.Setup;

[AllowAnonymous]
public class IndexModel(
	LoginService loginService,
	ProfileManagementService profileManagementService,
	IOptions<SystemEmailsOptions> options,
	IOptions<WebPortalOptions> frontendOptions
) : PageModel
{
	private readonly LoginService _loginService = loginService;
	private readonly ProfileManagementService _profileManagementService = profileManagementService;
	public string EnquiriesEmail => options.Value.Enquiries;
	public string WebPortalLoginRedirectUrl => frontendOptions.Value.LoginRedirectUrl;

	[BindProperty]
	public Step1InputModel Step1 { get; set; } = new();

	[BindProperty]
	public Step2InputModel Step2 { get; set; } = new();

	[BindProperty]
	public Step3InputModel Step3 { get; set; } = new();

	[BindProperty]
	public Step5InputModel Step5 { get; set; } = new();

	[BindProperty]
	public Step6InputModel Step6 { get; set; } = new();

	[BindProperty]
	public Step7InputModel Step7 { get; set; } = new();

	[BindProperty]
	public Step8InputModel Step8 { get; set; } = new();

	[BindProperty]
	public Step9InputModel Step9 { get; set; } = new();

	[BindProperty]
	public Step10InputModel Step10 { get; set; } = new();

	[BindProperty]
	public ViewModel ViewModel { get; set; } = new();

	[BindProperty]
	public string ReturnUrl { get; set; } = string.Empty;

	public async Task OnGetAsync(int? Step = null, string? UserId = null, string? Token = null, string? CTNumber = null, string? BirthDate = null, string? ReturnUrl = null, string? RecoveryCode = null)
	{
		this.ReturnUrl = ReturnUrl ?? string.Empty;

		if (!string.IsNullOrEmpty(CTNumber))
		{
			var user = await _profileManagementService.GetContactByUsername(CTNumber);
			ViewModel.IsPatient = user?.IsPatientType ?? false;
		}

		if (!string.IsNullOrEmpty(UserId))
		{
			var user = await _profileManagementService.GetUserFromSession(UserId);
			ViewModel.IsPatient = user?.IsPatient ?? false;
		}

		ViewModel.CurrentStep = Step ?? 1;

		if (ViewModel.CurrentStep == 1)
		{
			Step1.CTNumber = CTNumber ?? string.Empty;
			Step1.IsSetupRequired = await GetIsSetupAccountRequired(Step1.CTNumber ?? string.Empty);
		}

		if (ViewModel.CurrentStep == 2)
		{
			Step2.CTNumber = CTNumber ?? string.Empty;

			var parts = BirthDate?.Split('-') ?? [];
			if (int.TryParse(parts[2], out var year))
			{
				Step2.BirthYear = year;
			}

			if (int.TryParse(parts[1], out var month))
			{
				Step2.BirthMonth = month;
			}

			if (int.TryParse(parts[0], out var day))
			{
				Step2.BirthDay = day;
			}
		}

		if (ViewModel.CurrentStep == 5)
		{
			if (!string.IsNullOrEmpty(UserId) && !string.IsNullOrEmpty(Token))
			{
				var user = await _profileManagementService.GetUserFromSession(UserId);
				if (user is null)
				{
					ModelState.AddModelError(string.Empty, "Invalid or expired reset link.");
					return;
				}

				var result = await _profileManagementService.VerifySetupAccountToken(user, Token);
				if (!result)
				{
					ModelState.AddModelError(string.Empty, "Invalid or expired setup account link.");
					return;
				}

				Step5.IsPatient = user.IsPatient;
				Step5.UserId = UserId;
				Step5.Token = Token;
			}
		}

		if (ViewModel.CurrentStep == 6)
		{
			Step6.UserId = UserId;
		}

		if (ViewModel.CurrentStep == 7)
		{
			Step7.UserId = UserId;
		}

		if (ViewModel.CurrentStep == 8)
		{
			Step8.UserId = UserId;

			if (!string.IsNullOrEmpty(UserId))
			{
				var user = await _profileManagementService.GetUserFromSession(UserId);
				if (user is null)
				{
					ModelState.AddModelError(string.Empty, "Invalid or expired link.");
					return;
				}

				Step8.MfaType = user.SecuritySettings?.MfaType ?? MfaType.Unknown;
				Step8.MfaDestination = user?.SecuritySettings?.MfaDestination ?? string.Empty;
			}
		}

		if (ViewModel.CurrentStep == 9)
		{
			Step9.UserId = UserId;
			Step9.RecoveryCode = RecoveryCode ?? string.Empty;
		}

		if (ViewModel.CurrentStep == 10)
		{
			Step10.UserId = UserId;
		}
	}

	public async Task<IActionResult> OnPostStep1Async()
	{
		ModelState.Clear();
		TryValidateModel(Step1, nameof(Step1));

		if (!ModelState.IsValid)
		{
			ViewModel.CurrentStep = 1;
			ModelState.AddModelError("Step1.CTNumber", "Invalid CT Number.");
			return Page();
		}

		if (!Step1.IsSetupRequiredMessageSeen)
		{
			Step1.IsSetupRequired = await GetIsSetupAccountRequired(Step1.CTNumber ?? string.Empty);
			if (Step1.IsSetupRequired)
			{
				Step1.IsSetupRequiredMessageSeen = true;
				return Page();
			}
		}

		var contact = await _profileManagementService.GetContactByUsername(Step1.CTNumber ?? "");
		var portalOptInStatus = contact?.PortalOptIn ?? PortalOptIn.Unknown;
		var isPatient = contact?.IsPatientType ?? false;
		if (portalOptInStatus == PortalOptIn.Unknown)
		{
			ViewModel.CurrentStep = 2;
			ViewModel.IsPatient = isPatient;
			Step2.CTNumber = Step1.CTNumber!;
			return Page();
		}

		if (portalOptInStatus == PortalOptIn.No)
		{
			ModelState.AddModelError("Step1.CTNumber", "No access.");
			return Page();
		}

		ViewModel.CurrentStep = 3;
		ViewModel.IsPatient = isPatient;
		Step3.CTNumber = Step1.CTNumber!;
		return Page();
	}

	public async Task<IActionResult> OnPostStep2Async()
	{
		ViewModel.CurrentStep = 2;

		ModelState.Clear();
		TryValidateModel(Step2, nameof(Step2));

		if (!ModelState.IsValid || !Step2.BirthYear.HasValue || !Step2.BirthMonth.HasValue || !Step2.BirthDay.HasValue)
		{
			ModelState.AddModelError(string.Empty, "Invalid Date of Birth.");
			return Page();
		}

		Step2.DateOfBirth = new DateTime(Step2.BirthYear ?? 0, Step2.BirthMonth ?? 0, Step2.BirthDay ?? 0);

		var result = await _profileManagementService.VerifyUsernameAndDateOfBirth(Step2.CTNumber, Step2.DateOfBirth);
		if (!result)
		{
			ModelState.AddModelError(string.Empty, "This date of birth does not match the CT number provided. Please call support on 0800 193 2027.");
			return Page();
		}

		ViewModel.CurrentStep = 3;
		Step3.CTNumber = Step2.CTNumber!;
		Step3.DateOfBirth = Step2.DateOfBirth;

		return Page();
	}

	public async Task<IActionResult> OnPostStep3Async()
	{
		ViewModel.CurrentStep = 3;

		ModelState.Clear();
		TryValidateModel(Step3, nameof(Step3));

		if (!ModelState.IsValid)
		{
			return Page();
		}

		var result = await _profileManagementService.VerifyUsernameAndEmail(Step3.CTNumber ?? "", Step3.Email ?? "");
		if (!result)
		{
			ModelState.AddModelError(string.Empty, "This email address does not match the CT number provided. Please try again or call support on 0800 193 2027.");
			return Page();
		}

		var emailSent = await _profileManagementService.SetupAccountAsync(Step3.CTNumber ?? "", Step3.Email ?? "");
		if (!emailSent)
		{
			ModelState.AddModelError(string.Empty, "Error sending an email.");
			return Page();
		}

		ViewModel.CurrentStep = 4;
		return Page();
	}

	public async Task<IActionResult> OnPostStep5Async()
	{
		ViewModel.CurrentStep = 5;

		ModelState.Clear();
		TryValidateModel(Step5, nameof(Step5));

		if (!ModelState.IsValid)
		{
			return Page();
		}

		var user = await _profileManagementService.GetUserFromSession(Step5.UserId ?? "");
		if (user is null)
		{
			ModelState.AddModelError(string.Empty, "Invalid or expired setup account link.");
			return Page();
		}

		var result = await _profileManagementService.CreateNewPasswordAsync(user, Step5.Token ?? "", Step5.NewPassword ?? "");
		if (!result)
		{
			ModelState.AddModelError(string.Empty, "Error creating new password. Please try again or contact support.");
			return Page();
		}

		ViewModel.CurrentStep = 6;
		ViewModel.IsPatient = user.IsPatient;
		Step6.UserId = Step5.UserId;

		return Page();
	}

	public async Task<IActionResult> OnPostStep7Async()
	{
		ViewModel.CurrentStep = 7;

		ModelState.Clear();
		TryValidateModel(Step7, nameof(Step7));

		if (!ModelState.IsValid)
		{
			return Page();
		}

		var (field, message) = Step7.MfaType switch
		{
			MfaType.Email when string.IsNullOrWhiteSpace(Step7.Email) => ("Step7.Email", "Please enter your email address"),
			MfaType.Phone when string.IsNullOrWhiteSpace(Step7.Phone) => ("Step7.Phone", "Please enter your phone number"),
			MfaType.WhatsApp when string.IsNullOrWhiteSpace(Step7.WhatsApp) => ("Step7.WhatsApp", "Please enter your WhatsApp number"),
			_ => (null, null)
		};

		if (!string.IsNullOrEmpty(message))
		{
			ModelState.AddModelError(field!, message);
			return Page();
		}

		var mfaDestination = Step7.MfaType switch
		{
			MfaType.Email => Step7.Email,
			MfaType.Phone => Step7.Phone,
			MfaType.WhatsApp => Step7.WhatsApp,
			_ => null
		};

		var user = await _profileManagementService.GetUserFromSession(Step7.UserId ?? "");
		if (user is null)
		{
			ModelState.AddModelError(string.Empty, "Invalid user.");
			return Page();
		}

		var result = await _profileManagementService.SetupMfaAsync(user!, Step7.MfaType, mfaDestination!);
		if (!result)
		{
			ModelState.AddModelError(string.Empty, "Error sending 2FA code.");
			return Page();
		}

		ViewModel.CurrentStep = 8;
		Step8.MfaType = Step7.MfaType;
		Step8.MfaDestination = mfaDestination ?? string.Empty;
		Step8.UserId = Step7.UserId;
		return Page();
	}

	public async Task<IActionResult> OnPostStep8Async()
	{
		ViewModel.CurrentStep = 8;

		var user = await _profileManagementService.GetUserFromSession(Step8.UserId ?? "");
		if (user is null)
		{
			ModelState.AddModelError(string.Empty, "Error, invalid user.");
			return Page();
		}

		if (Step8.Button == "resend-code")
		{
			var resendResult = await _loginService.SendMfaCodeAsync(user);
			if (!resendResult)
			{
				ModelState.AddModelError(string.Empty, "Failed to resend the code. Please try again.");
			}

			return Page();
		}

		ModelState.Clear();
		TryValidateModel(Step8, nameof(Step8));

		if (!ModelState.IsValid)
		{
			return Page();
		}

		var result = await _profileManagementService.VerifyMfaTokenAsync(user, Step8.TwoFactorCode ?? "");
		if (!result)
		{
			ModelState.AddModelError(string.Empty, "Invalid code. Please try again.");
			return Page();
		}

		var recoveryCode = await _profileManagementService.GenerateRecoveryCodeAsync(user) ?? string.Empty;

		return RedirectToPage("TwoFactorSuccess", new { UserId = Step8.UserId, RecoveryCode = recoveryCode });
	}

	public IActionResult OnPostStep9()
	{
		ViewModel.CurrentStep = 9;

		ModelState.Clear();
		TryValidateModel(Step9, nameof(Step9));

		if (!ModelState.IsValid)
		{
			return Page();
		}

		ViewModel.CurrentStep = 10;
		Step10.UserId = Step9.UserId;
		return Page();
	}

	public async Task<IActionResult> OnPostStep10Async()
	{
		ViewModel.CurrentStep = 10;

		ModelState.Clear();
		TryValidateModel(Step10, nameof(Step10));

		if (!ModelState.IsValid)
		{
			return Page();
		}

		var user = await _profileManagementService.GetUserFromSession(Step10.UserId ?? "");
		if (user is null)
		{
			ModelState.AddModelError(string.Empty, "Error, invalid user.");
			return Page();
		}

		var result = _profileManagementService.VerifyRecoveryCode(user!, Step10.RecoveryCode ?? "");
		if (!result)
		{
			ModelState.AddModelError(string.Empty, "Invalid recovery code.");
			return Page();
		}

		return Redirect(WebPortalLoginRedirectUrl);
	}

	private async Task<bool> GetIsSetupAccountRequired(string ctNumber)
	{
		var isSetupRequired = false;
		if (!string.IsNullOrEmpty(ctNumber))
		{
			isSetupRequired = await _loginService.GetIsSetupAccountRequired(ctNumber ?? string.Empty);
		}

		return isSetupRequired;
	}
}
