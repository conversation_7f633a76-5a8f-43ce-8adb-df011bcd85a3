@using Microsoft.AspNetCore.WebUtilities
@model HealthNet.Homecare.IdentityServer.Pages.Account.Setup.IndexModel
@{
	var loginUrl = "/Account/Login";
	if (!string.IsNullOrEmpty(Model.ReturnUrl))
	{
		loginUrl = QueryHelpers.AddQueryString(loginUrl, "ReturnUrl", Model.ReturnUrl);
	}
}

<div class="login-logo">
	<img src="~/images/email-logo.png" alt="Logo" />
</div>

<h1 class="create-account-heading">Check your email</h1>
<span class="create-account-description">
	The link can sometimes end up in the junk mail folder. If you have not received an email from us within 30 minutes please contact our customer care team at:
	<a href="mailto:@Model.EnquiriesEmail">@Model.EnquiriesEmail</a>
</span>

<form method="post" class="create-account-form">
	<input type="hidden" asp-for="ReturnUrl" />
	<input type="hidden" asp-for="ViewModel.IsPatient" />

	<div asp-validation-summary="All" class="validation-summary-errors simple-error" style="display: none"></div>
	<a href="https://outlook.office.com/mail" target="_blank" class="btn btn-primary login-button">Open email app</a>
	<div class="create-account-help-link">
		<a href="@loginUrl">
			<img src="~/images/arrow-left.svg" alt="Back" /> Back
		</a>
	</div>
</form>
