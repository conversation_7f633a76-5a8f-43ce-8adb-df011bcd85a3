using System.ComponentModel.DataAnnotations;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.Setup;

public class Step5InputModel
{
	[Required(ErrorMessage = "Password is required")]
	[StringLength(100, MinimumLength = 8, ErrorMessage = "Password must be at least 8 characters")]
	[DataType(DataType.Password)]
	public string? NewPassword { get; set; }

	[Required(ErrorMessage = "Confirm password is required")]
	[Compare("NewPassword", ErrorMessage = "Passwords do not match")]
	[DataType(DataType.Password)]
	public string? ConfirmPassword { get; set; }

	public bool IsPatient { get; set; }

	public string? UserId { get; set; }
	public string? Token { get; set; }
}
