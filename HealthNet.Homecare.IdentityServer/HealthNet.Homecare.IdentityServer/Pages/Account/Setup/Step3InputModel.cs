using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.Setup;

public class Step3InputModel
{
	[Required(ErrorMessage = "Email is required")]
	[EmailAddress(ErrorMessage = "Invalid email format")]
	public string? Email { get; set; }

	[HiddenInput]
	public string CTNumber { get; set; } = string.Empty;

	[HiddenInput]
	public DateTime? DateOfBirth { get; set; }
}
