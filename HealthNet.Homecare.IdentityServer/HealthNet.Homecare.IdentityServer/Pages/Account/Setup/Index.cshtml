@page
@model HealthNet.Homecare.IdentityServer.Pages.Account.Setup.IndexModel
@{
}

<div class="create-account-container">
	<div class="login-help-top-right">
		<a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
			<img src="~/images/help.svg" alt="Help" class="help-icon-desktop" />
			<span>Need help?</span>
		</a>
	</div>
	<div class="create-account-stepper-container" style="@(Model.ViewModel.CurrentStep == 7 ? "margin-top: 6%;" : "")">
		<div class="create-account-stepper">
			<div class="create-account-step">
				<span class="circle">
					@if (Model.ViewModel.CurrentStep <= 4)
					{
						<img src="~/images/step-active.png" alt="Active" width="24" height="24" />
					}
					else
					{
						<img src="~/images/step-check.svg" alt="Completed" width="24" height="24" />
					}
				</span>
				<span class="step-label @(Model.ViewModel.CurrentStep <= 4 ? "active" : "")">Set up account</span>
			</div>
			<div class="step-line @(Model.ViewModel.CurrentStep > 4 ? "completed" : "")"></div>
			<div class="create-account-step">
				<span class="circle">
					@if (Model.ViewModel.CurrentStep < 5)
					{
						<img src="~/images/step-next.png" alt="Next" width="24" height="24" />
					}
					else if (Model.ViewModel.CurrentStep <= 6)
					{
						<img src="~/images/step-active.png" alt="Active" width="24" height="24" />
					}
					else
					{
						<img src="~/images/step-check.svg" alt="Completed" width="24" height="24" />
					}
				</span>
				<span class="step-label @(Model.ViewModel.CurrentStep >= 5 && Model.ViewModel.CurrentStep <= 6 ? "active" : "")">Create password</span>
			</div>
			<div class="step-line @(Model.ViewModel.CurrentStep > 6 ? "completed" : "")"></div>
			<div class="create-account-step">
				<span class="circle">
					@if (Model.ViewModel.CurrentStep < 7)
					{
						<img src="~/images/step-next.png" alt="Next" width="24" height="24" />
					}
					else
					{
						<img src="~/images/step-active.png" alt="Active" width="24" height="24" />
					}
				</span>
				<span class="step-label @(Model.ViewModel.CurrentStep >= 7 ? "active" : "")">
					<span class="d-none d-md-inline">Set up two-factor authentication @(Model.ViewModel.IsPatient ? "(optional)" : "")</span>
					<span class="d-inline d-md-none">Select authentication @(Model.ViewModel.IsPatient ? "(optional)" : "")</span>
				</span>
			</div>
		</div>
	</div>
	<div class="login-card">
		<div class="login-help-top-right-mobile">
			<a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
				<img src="~/images/help-blue.svg" alt="Help" class="help-icon-mobile" />
				<span>Help</span>
			</a>
		</div>
		@if (Model.ViewModel.CurrentStep == 1)
		{
			<partial name="_Step1_CTNumber" model="Model" />
		}
		else if (Model.ViewModel.CurrentStep == 2)
		{
			<partial name="_Step2_DateOfBirth" model="Model" />
		}
		else if (Model.ViewModel.CurrentStep == 3)
		{
			<partial name="_Step3_Email" model="Model" />
		}
		else if (Model.ViewModel.CurrentStep == 4)
		{
			<partial name="_Step4_CheckEmail" model="Model" />
		}
		else if (Model.ViewModel.CurrentStep == 5)
		{
			<partial name="_Step5_CreatePassword" model="Model" />
		}
		else if (Model.ViewModel.CurrentStep == 6)
		{
			<partial name="_Step6_PasswordCreated" model="Model" />
		}
		else if (Model.ViewModel.CurrentStep == 7)
		{
			<partial name="_Step7_Select2FA" model="Model" />
		}
		else if (Model.ViewModel.CurrentStep == 8)
		{
			<partial name="_Step8_Enter2FACode" model="Model" />
		}
		else if (Model.ViewModel.CurrentStep == 9)
		{
			<partial name="_Step9_RecoveryCode" model="Model" />
		}
		else if (Model.ViewModel.CurrentStep == 10)
		{
			<partial name="_Step10_EnterRecoveryCode" model="Model" />
		}
	</div>
</div>

@section scripts {
	@if (Model.ViewModel.CurrentStep == 2)
	{
		@Html.ScriptWithNonce(Url.Content("~/js/pages/create-account-birthdate-step.js"))
	}
	else if (Model.ViewModel.CurrentStep == 7)
	{
		@Html.ScriptWithNonce(Url.Content("~/js/pages/setup-2fa-step.js"))
	}
	else if (Model.ViewModel.CurrentStep == 8)
	{
		@Html.ScriptWithNonce(Url.Content("~/js/pages/2fe-step.js"))
	}
	else if (Model.ViewModel.CurrentStep == 9)
	{
		@Html.ScriptWithNonce(Url.Content("~/js/pages/recovery-code-step.js"))
	}
}