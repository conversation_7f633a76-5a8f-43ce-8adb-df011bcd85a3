using HealthNet.Homecare.IdentityServer.Domain.Enums;
using Microsoft.AspNetCore.Mvc;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.Setup;

public class Step8InputModel
{
	[BindProperty]
	public string TwoFactorCode { get; set; } = string.Empty;

	public string? MfaDestination { get; set; } = string.Empty;

	public MfaType MfaType { get; set; } = MfaType.Unknown;

	public string? UserId { get; set; }

	[BindProperty]
	public string? Button { get; set; }
}
