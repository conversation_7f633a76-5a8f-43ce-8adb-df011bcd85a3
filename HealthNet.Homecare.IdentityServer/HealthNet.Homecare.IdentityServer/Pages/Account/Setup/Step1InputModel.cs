using System.ComponentModel.DataAnnotations;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.Setup;

public class Step1InputModel
{
	[Required(ErrorMessage = "CT number is required")]
	[RegularExpression(@"^CT\d{6}$", ErrorMessage = "CT number format must be CT followed by 6 digits")]
	public string? CTNumber { get; set; }

	public bool IsSetupRequired { get; set; }

	public bool IsSetupRequiredMessageSeen { get; set; }
}
