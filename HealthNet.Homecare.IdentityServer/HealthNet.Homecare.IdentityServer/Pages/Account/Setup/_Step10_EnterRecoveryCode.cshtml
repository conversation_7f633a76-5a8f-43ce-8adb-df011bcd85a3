@model HealthNet.Homecare.IdentityServer.Pages.Account.Setup.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input
@using Microsoft.AspNetCore.WebUtilities
@{
	var previousStepUrl = "/Account/Setup/?Step=7";
	if (!string.IsNullOrEmpty(Model.Step10.UserId))
	{
		previousStepUrl = QueryHelpers.AddQueryString(previousStepUrl, "UserId", Model.Step10.UserId);
	}
}

<div class="login-logo">
	<img src="~/images/password-logo.png" alt="Logo" />
</div>
<h1 class="create-account-heading">Enter your recovery code</h1>
<span class="create-account-description">Enter the x-digit recovery code you received when you set up your account.</span>
<form method="post" asp-page-handler="Step10" class="create-account-form">
	<input type="hidden" asp-for="ReturnUrl" />
	<input type="hidden" asp-for="Step10.UserId" />

	<div class="create-account-email-container">
			<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
			 model="@(new InputModel {
								Id = "recoveryCode",
								Name = "Step10.RecoveryCode",
								Type = "text",
								Value = Model.Step10?.RecoveryCode ?? "",
								Placeholder = "Enter recovery code",
								IsRequired = false
							})" />
	</div>
	<div asp-validation-summary="All" class="validation-summary-errors simple-error" style="display: @(Model.ModelState.ErrorCount > 0 ? "block" : "none")"></div>
	<partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
			 model="@(new ButtonModel {
                                Text = "Verify",
                                ButtonType = "submit",
                                ButtonStyle = "primary",
                                AdditionalClasses = "login-button",
                                Name = "Input.Button",
								RequiredFields = new[] { "recoveryCode" }
                            })" />
	<div class="create-account-help-link">
		<a href="@previousStepUrl"><img src="~/images/arrow-left.svg" alt="Back" /> Back</a>
	</div>
</form> 