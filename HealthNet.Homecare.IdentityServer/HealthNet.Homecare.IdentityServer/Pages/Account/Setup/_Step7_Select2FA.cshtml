@model HealthNet.Homecare.IdentityServer.Pages.Account.Setup.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input
@using Microsoft.AspNetCore.WebUtilities
@{
	var previousStepUrl = "/Account/Setup/?Step=6";
	if (!string.IsNullOrEmpty(Model.Step7.UserId))
	{
		previousStepUrl = QueryHelpers.AddQueryString(previousStepUrl, "UserId", Model.Step7.UserId);
	}
}

<div class="login-logo">
	<img src="~/images/2fa-logo.png" alt="Logo" />
</div>
<h1 class="create-account-heading">Set up two-factor authentication</h1>
<span class="create-account-description">Your password has been successfully created. You're almost done setting up your account. Select a method of authentication to make your account extra safe.</span>
<form method="post" asp-page-handler="Step7" class="create-account-form">
	<input type="hidden" asp-for="ViewModel.CurrentStep" />
	<input type="hidden" asp-for="ViewModel.IsPatient" />
	<input type="hidden" asp-for="ReturnUrl" />
	<input type="hidden" asp-for="Step7.UserId" />

	<div class="authentication-methods">
		<span class="select-method-label">Select one method for authentication</span>
		<div class="radio-group">
			<div class="radio-item">
				<input type="radio" id="emailRadio" name="Step7.AuthMethod" value="email" />
				<label for="emailRadio">Email</label>
				<span class="method-description">Send a code to your email</span>
				<div class="email-input-container">
					<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
							 model="@(new InputModel {
								Id = "emailInput",
								Name = "Step7.Email",
								Value = Model.Step7?.Email ?? "",
								Placeholder = "Enter email address",
								AdditionalClasses = "auth-input",
								IsRequired = false
							})" />
				</div>
			</div>
		</div>
		<div class="radio-group">
			<div class="radio-item">
				<input type="radio" id="smsRadio" name="Step7.AuthMethod" value="sms" />
				<label for="smsRadio">SMS</label>
				<span class="method-description">Send a code to your phone</span>
				<div class="sms-input-container">
					<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
							 model="@(new InputModel {
							Id = "phoneInput",
							Name = "Step7.Phone",
							Type = "tel",
							Value = Model.Step7?.Phone ?? "",
							Placeholder = "Enter phone number",
							AdditionalClasses = "auth-input",
							IsRequired = false
						})" />
				</div>
			</div>
		</div>
		<div class="radio-group">
			<div class="radio-item">
				<input type="radio" id="whatsappRadio" name="Step7.AuthMethod" value="whatsapp" />
				<label for="whatsappRadio">WhatsApp</label>
				<span class="method-description">Send a code to your WhatsApp</span>
				<div class="whatsapp-input-container">
					<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
							 model="@(new InputModel {
							Id = "whatsappInput",
							Name = "Step7.WhatsApp",
							Type = "tel",
							Value = Model.Step7?.WhatsApp ?? "",
							Placeholder = "Enter phone number",
							AdditionalClasses = "auth-input",
							IsRequired = false
						})" />
				</div>
			</div>
		</div>
	</div>
	<div asp-validation-summary="All" class="validation-summary-errors simple-error" style="display: @(Model.ModelState.ErrorCount > 0 ? "block" : "none")"></div>
	<partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
			 model="@(new ButtonModel {
				Text = "Confirm",
				ButtonType = "submit",
				ButtonStyle = "primary",
				AdditionalClasses = "login-button",
				Name = "action",
				Value = "next"
			})" />

	<div class="help-link">
		Need help? <a href="mailto:@Model.EnquiriesEmail">@Model.EnquiriesEmail</a>
	</div>
	<div class="create-account-help-link mt-3">
		<a href="@previousStepUrl">
			<img src="~/images/arrow-left.svg" alt="Back" /> Back
		</a>
	</div>
</form>

