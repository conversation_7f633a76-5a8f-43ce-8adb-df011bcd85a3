using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.Login.TwoFactor;

[AllowAnonymous]
public class SuccessModel : PageModel
{
	[BindProperty(SupportsGet = true)]
	public string ReturnUrl { get; set; } = string.Empty;

	public void OnGet(string returnUrl)
	{
		ReturnUrl = returnUrl ?? "~/";
	}
}
