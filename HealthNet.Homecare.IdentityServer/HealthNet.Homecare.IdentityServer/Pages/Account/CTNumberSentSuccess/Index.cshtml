@page
@model HealthNet.Homecare.IdentityServer.Pages.Account.CTNumberSentSuccess.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.But<PERSON>

@{
	ViewData["Title"] = "Link Sent";
	var loginUrl = $"/Account/Login?ReturnUrl={Uri.EscapeDataString(Model.ReturnUrl ?? "")}";
}

<div class="login-container">
	<div class="login-help-top-right">
		<a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
			<img src="~/images/help.svg" alt="Help" class="help-icon-desktop" />
			<span>Need help?</span>
		</a>
	</div>
	<div class="login-card">
		<div class="login-help-top-right-mobile">
			<a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
				<img src="~/images/help-blue.svg" alt="Help" class="help-icon-mobile" />
				<span>Help</span>
			</a>
		</div>

		<div class="login-logo">
			<img src="~/images/email-logo.png" alt="Logo" />
		</div>

		<h1 class="create-account-heading">Check your email</h1>
		<span class="create-account-description">
			The link can sometimes end up in the junk mail folder. If you have set up an account with us but not received an email within 30 minutes please contact our customer care team at:
			<a href="mailto:@Model.EnquiriesEmail">@Model.EnquiriesEmail</a>
		</span>

		<form method="post" class="create-account-form">
			<div asp-validation-summary="All" class="validation-summary-errors simple-error" style="display: none"></div>
			<a href="https://outlook.office.com/mail" target="_blank" class="btn btn-primary login-button">Open email app</a>
			<div class="create-account-help-link">
				<a href="@loginUrl">
					<img src="~/images/arrow-left.svg" alt="Back" /> Back
				</a>
			</div>
		</form>
	</div>
</div>
