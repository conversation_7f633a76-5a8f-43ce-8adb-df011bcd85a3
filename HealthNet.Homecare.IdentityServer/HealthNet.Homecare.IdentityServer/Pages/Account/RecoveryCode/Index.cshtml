@page
@model HealthNet.Homecare.IdentityServer.Pages.Account.RecoveryCode.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input
@using Microsoft.AspNetCore.WebUtilities
@{
	var backUrl = QueryHelpers.AddQueryString("/Account/TwoFactor", new Dictionary<string, string?>()
	{
		{"UserId", Model.Input.UserId},
		{"ReturnUrl", Model.Input.ReturnUrl},
		{"RememberMe", Model.Input.RememberMe.ToString()},
	});
}

<div class="login-container">
	<div class="login-card">
		<div class="login-help-top-right-mobile">
			<a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
				<img src="~/images/help-blue.svg" alt="Help" class="help-icon-mobile" />
				<span>Help</span>
			</a>
		</div>
		<div class="login-logo">
			<img src="~/images/password-logo.png" alt="Logo" />
		</div>
		<h1 class="create-account-heading">Enter your recovery code</h1>
		<span class="create-account-description">Enter the x-digit recovery code you received when you set up your account.</span>
		<form method="post" class="create-account-form">
			<input type="hidden" asp-for="Input.ReturnUrl" />
			<input type="hidden" asp-for="Input.UserId" />

			<div class="create-account-email-container">
				<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
						 model="@(new InputModel {
								Id = "recoveryCode",
								Name = "Input.RecoveryCode",
								Type = "text",
								Value = Model.Input?.RecoveryCode ?? "",
								Placeholder = "Enter recovery code",
								IsRequired = false
							})" />
			</div>
			<div asp-validation-summary="All" class="validation-summary-errors simple-error" style="display: @(Model.ModelState.ErrorCount > 0 ? "block" : "none")"></div>
			<partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
					 model="@(new ButtonModel {
                                Text = "Verify",
                                ButtonType = "submit",
                                ButtonStyle = "primary",
                                AdditionalClasses = "login-button",
                                Name = "Input.Button",
								RequiredFields = new[] { "recoveryCode" }
                            })" />
			<div class="create-account-help-link">
				<a href="@backUrl"><img src="~/images/arrow-left.svg" alt="Back" /> Back</a>
			</div>
		</form>
	</div>
</div>