using HealthNet.Homecare.IdentityServer.Infrastructure.Configurations;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Options;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.SetupLinkSentSuccess;

public class IndexModel(IOptions<SystemEmailsOptions> options) : PageModel
{
	[BindProperty(SupportsGet = true)]
	public string ReturnUrl { get; set; } = string.Empty;
	public string EnquiriesEmail { get; set; } = options.Value.Enquiries;
}
