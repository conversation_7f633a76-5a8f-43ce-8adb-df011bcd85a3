@page
@model HealthNet.Homecare.IdentityServer.Pages.Account.ForgotCTNumber.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input
@{
	var loginUrl = $"/Account/Login?ReturnUrl={Uri.EscapeDataString(Model.ReturnUrl ?? "")}";
}

<div class="login-container">
	<div class="login-help-top-right">
		<a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
			<img src="~/images/help.svg" alt="Help" class="help-icon-desktop" />
			<span>Need help?</span>
		</a>
	</div>
	<div class="login-card">
		<div class="login-help-top-right-mobile">
			<a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
				<img src="~/images/help-blue.svg" alt="Help" class="help-icon-mobile" />
				<span>Help</span>
			</a>
		</div>
		<div class="login-logo">
			<img src="~/images/email-logo.png" alt="Logo" />
		</div>
		<h1 class="create-account-heading">Retrieve CT Number</h1>
		<span class="create-account-description">Enter your email address and we’ll send you a link to retrieve your CT number.</span>
		<form method="post" class="create-account-form" autocomplete="off">
			<input type="hidden" asp-for="ReturnUrl" />

			<div class="create-account-form-group">
				<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
						 model="@(new InputModel {
                             Id = "email",
                             Name = "Input.Email",
                             Label = "Email address",
                             Autocomplete = "off"
                         })" />
			</div>
			<div asp-validation-summary="ModelOnly" class="validation-summary-errors simple-error"></div>
			<partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
					 model="@(new ButtonModel {
                         Text = "Send link to email",
                         ButtonType = "submit",
                         ButtonStyle = "primary",
                         AdditionalClasses = "login-button",
                         Name = "Input.Button",
                         Value = "retrieve",
						 RequiredFields = new[] { "email" }
                     })" />

		</form>
		<div class="create-account-help-link">
			<div class="create-account-help-link">
				<a href="@loginUrl"><img src="~/images/arrow-left.svg" alt="Back" /> Back</a>
			</div>
		</div>
	</div>
</div>

@section scripts {
	@Html.ScriptWithNonce(Url.Content("~/js/pages/forgot-ct-number.js"))
}
