using HealthNet.Homecare.IdentityServer.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.ForgotCTNumber;

[AllowAnonymous]
public class IndexModel(ProfileManagementService profileManagementService) : PageModel
{
	private readonly ProfileManagementService _profileManagementService = profileManagementService;

	[BindProperty]
	public InputModel Input { get; set; } = new();

	[BindProperty(SupportsGet = true)]
	public string ReturnUrl { get; set; } = string.Empty;

	public async Task<IActionResult> OnPostAsync()
	{
		if (!ModelState.IsValid)
		{
			return Page();
		}

		var loginPath = Url.Page("/Account/Login/Index", new { ReturnUrl });
		var fullLoginUrl = $"{Request?.Scheme}://{Request?.Host}{loginPath}";

		var result = await _profileManagementService.ForgotUsernameAsync(Input.Email, fullLoginUrl);
		if (!result)
		{
			ModelState.AddModelError(string.Empty, "Failed to send CT number. Please try again later.");
			return Page();
		}

		return RedirectToPage("/Account/CTNumberSentSuccess/Index", new { ReturnUrl });
	}
}
