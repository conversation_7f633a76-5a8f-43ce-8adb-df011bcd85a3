@page
@model HealthNet.Homecare.IdentityServer.Pages.Account.PasswordResetSuccess.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button


@{
    ViewData["Title"] = "Password Created";
}

<div class="login-container">
    <div class="login-help-top-right">
        <a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
            <img src="~/images/help.svg" alt="Help" class="help-icon-desktop" />
            <span>Need help?</span>
        </a>
    </div>
    <div class="login-card">
        <div class="login-help-top-right-mobile">
            <a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
                <img src="~/images/help-blue.svg" alt="Help" class="help-icon-mobile" />
                <span>Help</span>
            </a>
        </div>

        <div class="login-logo">
            <img src="~/images/success-icon.svg" alt="Logo" />
        </div>

        <h1 class="create-account-heading">Password Created</h1>
        <span class="create-account-description">
            Your password has been successfully created.
        </span>

        <form method="post" class="create-account-form">
            <input type="hidden" asp-for="ReturnUrl" />

            <partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
                     model="@(new ButtonModel {
                        Text = "Go to Log in",
                        ButtonType = "submit",
                        ButtonStyle = "primary",
                        AdditionalClasses = "login-button"
                    })" />
        </form>
    </div>
</div>