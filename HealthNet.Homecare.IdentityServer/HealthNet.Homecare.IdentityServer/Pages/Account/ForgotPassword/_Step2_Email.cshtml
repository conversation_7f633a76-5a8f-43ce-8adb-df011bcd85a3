@model HealthNet.Homecare.IdentityServer.Pages.Account.ForgotPassword.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input
@{
    var step1Url = $"/Account/ForgotPassword?ReturnUrl={Uri.EscapeDataString(Model.ReturnUrl ?? "")}&CTNumber={Uri.EscapeDataString(Model.Step2.CTNumber)}&Email={Uri.EscapeDataString(Model.Step2.Email ?? "")}&step=1";
}

<span class="create-account-description">Enter your email address and we’ll send you a link to reset password.</span>
<form method="post" asp-page-handler="Step2" class="create-account-form">
	<input type="hidden" asp-for="ViewModel.CurrentStep" />
    <input type="hidden" asp-for="Step2.CTNumber" />
    <input type="hidden" asp-for="ReturnUrl" />

	@if (Model.IsLinkExpired)
	{
		<div class="alert-container warning">
			<div class="alert-icon">
				<img src="~/images/alert-circle-yellow.svg" alt="Warning" />
			</div>
			<div class="alert-content">
				<div class="alert-title">
					<div>The link expired.</div>
				</div>
				<div class="alert-message">Please request a new link to your email.</div>
			</div>
		</div>
	}
	
	<div class="create-account-email-container">
		<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
				 model="@(new InputModel {
                 Id = "email",
                 Name = "Step2.Email",
                 Label = "Email address",
                 Value = Model.Step2.Email ?? "",
                 Autocomplete = "off"
             })" />
		<div asp-validation-summary="All" class="validation-summary-errors simple-error" style="display: @(Model.ModelState.ErrorCount > 0 ? "block" : "none")"></div>
	</div>
	<partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
			 model="@(new ButtonModel {
                 Text = "Send link to email",
                 ButtonType = "submit",
                 ButtonStyle = "primary",
                 Name = "Input.Button",
                 AdditionalClasses = "login-button",
                 Value = "continue",
                 RequiredFields = new[] { "email" }
             })" />
	<div class="create-account-help-link">
        <a href="@step1Url" id="back-link"><img src="~/images/arrow-left.svg" alt="Back" /> Back</a>
	</div>
</form>
