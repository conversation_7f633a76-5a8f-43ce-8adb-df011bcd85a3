@model HealthNet.Homecare.IdentityServer.Pages.Account.ForgotPassword.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input
@{
	var loginUrl = $"/Account/Login?ReturnUrl={Uri.EscapeDataString(Model.ReturnUrl ?? "")}";
	var forgotCTNumberUrl = $"/Account/ForgotCTNumber?ReturnUrl={Uri.EscapeDataString(Model.ReturnUrl ?? "")}";
}

<span class="create-account-description">Start by entering your CT number. This can be found on any delivery note or in your welcome email. It starts with CT followed by 6 digits. (e.g CT123456)</span>
<form method="post" asp-page-handler="Step1" class="create-account-form">
	<input type="hidden" asp-for="ViewModel.CurrentStep" />
	<input type="hidden" asp-for="ReturnUrl" />
	<input type="hidden" asp-for="Step1.Email" />

	<div class="create-account-email-container">
		<partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
				 model="@(new InputModel {
                 Id = "ctNumber",
                 Name = "Step1.CTNumber",
                 Label = "CT number",
                 Value = Model.Step1.CTNumber ?? "",
                 Autofocus = true,
                 AdditionalClasses = "ct-number-input",
             })" />
		<div class="forgot-ct-number forgot-link"><a href="@forgotCTNumberUrl">Forgot CT number?</a></div>
		<div asp-validation-summary="All" class="validation-summary-errors simple-error" style="display: @(Model.ModelState.ErrorCount > 0 ? "block" : "none")"></div>
	</div>
	<partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
			 model="@(new ButtonModel {
                 Text = "Continue",
                 ButtonType = "submit",
                 ButtonStyle = "primary",
                 AdditionalClasses = "login-button",
                 Name = "action",
                 Value = "next",
				 IsDisabled = true,
                 RequiredFields = new[] { "ctNumber" }
             })" />
	<div class="create-account-help-link">
		<div class="create-account-help-link">
			<a href="@loginUrl"><img src="~/images/arrow-left.svg" alt="Back" /> Back</a>
		</div>
	</div>
</form>

