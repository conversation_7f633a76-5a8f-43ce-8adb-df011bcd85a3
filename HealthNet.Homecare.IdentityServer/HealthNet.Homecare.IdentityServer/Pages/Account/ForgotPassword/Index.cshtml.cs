using HealthNet.Homecare.IdentityServer.Domain.Common;
using HealthNet.Homecare.IdentityServer.Infrastructure.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.Web;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.ForgotPassword;

[AllowAnonymous]
public class IndexModel(ProfileManagementService profileManagementService) : PageModel
{
	private readonly ProfileManagementService _profileManagementService = profileManagementService;

	[BindProperty]
	public Step1InputModel Step1 { get; set; } = new();

	[BindProperty]
	public Step2InputModel Step2 { get; set; } = new();

	[BindProperty]
	public ViewModel ViewModel { get; set; } = new();

	[BindProperty(SupportsGet = true)]
	public string ReturnUrl { get; set; } = string.Empty;

	public bool IsLinkExpired { get; set; } = false;

	public void OnGet(int? step = null, string? CTNumber = null, string? Email = null, bool IsLinkExpired = false, string? ReturnUrl = null)
	{
		ViewModel.CurrentStep = step ?? 1;
		this.IsLinkExpired = IsLinkExpired;
		this.ReturnUrl = ReturnUrl ?? string.Empty;

		if (!string.IsNullOrEmpty(CTNumber))
		{
			Step1.CTNumber = CTNumber;
			Step2.CTNumber = CTNumber;
		}

		if (!string.IsNullOrEmpty(Email))
		{
			Step1.Email = Email;
			Step2.Email = Email;
		}
	}

	public IActionResult OnPostStep1()
	{
		ModelState.Clear();
		TryValidateModel(Step1, nameof(Step1));

		if (!ModelState.IsValid)
		{
			ViewModel.CurrentStep = 1;
			return Page();
		}

		ViewModel.CurrentStep = 2;
		Step2.CTNumber = Step1.CTNumber!;
		Step2.Email = Step1.Email;
		return Page();
	}

	public async Task<IActionResult> OnPostStep2Async()
	{
		ViewModel.CurrentStep = 2;

		ModelState.Clear();
		TryValidateModel(Step2, nameof(Step2));

		if (!ModelState.IsValid)
		{
			return Page();
		}

		var result = await _profileManagementService.ForgotPasswordAsync(
			Step2.CTNumber,
			Step2.Email!,
			GetBaseUrlFromReturnUrl(ReturnUrl)
		);
		if (!result)
		{
			ModelState.AddModelError("Step2.Email", AppError.Message.InvalidUserNumberAndEmail);
			return Page();
		}

		return RedirectToPage("../ResetLinkSentSuccess/Index", new { ReturnUrl });
	}

	private static string? GetBaseUrlFromReturnUrl(string returnUrl)
	{
		var queryStart = returnUrl.IndexOf('?');
		if (queryStart == -1)
		{
			return null;
		}

		var query = returnUrl.Substring(queryStart + 1);
		var parameters = HttpUtility.ParseQueryString(query);

		var encodedRedirectUri = parameters["redirect_uri"];
		if (string.IsNullOrEmpty(encodedRedirectUri))
		{
			return null;
		}

		var decodedUri = HttpUtility.UrlDecode(encodedRedirectUri);
		if (!Uri.TryCreate(decodedUri, UriKind.Absolute, out var uri))
		{
			return null;
		}

		return $"{uri.Scheme}://{uri.Host}" + (uri.IsDefaultPort ? "" : $":{uri.Port}");
	}
}
