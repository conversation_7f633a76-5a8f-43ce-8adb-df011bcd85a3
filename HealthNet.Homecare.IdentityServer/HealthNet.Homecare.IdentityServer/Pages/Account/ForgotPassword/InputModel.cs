using System.ComponentModel.DataAnnotations;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.ForgotPassword;

public class InputModel
{
	[Required(ErrorMessage = "CT number is required")]
	[RegularExpression(@"^CT\d{6}$", ErrorMessage = "CT number format must be CT followed by 6 digits")]
	public string? CTNumber { get; set; }

	[Required(ErrorMessage = "Email is required")]
	[EmailAddress(ErrorMessage = "Invalid email format")]
	public string? Email { get; set; }
}
