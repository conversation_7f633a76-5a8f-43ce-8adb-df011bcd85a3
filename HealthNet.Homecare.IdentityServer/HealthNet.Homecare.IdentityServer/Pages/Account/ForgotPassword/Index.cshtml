@page
@model HealthNet.Homecare.IdentityServer.Pages.Account.ForgotPassword.IndexModel
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input

<div class="login-container">
	<div class="login-help-top-right">
		<a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
			<img src="~/images/help.svg" alt="Help" class="help-icon-desktop" />
			<span>Need help?</span>
		</a>
	</div>
	<div class="login-card">
		<div class="login-help-top-right-mobile">
			<a href="https://www.healthnethomecare.co.uk/healthnet-digital-tools/" target="_blank">
				<img src="~/images/help-blue.svg" alt="Help" class="help-icon-mobile" />
				<span>Help</span>
			</a>
		</div>
		<div class="login-logo">
			<img src="~/images/password-logo.png" alt="Logo" />
		</div>
		<h1 class="create-account-heading">Reset password</h1>

		@if (Model.ViewModel.CurrentStep == 1)
		{
			<partial name="_Step1_CTNumber" model="Model" />
		}
		else if (Model.ViewModel.CurrentStep == 2)
		{
			<partial name="_Step2_Email" model="Model" />
		}
	</div>
</div>

@section scripts {
	@Html.ScriptWithNonce(Url.Content("~/js/pages/forgot-password.js"))
}
