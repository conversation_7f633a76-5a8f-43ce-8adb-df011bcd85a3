using HealthNet.Homecare.IdentityServer.Infrastructure.Configurations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Options;

namespace HealthNet.Homecare.IdentityServer.Pages.Account.ResetLinkSentSuccess;

[AllowAnonymous]
public class IndexModel : PageModel
{
	public IndexModel(IOptions<SystemEmailsOptions> options)
	{
		EnquiriesEmail = options.Value.Enquiries;
	}

	[BindProperty(SupportsGet = true)]
	public string ReturnUrl { get; set; } = string.Empty;
	public string EnquiriesEmail { get; set; } = string.Empty;

	public void OnGet()
	{
		// No additional logic needed for this page
	}
}