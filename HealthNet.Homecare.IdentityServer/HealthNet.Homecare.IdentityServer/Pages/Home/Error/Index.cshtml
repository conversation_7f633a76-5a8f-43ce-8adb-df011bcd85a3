@page
@model HealthNet.Homecare.IdentityServer.Pages.Error.Index

<div class="container my-5 error-page">
    <div class="lead mb-4">
        <h1 class="text-danger">Error</h1>
        <p class="text-muted">Sorry, something went wrong.</p>
    </div>

    <div class="alert alert-danger p-4 shadow-sm">
        <h5 class="mb-3">Details</h5>

        @if (Model.View.Error != null)
        {
            <p class="mb-2"><strong>Error Code:</strong> <code>@Model.View.Error.Error</code></p>

            @if (Model.View.Error.ErrorDescription != null)
            {
                <div class="mb-3">
                    <strong>Description:</strong>
                    <pre class="error-description">@Model.View.Error.ErrorDescription</pre>
                </div>
            }
        }
        else
        {
            <p>No error details available.</p>
        }

        @if (Model?.View?.Error?.RequestId != null)
        {
            <div class="text-muted mt-3">
                <small>Request Id: <code>@Model.View.Error.RequestId</code></small>
            </div>
        }
    </div>
</div>
