using Duende.IdentityServer.Services;
using HealthNet.Homecare.IdentityServer.Abstractions.Services;
using HealthNet.Homecare.IdentityServer.Configurations;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Options;

namespace HealthNet.Homecare.IdentityServer.Pages.Error;

[AllowAnonymous]
public class Index(
    IErrorStore errorStore,
    IOptions<ErrorOptions> errorOptions,
    IIdentityServerInteractionService interaction
) : PageModel
{
    private readonly IErrorStore _errorStore = errorStore;
    private readonly ErrorOptions _errorOptions = errorOptions.Value;
    private readonly IIdentityServerInteractionService _interaction = interaction;

    public ViewModel View { get; set; } = new();

    public async Task OnGet(string? errorId)
    {
        // retrieve error details from identityserver
        var message = await _interaction.GetErrorContextAsync(errorId);
        message ??= await _errorStore.RetrieveAsync(errorId ?? "");

        if (message is null)
        {
            return;
        }

        View.Error = message;

        if (!_errorOptions.ShowErrorDescription)
        {
            message.ErrorDescription = null;
        }
    }
}
