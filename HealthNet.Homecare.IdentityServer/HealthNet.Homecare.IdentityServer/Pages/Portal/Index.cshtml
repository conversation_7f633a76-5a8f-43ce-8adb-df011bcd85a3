@page
@model HealthNet.Homecare.IdentityServer.Pages.Portal.Index

<div class="container my-5">
    <div class="mb-4">
        <h1 class="mb-3">Client Application Portal</h1>
        <p class="lead">
            This portal contains links to client applications that are configured
            with an <code>InitiateLoginUri</code>, which enables
            <a href="https://openid.net/specs/openid-connect-core-1_0.html#ThirdPartyInitiatedLogin" target="_blank" rel="noopener noreferrer">
                third-party initiated login
            </a>.
        </p>
    </div>

    @if (!Model.Clients.Any())
    {
        <div class="alert alert-warning text-center">
            You do not have any clients configured with an <code>InitiateLoginUri</code>.
        </div>
    }
    else
    {
        <div class="card shadow-sm">
            <div class="card-header">
                <h5 class="mb-0">Available Applications</h5>
            </div>
            <ul class="list-group list-group-flush">
                @foreach (var client in Model.Clients)
                {
                    <li class="list-group-item">
                        <a href="@client.InitiateLoginUri" class="text-decoration-none">
                            @client.LinkText
                        </a>
                    </li>
                }
            </ul>
        </div>
    }
</div>
