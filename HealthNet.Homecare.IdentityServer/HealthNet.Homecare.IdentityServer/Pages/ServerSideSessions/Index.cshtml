@page
@model HealthNet.Homecare.IdentityServer.Pages.ServerSideSessions.IndexModel

<div class="container my-5">
    <h2 class="mb-4">User Sessions</h2>

    @if (Model.UserSessions != null)
    {
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                @if (Model.UserSessions.HasPrevResults)
                {
                    <a class="btn btn-outline-primary" asp-page="/ServerSideSessions/Index"
                       asp-route-prev="true"
                       asp-route-token="@Model.UserSessions.ResultsToken"
                       asp-route-DisplayNameFilter="@Model.DisplayNameFilter"
                       asp-route-SubjectIdFilter="@Model.SubjectIdFilter"
                       asp-route-SessionIdFilter="@Model.SessionIdFilter">
                        &larr; Prev
                    </a>
                }
            </div>
            <div>
                @if (Model.UserSessions.HasNextResults)
                {
                    <a class="btn btn-outline-primary" asp-page="/ServerSideSessions/Index"
                       asp-route-token="@Model.UserSessions.ResultsToken"
                       asp-route-DisplayNameFilter="@Model.DisplayNameFilter"
                       asp-route-SubjectIdFilter="@Model.SubjectIdFilter"
                       asp-route-SessionIdFilter="@Model.SessionIdFilter">
                        Next &rarr;
                    </a>
                }
            </div>
        </div>

        <form method="get" class="row g-3 mb-4 align-items-end">
            <div class="col-md-4">
                <label asp-for="DisplayNameFilter" class="form-label">Name</label>
                <input type="search" asp-for="DisplayNameFilter" class="form-control" autofocus />
            </div>
            <div class="col-md-4">
                <label asp-for="SessionIdFilter" class="form-label">Session Id</label>
                <input type="search" asp-for="SessionIdFilter" class="form-control" />
            </div>
            <div class="col-md-4">
                <label asp-for="SubjectIdFilter" class="form-label">Subject Id</label>
                <input type="search" asp-for="SubjectIdFilter" class="form-control" />
            </div>
            <div class="col-md-12 text-end">
                <button type="submit" class="btn btn-success">Filter</button>
            </div>
        </form>

        @if (Model.UserSessions.TotalCount.HasValue)
        {
            <div class="text-center mb-3 text-muted">
                @if (Model.UserSessions.CurrentPage.HasValue && Model.UserSessions.TotalPages.HasValue)
                {
                    <span>Total Results: @Model.UserSessions.TotalCount, Page @Model.UserSessions.CurrentPage of @Model.UserSessions.TotalPages</span>
                }
                else
                {
                    <span>Total Results: @Model.UserSessions.TotalCount</span>
                }
            </div>
        }

        @if (Model.UserSessions.Results.Any())
        {
            <div class="table-responsive">
                <table class="table table-bordered table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th>Subject Id</th>
                            <th>Session Id</th>
                            <th>Display Name</th>
                            <th>Created</th>
                            <th>Expires</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var session in Model.UserSessions.Results)
                        {
                            <tr>
                                <td>@session.SubjectId</td>
                                <td>@session.SessionId</td>
                                <td>@session.DisplayName</td>
                                <td>@session.Created</td>
                                <td>@session.Expires</td>
                                <td>
                                    <form method="post" class="m-0">
                                        <input type="hidden" name="SessionId" value="@session.SessionId" />
                                        <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                    </form>
                                </td>
                            </tr>
                            <tr class="table-secondary">
                                <td colspan="6">
                                    <strong>Clients:</strong>
                                    @if (session.ClientIds?.Any() == true)
                                    {
                                        @string.Join(", ", session.ClientIds)
                                    }
                                    else
                                    {
                                        <span class="text-muted">None</span>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="alert alert-info text-center">No user sessions found.</div>
        }
    }
    else
    {
        <div class="alert alert-warning">
            Server-side sessions are not enabled.
            To enable, use <code>AddServerSideSessions()</code> in your IdentityServer configuration.
            See the <a href="https://docs.duendesoftware.com/identityserver/v7/ui/server_side_sessions" target="_blank">documentation</a> for more information.
        </div>
    }
</div>
