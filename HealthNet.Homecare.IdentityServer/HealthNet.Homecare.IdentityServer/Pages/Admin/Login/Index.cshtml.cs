using Duende.IdentityServer;
using HealthNet.Homecare.IdentityServer.Domain.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace HealthNet.Homecare.IdentityServer.Pages.Admin.Login;

public class IndexModel(SignInManager<ApplicationUser> signInManager) : PageModel
{
    private readonly SignInManager<ApplicationUser> _signInManager = signInManager;

    [BindProperty]
    public InputModel Input { get; set; } = default!;

    public void OnGet() { }

    public async Task<IActionResult> OnPostAsync()
    {
        var user = await _signInManager.UserManager.FindByNameAsync(Input.Username);
        if (user is null)
        {
            ModelState.AddModelError(string.Empty, "Invalid login or password");
            return Page();
        }

        var result = await _signInManager.PasswordSignInAsync(Input.Username, Input.Password, false, false);
        if (!result.Succeeded)
        {
            ModelState.AddModelError(string.Empty, "Invalid login or password");
            return Page();
        }

        var userPrincipal = await _signInManager.CreateUserPrincipalAsync(user);
        var identityServerUser = new IdentityServerUser(user.Id)
        {
            DisplayName = user.UserName,
            AdditionalClaims = userPrincipal.Claims.ToList(),
        };

        await HttpContext.SignInAsync(identityServerUser, new()
        {
            IsPersistent = true,
            ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(30),
        });

        return Redirect("/Admin");
    }
}
