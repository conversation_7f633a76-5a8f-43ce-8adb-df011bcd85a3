@page
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button
@using HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input
@model HealthNet.Homecare.IdentityServer.Pages.Admin.Login.IndexModel
@{
}

<link rel="stylesheet" href="~/css/page/login.css" />

<div class="login-container">
    <div class="login-card">
        <div class="login-logo">
            <img src="~/images/logo.svg" alt="Logo" />
        </div>
        <h1 class="login-heading">Admin Login</h1>

        <form method="post" class="login-form">
            <partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
                     model="@(new InputModel {
                         Id = "username",
                         Name = "Input.Username",
                         Label = "Username",
                         Placeholder = "Enter your admin username",
                         Autofocus = true
                     })" />
            <span asp-validation-for="Input.Username" class="field-validation-error"></span>

            <partial name="~/Pages/Shared/Components/Input/_Input.cshtml"
                     model="@(new InputModel {
                         Id = "password",
                         Name = "Input.Password",
                         Type = "password",
                         Label = "Password",
                         Autocomplete = "off"
                     })" />
            <span asp-validation-for="Input.Password" class="field-validation-error"></span>

            <div asp-validation-summary="ModelOnly" class="validation-summary-errors"></div>

            <partial name="~/Pages/Shared/Components/Button/_Button.cshtml"
                     model="@(new ButtonModel {
                         Text = "Log in",
                         ButtonType = "submit",
                         ButtonStyle = "primary",
                         AdditionalClasses = "login-button",
                         Name = "Input.Button",
                         Value = "login",
                         IsDisabled = false,
                         RequiredFields = new[] { "username", "password" }
                     })" />
        </form>
    </div>
</div>
