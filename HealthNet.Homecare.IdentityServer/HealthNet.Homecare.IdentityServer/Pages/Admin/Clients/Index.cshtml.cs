using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace HealthNet.Homecare.IdentityServer.Pages.Admin.Clients;

[Authorize]
public class IndexModel(ClientRepository repository) : PageModel
{
    private readonly ClientRepository _repository = repository;

    public string? Filter { get; set; }
    public IEnumerable<ClientSummaryModel> Clients { get; private set; } = default!;

    public async Task OnGetAsync(string? filter)
    {
        Filter = filter;
        Clients = await _repository.GetAllAsync(filter);
    }
}
