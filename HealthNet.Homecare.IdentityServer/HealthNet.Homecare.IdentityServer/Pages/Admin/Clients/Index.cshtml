@page
@model HealthNet.Homecare.IdentityServer.Pages.Admin.Clients.IndexModel
@{
    Layout = "_Layout";
}

<div class="container my-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Clients</h2>
        <div>
            <a asp-page="/Admin/Index" class="btn btn-outline-secondary me-2">Back to Dashboard</a>
            <a asp-page="/Admin/Clients/New" class="btn btn-success">New Client</a>
        </div>
    </div>

    <form method="get" class="row g-3 align-items-center mb-4">
        <div class="col-4">
            <input class="form-control" placeholder="Filter by Client ID or Name" name="filter" value="@Model.Filter" />
        </div>
        <div class="col-2">
            <button type="submit" class="btn btn-primary">Search</button>
        </div>
    </form>

    <div class="table-responsive">
        <table class="table table-bordered table-hover align-middle">
            <thead class="table-light">
                <tr>
                    <th>Client ID</th>
                    <th>Name</th>
                    <th>Flow</th>
                </tr>
            </thead>
            <tbody>
                @if (Model.Clients.Any())
                {
                    foreach (var client in Model.Clients)
                    {
                        <tr>
                            <td>
                                <a asp-page="/Admin/Clients/Edit" asp-route-id="@client.ClientId">
                                    @client.ClientId
                                </a>
                            </td>
                            <td>@client.Name</td>
                            <td>
                                <span class="badge bg-light text-dark">@client.Flow</span>
                            </td>
                        </tr>
                    }
                }
                else
                {
                    <tr>
                        <td colspan="3" class="text-center text-muted">No clients found.</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>