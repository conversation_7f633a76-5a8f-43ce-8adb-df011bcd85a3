@page
@model HealthNet.Homecare.IdentityServer.Pages.Admin.Clients.NewModel
@{
}

<div class="container my-5">
    @if (Model.Created)
    {
        <div class="alert alert-success">
            <h4 class="alert-heading">🎉 Client Created</h4>
            <p>
                <strong>Client ID:</strong> <code>@Model.InputModel.ClientId</code>
            </p>
            <p>
                <strong>Secret:</strong>
                <span class="d-block alert alert-danger mt-2">
                    @Model.InputModel.Secret
                </span>
            </p>
            <p class="mt-3">
                ⚠️ This secret is only shown once. Please store it securely.
            </p>
            <hr />
            <p>
                <a class="btn btn-primary" asp-page="/Admin/Clients/Edit" asp-route-id="@Model.InputModel.ClientId">Continue to Edit</a>
            </p>
        </div>
    }
    else
    {
        <h2 class="mb-4">New Client</h2>
        <partial name="_ValidationSummary" />

        <form method="post" class="row g-4">
            <div class="col-md-6">

                <div class="mb-3">
                    <label asp-for="InputModel.ClientId" class="form-label">Client ID</label>
                    <input asp-for="InputModel.ClientId" class="form-control" autofocus />
                </div>

                <div class="mb-3">
                    <label asp-for="InputModel.Secret" class="form-label">Client Secret</label>
                    <input asp-for="InputModel.Secret" class="form-control" />
                </div>

                <div class="mb-3">
                    <label asp-for="InputModel.Name" class="form-label">Client Name</label>
                    <input asp-for="InputModel.Name" class="form-control" />
                </div>

                <div class="mb-3 form-group">
                    <label asp-for="@Model.InputModel.Flow"></label>
                    <select class="form-control" asp-for="@Model.InputModel.Flow">
                        <option value="0">Client Credentials (API-to-API)</option>
                        <option value="1">Code Flow (with PKCE)</option>
                    </select>
                </div>

                <div class="d-flex justify-content-between">
                    <button class="btn btn-success" type="submit">Create Client</button>
                    <a class="btn btn-secondary" asp-page="/Admin/Clients/Index">Cancel</a>
                </div>
            </div>
        </form>
    }
</div>
