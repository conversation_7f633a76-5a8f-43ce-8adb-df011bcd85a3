@page
@using HealthNet.Homecare.IdentityServer.Extensions
@model HealthNet.Homecare.IdentityServer.Pages.Admin.Clients.EditModel
@{
}

<div class="container my-5">
    <h2 class="mb-4">
        Edit Client <code>@Model.InputModel.ClientId</code>
        <span class="badge bg-secondary">@Model.InputModel.Flow</span>
    </h2>

    <partial name="_ValidationSummary" />

    <form method="post" class="row g-4">
        <input type="hidden" asp-for="@Model.InputModel.ClientId" />
        <input type="hidden" asp-for="@Model.InputModel.Flow" />

        <div class="col-md-8">
            <div class="mb-3">
                <label asp-for="InputModel.Name" class="form-label">Client Name</label>
                <input asp-for="InputModel.Name" class="form-control" autofocus />
            </div>

            <div class="mb-3">
                <label asp-for="InputModel.AllowedScopes" class="form-label">
                    Allowed Scopes <span class="text-muted">(space separated)</span>
                </label>
                <input asp-for="InputModel.AllowedScopes" class="form-control" />
            </div>

            @if (Model.InputModel.Flow == HealthNet.Homecare.IdentityServer.Pages.Admin.Clients.Flow.CodeFlowWithPkce)
            {
                <hr />
                <h5 class="text-muted mb-3">Web Client Configuration</h5>

                <div class="mb-3">
                    <label asp-for="InputModel.CorsOrigin" class="form-label">CORS Origin</label>
                    <input asp-for="InputModel.CorsOrigin" class="form-control" />
                </div>

                <div class="mb-3">
                    <label asp-for="InputModel.RedirectUri" class="form-label">Redirect URI</label>
                    <input asp-for="InputModel.RedirectUri" class="form-control" />
                </div>

                <div class="mb-3">
                    <label asp-for="InputModel.InitiateLoginUri" class="form-label">Initiate Login URI</label>
                    <input asp-for="InputModel.InitiateLoginUri" class="form-control" />
                </div>

                <div class="mb-3">
                    <label asp-for="InputModel.PostLogoutRedirectUri" class="form-label">Post Logout Redirect URI</label>
                    <input asp-for="InputModel.PostLogoutRedirectUri" class="form-control" />
                </div>

                <div class="mb-3">
                    <label asp-for="InputModel.FrontChannelLogoutUri" class="form-label">Front Channel Logout URI</label>
                    <input asp-for="InputModel.FrontChannelLogoutUri" class="form-control" />
                </div>

                <div class="mb-3">
                    <label asp-for="InputModel.BackChannelLogoutUri" class="form-label">Back Channel Logout URI</label>
                    <input asp-for="InputModel.BackChannelLogoutUri" class="form-control" />
                </div>
            }
        </div>

        <div class="col-md-8 d-flex justify-content-between mt-4">
            <div>
                <button class="btn btn-primary me-2" type="submit" name="Button" value="save">💾 Save Changes</button>
                <a class="btn btn-secondary" asp-page="/Admin/Clients/Index">↩ Cancel</a>
            </div>
            <button 
                id="deleteButton"
                class="btn btn-outline-danger" 
                type="submit" name="Button" value="delete">
                🗑 Delete
            </button>
        </div>
    </form>
</div>

@{
    var script = @"
        document.addEventListener('DOMContentLoaded', function () {
            const deleteButton = document.getElementById('deleteButton');

            if (deleteButton) {
                deleteButton.addEventListener('click', function (event) {
                    const confirmation = confirm('Are you sure?');
                    if (!confirmation) {
                        event.preventDefault();
                    }
                });
            }
        });
    ";
    @Html.InlineScriptWithNonce(script);
}
