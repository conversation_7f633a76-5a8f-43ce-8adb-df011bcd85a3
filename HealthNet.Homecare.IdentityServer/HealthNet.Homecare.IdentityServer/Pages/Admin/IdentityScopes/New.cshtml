@page
@model HealthNet.Homecare.IdentityServer.Pages.Admin.IdentityScopes.NewModel
@{
}

<div class="container my-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">New Identity Scope</h2>
        <a asp-page="/Admin/IdentityScopes/Index" class="btn btn-outline-secondary">Back to Scopes</a>
    </div>

    <partial name="_ValidationSummary" />

    <div class="row">
        <div class="col-md-6">
            <form method="post" class="vstack gap-3">
                <div>
                    <label asp-for="InputModel.Name" class="form-label"></label>
                    <input asp-for="InputModel.Name" class="form-control" autofocus />
                </div>

                <div>
                    <label asp-for="InputModel.DisplayName" class="form-label"></label>
                    <input asp-for="InputModel.DisplayName" class="form-control" />
                </div>

                <div>
                    <label asp-for="InputModel.UserClaims" class="form-label">User Claims (space delimited)</label>
                    <input asp-for="InputModel.UserClaims" class="form-control" />
                </div>

                <div class="d-flex justify-content-between gap-2">
                    <button type="submit" class="btn btn-primary">Save Scope</button>
                    <a asp-page="/Admin/IdentityScopes/Index" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
