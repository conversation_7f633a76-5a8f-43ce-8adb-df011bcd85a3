@page
@model HealthNet.Homecare.IdentityServer.Pages.Admin.IdentityScopes.IndexModel
@{
}

<div class="container my-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Identity Scopes</h2>
        <div>
            <a asp-page="/Admin/Index" class="btn btn-outline-secondary me-2">Back to Dashboard</a>
            <a asp-page="/Admin/IdentityScopes/New" class="btn btn-success">New Scope</a>
        </div>
    </div>

    <form method="get" class="row g-3 align-items-center mb-4">
        <div class="col-4">
            <input class="form-control" placeholder="Filter by Name or Display Name" name="filter" value="@Model.Filter" autofocus />
        </div>
        <div class="col-2">
            <button type="submit" class="btn btn-primary">Search</button>
        </div>
    </form>

    <div class="table-responsive">
        <table class="table table-bordered table-hover align-middle">
            <thead class="table-light">
                <tr>
                    <th>Name</th>
                    <th>Display Name</th>
                </tr>
            </thead>
            <tbody>
                @if (Model.Scopes.Any())
                {
                    foreach (var scope in Model.Scopes)
                    {
                        <tr>
                            <td>
                                <a asp-page="/Admin/IdentityScopes/Edit" asp-route-id="@scope.Name">
                                    @scope.Name
                                </a>
                            </td>
                            <td>@scope.DisplayName</td>
                        </tr>
                    }
                }
                else
                {
                    <tr>
                        <td colspan="2" class="text-center text-muted">No identity scopes found.</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>
