@page
@using HealthNet.Homecare.IdentityServer.Extensions
@model HealthNet.Homecare.IdentityServer.Pages.Admin.ApiScopes.EditModel
@{
}

<div class="container my-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Edit Identity Scope: <span class="text-muted">@Model.InputModel.Name</span></h2>
        <a asp-page="/Admin/ApiScopes/Index" class="btn btn-outline-secondary">Back to Scopes</a>
    </div>

    <partial name="_ValidationSummary" />

    <div class="row">
        <div class="col-md-6">
            <form method="post" class="vstack gap-3">
                <input type="hidden" asp-for="InputModel.Name" />

                <div>
                    <label asp-for="InputModel.DisplayName" class="form-label"></label>
                    <input asp-for="InputModel.DisplayName" class="form-control" autofocus />
                </div>

                <div>
                    <label asp-for="InputModel.UserClaims" class="form-label">User Claims (space delimited)</label>
                    <input asp-for="InputModel.UserClaims" class="form-control" />
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" name="Button" value="save" class="btn btn-primary me-2">💾 Save Changes</button>
                    <a asp-page="/Admin/ApiScopes/Index" class="btn btn-secondary">Cancel</a>
                    <button id="deleteButton"
                        class="btn btn-outline-danger" 
                        type="submit" name="Button" value="delete">
                        🗑 Delete
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@{
    var script = @"
        document.addEventListener('DOMContentLoaded', function () {
            const deleteButton = document.getElementById('deleteButton');

            if (deleteButton) {
                deleteButton.addEventListener('click', function (event) {
                    const confirmation = confirm('Are you sure?');
                    if (!confirmation) {
                        event.preventDefault();
                    }
                });
            }
        });
    ";
    @Html.InlineScriptWithNonce(script)
    ;
}
