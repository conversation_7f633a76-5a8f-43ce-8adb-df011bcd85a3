@page
@model HealthNet.Homecare.IdentityServer.Pages.Admin.IndexModel
@{
}

<div class="container my-5">
    <div class="d-flex justify-content-end mb-3">
        <form method="post" asp-page-handler="Logout">
            <button type="submit" class="btn btn-outline-danger">Sign Out</button>
        </form>
    </div>

    <div class="text-center mb-4">
        <h2 class="fw-bold">Duende IdentityServer Admin</h2>
        <p class="text-muted">Simple administrative tools for managing clients and scopes.</p>
    </div>

    <div class="row row-cols-1 row-cols-md-3 g-4">
        <div class="col">
            <a asp-page="/Admin/Clients/Index" class="text-decoration-none">
                <div class="card h-100 shadow-sm border-0 hover-shadow">
                    <div class="card-body text-center">
                        <h5 class="card-title mb-2">Clients</h5>
                        <p class="card-text text-muted">Manage registered clients including web, mobile, and API consumers.</p>
                    </div>
                </div>
            </a>
        </div>
        <div class="col">
            <a asp-page="/Admin/IdentityScopes/Index" class="text-decoration-none">
                <div class="card h-100 shadow-sm border-0">
                    <div class="card-body text-center">
                        <h5 class="card-title mb-2">Identity Scopes</h5>
                        <p class="card-text text-muted">View and manage identity scopes used in OpenID Connect flows.</p>
                    </div>
                </div>
            </a>
        </div>
        <div class="col">
            <a asp-page="/Admin/ApiScopes/Index" class="text-decoration-none">
                <div class="card h-100 shadow-sm border-0">
                    <div class="card-body text-center">
                        <h5 class="card-title mb-2">API Scopes</h5>
                        <p class="card-text text-muted">Define scopes for securing access to API resources.</p>
                    </div>
                </div>
            </a>
        </div>
    </div>
</div>

