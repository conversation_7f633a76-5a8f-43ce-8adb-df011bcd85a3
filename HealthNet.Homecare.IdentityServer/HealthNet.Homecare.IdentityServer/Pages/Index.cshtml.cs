using Duende.IdentityServer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.Reflection;

namespace HealthNet.Homecare.IdentityServer.Pages.Home
{
	[AllowAnonymous]
	public class Index : PageModel
	{
		public Index(IdentityServerLicense? license = null)
		{
			License = license;
		}

		public string Version
		{
			get => typeof(Duende.IdentityServer.Hosting.IdentityServerMiddleware).Assembly
				.GetCustomAttribute<AssemblyInformationalVersionAttribute>()
				?.InformationalVersion.Split('+').First()
				?? "unavailable";
		}

		public IdentityServerLicense? License { get; }

		public IActionResult OnGet()
		{
			return RedirectToPage("/Account/Login/Index");
		}
	}
}
