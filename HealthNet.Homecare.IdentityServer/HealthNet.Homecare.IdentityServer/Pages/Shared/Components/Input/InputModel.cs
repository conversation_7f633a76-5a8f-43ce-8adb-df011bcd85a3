using Microsoft.AspNetCore.Mvc.ViewFeatures;

namespace HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input
{
    public class InputModel
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; } = "text";
        public string Placeholder { get; set; }
        public string Value { get; set; }
        public string AdditionalClasses { get; set; }
        public bool IsRequired { get; set; }
        public bool IsDisabled { get; set; }
        public bool IsReadOnly { get; set; }
        public string Autocomplete { get; set; }
        public bool Autofocus { get; set; }
        public string Label { get; set; }
        public string TooltipText { get; set; }
        public int? MaxLength { get; set; }
        public int? Min { get; set; }
        public int? Max { get; set; }
        public string ErrorMessage { get; set; }
    }
} 