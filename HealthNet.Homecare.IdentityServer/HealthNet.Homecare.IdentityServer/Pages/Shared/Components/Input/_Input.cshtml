@model HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Input.InputModel

<div class="form-group">
    @if (!string.IsNullOrEmpty(Model.Label))
    {
        <label class="form-label" for="@Model.Id">
            @Model.Label
            @if (!string.IsNullOrEmpty(Model.TooltipText))
            {
                <partial name="~/Pages/Shared/Components/InfoTooltip/_InfoTooltip.cshtml" model="@Model.TooltipText" />
            }
        </label>
    }
    <div class="@(Model.Type == "password" ? "password-input-wrapper" : "")">
        <input id="@Model.Id"
               name="@Model.Name"
               type="@Model.Type"
               class="form-control @Model.AdditionalClasses"
               placeholder="@Model.Placeholder"
               value="@Model.Value"
               @(Model.IsRequired ? "required" : "")
               @(Model.IsDisabled ? "disabled" : "")
               @(Model.IsReadOnly ? "readonly" : "")
               @(Model.Autofocus ? "autofocus" : "")
               @(Model.MaxLength.HasValue ? $"maxlength=\"{Model.MaxLength}\"" : "")
               @(Model.Min.HasValue ? $"min=\"{Model.Min}\"" : "")
               @(Model.Max.HasValue ? $"max=\"{Model.Max}\"" : "")
               autocomplete="@Model.Autocomplete"
               data-required="@Model.IsRequired.ToString().ToLower()" />
        @if (Model.Type == "password")
        {
            <button type="button" 
                    class="password-toggle-button">
                <img src="~/images/eye-off.svg" alt="Toggle password visibility" class="password-toggle-icon" />
            </button>
        }
    </div>
    <span class="field-validation-error" data-valmsg-for="@Model.Name"></span>
</div> 