@model HealthNet.Homecare.IdentityServer.Pages.Shared.Components.Button.ButtonModel

<button class="btn <EMAIL> @Model.AdditionalClasses @(Model.IsLoading ? "btn-loading" : "")"
		type="@Model.ButtonType"
		name="@Model.Name"
		value="@Model.Value"
		@(Model.IsDisabled || Model.IsLoading ? "disabled" : "")
		@if (Model.RequiredFields != null && Model.RequiredFields.Any())
		{
	            <text>data-required-fields="@string.Join(",", Model.RequiredFields)"</text>
        }
		@if (Model.AdditionalAttributes != null)
		{
			foreach (var attr in Model.AdditionalAttributes)
			{
				<text>@attr.Key="@attr.Value"</text>
			}
		}
>
    <span class="btn-loading-spinner" style="@(Model.IsLoading ? "" : "display: none;")">
        <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.5 1.875V3.95833M10.5 15V18.3333M5.29167 10H2.375M18.2083 10H16.9583M15.8809 15.3809L15.2917 14.7917M16.0535 4.51316L14.875 5.69167M4.60131 15.8987L6.95833 13.5417M4.7739 4.34057L6.54167 6.10833" stroke="currentColor" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    </span>
    <span class="btn-text" style="@(Model.IsLoading ? "display: none;" : "")">@Model.Text</span>
</button>