@using HealthNet.Homecare.IdentityServer.Extensions
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no" />

    <link rel="icon" type="image/x-icon" href="~/images/favicon.ico" />

    @Html.StylesheetWithNonce(Url.Content("~/lib/bootstrap/dist/css/bootstrap.min.css"))
    @Html.StylesheetWithNonce(Url.Content("~/lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css"))
    
    @Html.RenderStyleBundleWithNonce("site")

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&amp;display=swap" rel="stylesheet" />

    @Html.ScriptWithNonce("https://challenges.cloudflare.com/turnstile/v0/api.js")
</head>
<body>
    <div>
        @RenderBody()
    </div>

    @Html.ScriptWithNonce(Url.Content("~/lib/jquery/dist/jquery.min.js"))
    @Html.ScriptWithNonce(Url.Content("~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"))
    
    @Html.RenderScriptBundleWithNonce("components")

    @RenderSection("scripts", required: false)
</body>
</html>