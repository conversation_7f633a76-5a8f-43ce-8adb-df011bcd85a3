@using Duende.IdentityServer.Extensions
@{
    #nullable enable
    string? name = null;
    if (!true.Equals(ViewData["signed-out"]))
    {
        name = Context.User?.GetDisplayName();
    }
}

<div class="nav-page">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">

        <a href="~/" class="navbar-brand">
            <img src="~/duende-logo.svg" class="icon-banner">
            Duende IdentityServer
        </a>

        @if (!string.IsNullOrWhiteSpace(name))
        {
            <ul class="navbar-nav mr-auto">
                <li class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">@name <b class="caret"></b></a>
                    
                    <div class="dropdown-menu">
                        <a class="dropdown-item" asp-page="/Account/Logout/Index">Logout</a>
                    </div>
              </li>
            </ul>
        }
    
    </nav>
</div>
