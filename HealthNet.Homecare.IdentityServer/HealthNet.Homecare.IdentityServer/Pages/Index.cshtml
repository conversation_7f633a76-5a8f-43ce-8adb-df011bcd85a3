@page
@model HealthNet.Homecare.IdentityServer.Pages.Home.Index

<div class="welcome-page">
    <h1>
        Welcome to Duende IdentityServer
        <small class="text-muted">(version @Model.Version)</small>
    </h1>

    <ul>
        <li>
            IdentityServer publishes a
            <a href="~/.well-known/openid-configuration">discovery document</a>
            where you can find metadata and links to all the endpoints, key material, etc.
        </li>
        <li>
            Click <a href="~/diagnostics">here</a> to see the claims for your current session.
        </li>
        <li>
            Click <a href="~/grants">here</a> to manage your stored grants.
        </li>
        <li>
            Click <a href="~/serversidesessions">here</a> to view the server side sessions.
        </li>
        <li>
            Click <a href="~/ciba/all">here</a> to view your pending CIBA login requests.
        </li>
        <li>
            Click <a href="~/admin">here</a> to view the simple admin page.
        </li>
        <li>
            Click <a href="~/portal">here</a> to view the client application portal.
        </li>
        <li>
            Here are links to the
            <a href="https://github.com/duendesoftware/IdentityServer">source code repository</a>,
            and <a href="https://github.com/duendesoftware/samples">ready to use samples</a>.
        </li>
    </ul>

    @if(Model.License != null)
    {
        <h2>License</h2>
        <dl>
            <dt>Serial Number</dt>
            <dd>@Model.License.SerialNumber</dd>
            <dt>Expiration</dt>
            <dd>@Model.License.Expiration!.Value.ToLongDateString()</dd>
        </dl>
    }
</div>
