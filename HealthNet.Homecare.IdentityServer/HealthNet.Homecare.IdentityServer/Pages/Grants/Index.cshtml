@page
@model HealthNet.Homecare.IdentityServer.Pages.Grants.Index
@{
}

<div class="container my-5">
    <div class="mb-4">
        <h1 class="mb-3">Client Application Permissions</h1>
        <p class="lead">Below is the list of applications you have given permission to and the resources they have access to.</p>
    </div>

    @if (!Model.View.Grants.Any())
    {
        <div class="alert alert-info text-center">
            You have not given access to any applications.
        </div>
    }
    else
    {
        <div class="vstack gap-4">
            @foreach (var grant in Model.View.Grants)
            {
                <div class="card shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center gap-2">
                            @if (!string.IsNullOrEmpty(grant.ClientLogoUrl))
                            {
                                <img src="@grant.ClientLogoUrl" alt="@grant.ClientName logo" style="height: 32px;" />
                            }
                            <strong>@grant.ClientName</strong>
                        </div>
                        <form asp-page="/Grants/Index" method="post" class="m-0">
                            <input type="hidden" name="clientId" value="@grant.ClientId" />
                            <button class="btn btn-sm btn-danger">Revoke Access</button>
                        </form>
                    </div>

                    <ul class="list-group list-group-flush">
                        @if (!string.IsNullOrEmpty(grant.Description))
                        {
                            <li class="list-group-item">
                                <strong>Description:</strong> @grant.Description
                            </li>
                        }
                        <li class="list-group-item">
                            <strong>Created:</strong> @grant.Created:yyyy-MM-dd
                        </li>
                        @if (grant.Expires.HasValue)
                        {
                            <li class="list-group-item">
                                <strong>Expires:</strong> @grant.Expires.Value:yyyy-MM-dd
                            </li>
                        }
                        @if (grant.IdentityGrantNames.Any())
                        {
                            <li class="list-group-item">
                                <strong>Identity Grants:</strong>
                                <ul class="mb-0">
                                    @foreach (var name in grant.IdentityGrantNames)
                                    {
                                        <li>@name</li>
                                    }
                                </ul>
                            </li>
                        }
                        @if (grant.ApiGrantNames.Any())
                        {
                            <li class="list-group-item">
                                <strong>API Grants:</strong>
                                <ul class="mb-0">
                                    @foreach (var name in grant.ApiGrantNames)
                                    {
                                        <li>@name</li>
                                    }
                                </ul>
                            </li>
                        }
                    </ul>
                </div>
            }
        </div>
    }
</div>
