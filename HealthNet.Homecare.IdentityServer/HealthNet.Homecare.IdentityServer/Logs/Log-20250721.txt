2025-07-21 16:27:49 [Information] Starting Duende IdentityServer version 7.2.3+97927d92196d2386c95e60fd0a4e72e27c300cd2 (.NET 8.0.11)
2025-07-21 16:27:49 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-21 16:27:49 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-21 16:27:50 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-21 16:27:50 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-21 16:27:50 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-21 16:27:50 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-21 16:27:50 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-21 16:27:50 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-21 16:27:50 [Information] Seeding database...
2025-07-21 16:27:51 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-21 16:27:51 [Debug] Clients already populated
2025-07-21 16:27:51 [Debug] IdentityResources already populated
2025-07-21 16:27:51 [Debug] ApiScopes already populated
2025-07-21 16:27:51 [Debug] OIDC IdentityProviders already populated
2025-07-21 16:27:51 [Information] Done seeding database. Exiting.
2025-07-21 16:27:52 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-21 16:27:52 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-21 16:27:52 [Debug] Login Url: /Account/Login
2025-07-21 16:27:52 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-21 16:27:52 [Debug] Logout Url: /Account/Logout
2025-07-21 16:27:52 [Debug] ConsentUrl Url: /consent
2025-07-21 16:27:52 [Debug] Consent Return Url Parameter: returnUrl
2025-07-21 16:27:52 [Debug] Error Url: /home/<USER>
2025-07-21 16:27:52 [Debug] Error Id Parameter: errorId
2025-07-21 16:27:52 [Information] HTTP GET / responded 302 in 71.1634 ms
2025-07-21 16:27:52 [Information] HTTP GET /Account/Login responded 200 in 109.3980 ms
2025-07-21 16:27:53 [Information] HTTP GET /images/help-blue.svg responded 304 in 2.9556 ms
2025-07-21 16:27:53 [Information] HTTP GET /css/site.css responded 304 in 2.9623 ms
2025-07-21 16:27:53 [Information] HTTP GET /js/components/info-tooltip.js responded 304 in 0.1753 ms
2025-07-21 16:27:53 [Information] HTTP GET /js/index.js responded 304 in 0.1754 ms
2025-07-21 16:27:53 [Information] HTTP GET /js/components/input.js responded 304 in 0.1212 ms
2025-07-21 16:27:53 [Information] HTTP GET /js/components/button-validation.js responded 304 in 0.0817 ms
2025-07-21 16:27:53 [Information] HTTP GET /js/pages/password-validation.js responded 304 in 0.1082 ms
2025-07-21 16:27:53 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.0305 ms
2025-07-21 16:27:53 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0890 ms
2025-07-21 16:30:33 [Information] HTTP GET /Account/ForgotPassword responded 200 in 31.4804 ms
2025-07-21 16:30:33 [Information] HTTP GET /images/arrow-left.svg responded 304 in 0.1544 ms
2025-07-21 16:30:33 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.1090 ms
2025-07-21 16:30:37 [Information] HTTP GET /Account/Login responded 200 in 51.5815 ms
2025-07-21 16:30:47 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:30:47 [Information] HTTP POST /Account/Login responded 302 in 116.8674 ms
2025-07-21 16:30:47 [Error] Exception reading protected message
System.Security.Cryptography.CryptographicException: The provided payload cannot be decrypted because it was not protected with this protection provider. For more information go to https://aka.ms/aspnet/dataprotectionwarning
   at Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingBasedDataProtector.UnprotectCore(Byte[] protectedData, Boolean allowOperationsOnRevokedKeys, UnprotectStatus& status)
   at Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingBasedDataProtector.Unprotect(Byte[] protectedData)
   at Duende.IdentityServer.Stores.ProtectedDataMessageStore`1.ReadAsync(String value) in /_/identity-server/src/IdentityServer/Stores/Default/ProtectedDataMessageStore.cs:line 56
2025-07-21 16:30:47 [Information] HTTP GET /home/<USER>
2025-07-21 16:30:54 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.9538 ms
2025-07-21 16:30:54 [Information] HTTP GET /css/site.css.map responded 304 in 0.1130 ms
2025-07-21 16:30:57 [Information] HTTP GET /Account/Login responded 200 in 3.1911 ms
2025-07-21 16:30:57 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4452 ms
2025-07-21 16:35:03 [Information] Starting Duende IdentityServer version 7.2.3+97927d92196d2386c95e60fd0a4e72e27c300cd2 (.NET 8.0.11)
2025-07-21 16:35:03 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-21 16:35:03 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-21 16:35:03 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-21 16:35:03 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-21 16:35:03 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-21 16:35:03 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-21 16:35:03 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-21 16:35:03 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-21 16:35:03 [Information] Seeding database...
2025-07-21 16:35:04 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-21 16:35:05 [Debug] Clients already populated
2025-07-21 16:35:05 [Debug] IdentityResources already populated
2025-07-21 16:35:05 [Debug] ApiScopes already populated
2025-07-21 16:35:05 [Debug] OIDC IdentityProviders already populated
2025-07-21 16:35:05 [Information] Done seeding database. Exiting.
2025-07-21 16:35:05 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-21 16:35:05 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-21 16:35:06 [Debug] Login Url: /Account/Login
2025-07-21 16:35:06 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-21 16:35:06 [Debug] Logout Url: /Account/Logout
2025-07-21 16:35:06 [Debug] ConsentUrl Url: /consent
2025-07-21 16:35:06 [Debug] Consent Return Url Parameter: returnUrl
2025-07-21 16:35:06 [Debug] Error Url: /home/<USER>
2025-07-21 16:35:06 [Debug] Error Id Parameter: errorId
2025-07-21 16:35:06 [Information] HTTP GET / responded 302 in 64.6218 ms
2025-07-21 16:35:06 [Information] HTTP GET /Account/ForgotPassword responded 200 in 128.9669 ms
2025-07-21 16:35:06 [Information] HTTP GET /Account/Login responded 200 in 55.1249 ms
2025-07-21 16:35:06 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 1.3147 ms
2025-07-21 16:35:56 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 1.0527 ms
2025-07-21 16:35:58 [Information] HTTP GET /Account/Login responded 200 in 41.7110 ms
2025-07-21 16:35:58 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.7964 ms
2025-07-21 16:35:58 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 6.2071 ms
2025-07-21 16:35:58 [Information] HTTP GET /images/help-blue.svg responded 200 in 6.0065 ms
2025-07-21 16:35:58 [Information] HTTP GET /images/help.svg responded 200 in 5.8420 ms
2025-07-21 16:35:58 [Information] HTTP GET /css/site.css responded 200 in 6.1034 ms
2025-07-21 16:35:58 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 7.5854 ms
2025-07-21 16:35:58 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.3661 ms
2025-07-21 16:35:58 [Information] HTTP GET /images/logo.svg responded 200 in 0.4566 ms
2025-07-21 16:35:58 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.4016 ms
2025-07-21 16:35:58 [Information] HTTP GET /css/site.css.map responded 200 in 0.4219 ms
2025-07-21 16:35:58 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.7872 ms
2025-07-21 16:35:58 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 4.7227 ms
2025-07-21 16:35:58 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.6769 ms
2025-07-21 16:35:58 [Information] HTTP GET /js/index.js responded 200 in 0.3917 ms
2025-07-21 16:35:58 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2821 ms
2025-07-21 16:35:58 [Information] HTTP GET /js/components/input.js responded 200 in 0.2095 ms
2025-07-21 16:35:58 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2257 ms
2025-07-21 16:35:58 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3804 ms
2025-07-21 16:35:58 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.3249 ms
2025-07-21 16:35:58 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3815 ms
2025-07-21 16:35:59 [Information] HTTP GET /images/login-background.png responded 200 in 0.9814 ms
2025-07-21 16:35:59 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.3067 ms
2025-07-21 16:35:59 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2998 ms
2025-07-21 16:36:10 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:36:10 [Information] HTTP POST /Account/Login responded 200 in 100.7968 ms
2025-07-21 16:36:10 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4892 ms
2025-07-21 16:36:10 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 1.2520 ms
2025-07-21 16:36:11 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.3095 ms
2025-07-21 16:36:36 [Information] HTTP POST /Account/Login responded 200 in 303.4025 ms
2025-07-21 16:36:38 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:36:38 [Information] HTTP POST /Account/Login responded 200 in 6.2456 ms
2025-07-21 16:36:38 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5986 ms
2025-07-21 16:36:38 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.1603 ms
2025-07-21 16:36:42 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:36:42 [Information] HTTP POST /Account/Login responded 200 in 6.2091 ms
2025-07-21 16:36:42 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5895 ms
2025-07-21 16:36:44 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:36:44 [Information] HTTP POST /Account/Login responded 200 in 5.8352 ms
2025-07-21 16:36:44 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6105 ms
2025-07-21 16:36:44 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4895 ms
2025-07-21 16:36:44 [Information] HTTP GET /css/site.css responded 200 in 0.6987 ms
2025-07-21 16:36:44 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1931 ms
2025-07-21 16:36:44 [Information] HTTP GET /images/help.svg responded 200 in 0.4369 ms
2025-07-21 16:36:44 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.9299 ms
2025-07-21 16:36:44 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.3630 ms
2025-07-21 16:36:44 [Information] HTTP GET /images/logo.svg responded 200 in 0.4068 ms
2025-07-21 16:36:44 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.3715 ms
2025-07-21 16:36:44 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.7003 ms
2025-07-21 16:36:44 [Information] HTTP GET /css/site.css.map responded 200 in 0.3243 ms
2025-07-21 16:36:44 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.6948 ms
2025-07-21 16:36:44 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.6590 ms
2025-07-21 16:36:44 [Information] HTTP GET /js/index.js responded 200 in 0.3494 ms
2025-07-21 16:36:44 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.4062 ms
2025-07-21 16:36:44 [Information] HTTP GET /js/components/input.js responded 200 in 0.3460 ms
2025-07-21 16:36:44 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2810 ms
2025-07-21 16:36:44 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.4534 ms
2025-07-21 16:36:44 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1907 ms
2025-07-21 16:36:44 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3748 ms
2025-07-21 16:36:44 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.1968 ms
2025-07-21 16:36:44 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.7229 ms
2025-07-21 16:36:44 [Information] HTTP GET /images/login-background.png responded 200 in 0.9489 ms
2025-07-21 16:36:45 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3878 ms
2025-07-21 16:36:47 [Information] HTTP GET /Account/Login responded 200 in 3.2940 ms
2025-07-21 16:36:47 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5694 ms
2025-07-21 16:36:54 [Information] HTTP POST /Account/Login responded 200 in 105.1845 ms
2025-07-21 16:36:54 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:36:54 [Information] HTTP POST /Account/Login responded 200 in 7.8495 ms
2025-07-21 16:36:54 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5334 ms
2025-07-21 16:37:17 [Information] HTTP GET /Account/ForgotPassword responded 200 in 2.3933 ms
2025-07-21 16:37:17 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3604 ms
2025-07-21 16:37:17 [Information] HTTP GET /images/password-logo.png responded 200 in 0.3646 ms
2025-07-21 16:37:17 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.3574 ms
2025-07-21 16:37:17 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.0908 ms
2025-07-21 16:37:17 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.2743 ms
2025-07-21 16:37:21 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:37:21 [Information] HTTP POST /Account/ForgotPassword responded 200 in 14.2600 ms
2025-07-21 16:37:21 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4023 ms
2025-07-21 16:37:21 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.1260 ms
2025-07-21 16:37:22 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.7042 ms
2025-07-21 16:37:22 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5931 ms
2025-07-21 16:37:22 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0773 ms
2025-07-21 16:38:35 [Information] HTTP GET /Account/Login responded 200 in 2.5946 ms
2025-07-21 16:38:35 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3577 ms
2025-07-21 16:38:35 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.3557 ms
2025-07-21 16:38:46 [Information] HTTP POST /Account/Login responded 200 in 110.7727 ms
2025-07-21 16:38:47 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:38:47 [Information] HTTP POST /Account/Login responded 200 in 6.0957 ms
2025-07-21 16:38:47 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3121 ms
2025-07-21 16:38:47 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.0944 ms
2025-07-21 16:39:06 [Information] HTTP POST /Account/Login responded 200 in 89.0460 ms
2025-07-21 16:48:13 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:48:13 [Information] Contact?$filter=(No eq 'CT237642')
2025-07-21 16:48:14 [Information] Contact?$filter=(No eq 'CT086521')
2025-07-21 16:48:14 [Debug] Executing WebUtilitiesProxy.UpdateClinicianLastLogonAsync
2025-07-21 16:48:14 [Debug] Configured "Basic" authentication
2025-07-21 16:48:14 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-21 16:48:15 [Debug] Successfully executed WebUtilitiesProxy.UpdateClinicianLastLogonAsync in "00:00:00.6750447"ms
2025-07-21 16:48:15 [Information] {"Username":"CT237642","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNE8BDJLM1NI:********","TimeStamp":"2025-07-21T13:48:15.1854653","ProcessId":15992,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-21 16:48:15 [Information] HTTP POST /Account/Login responded 200 in 1223.1268 ms
2025-07-21 16:48:15 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4020 ms
2025-07-21 16:48:15 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.7956 ms
2025-07-21 16:48:15 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.6120 ms
2025-07-21 16:48:15 [Information] HTTP GET /css/site.css responded 200 in 0.6252 ms
2025-07-21 16:48:15 [Information] HTTP GET /images/help.svg responded 200 in 0.2223 ms
2025-07-21 16:48:15 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.4409 ms
2025-07-21 16:48:15 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2926 ms
2025-07-21 16:48:15 [Information] HTTP GET /images/logo.svg responded 200 in 0.4624 ms
2025-07-21 16:48:15 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2223 ms
2025-07-21 16:48:15 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.5216 ms
2025-07-21 16:48:15 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.5043 ms
2025-07-21 16:48:15 [Information] HTTP GET /js/index.js responded 200 in 0.1900 ms
2025-07-21 16:48:15 [Information] HTTP GET /css/site.css.map responded 200 in 0.1444 ms
2025-07-21 16:48:15 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.8173 ms
2025-07-21 16:48:15 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3585 ms
2025-07-21 16:48:15 [Information] HTTP GET /js/components/input.js responded 200 in 0.2487 ms
2025-07-21 16:48:15 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3034 ms
2025-07-21 16:48:15 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.4130 ms
2025-07-21 16:48:15 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2942 ms
2025-07-21 16:48:15 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3156 ms
2025-07-21 16:48:15 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.2004 ms
2025-07-21 16:48:15 [Information] HTTP GET /images/login-background.png responded 200 in 3.8650 ms
2025-07-21 16:48:15 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 4.3539 ms
2025-07-21 16:48:15 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2329 ms
2025-07-21 16:48:22 [Information] HTTP GET /Account/Login responded 200 in 2.7544 ms
2025-07-21 16:48:22 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3987 ms
2025-07-21 16:48:22 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0917 ms
2025-07-21 16:48:29 [Information] HTTP POST /Account/Login responded 200 in 138.0979 ms
2025-07-21 16:48:44 [Information] HTTP GET /Account/Login responded 200 in 2.4610 ms
2025-07-21 16:48:44 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3977 ms
2025-07-21 16:48:44 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.6002 ms
2025-07-21 16:48:44 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.3526 ms
2025-07-21 16:48:44 [Information] HTTP GET /css/site.css responded 200 in 0.4449 ms
2025-07-21 16:48:44 [Information] HTTP GET /images/help.svg responded 200 in 0.3487 ms
2025-07-21 16:48:44 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.3610 ms
2025-07-21 16:48:44 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.3463 ms
2025-07-21 16:48:44 [Information] HTTP GET /images/logo.svg responded 200 in 0.6116 ms
2025-07-21 16:48:44 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2978 ms
2025-07-21 16:48:44 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.6185 ms
2025-07-21 16:48:44 [Information] HTTP GET /css/site.css.map responded 200 in 0.2352 ms
2025-07-21 16:48:44 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.4654 ms
2025-07-21 16:48:44 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.6108 ms
2025-07-21 16:48:45 [Information] HTTP GET /js/index.js responded 200 in 0.3650 ms
2025-07-21 16:48:45 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3132 ms
2025-07-21 16:48:45 [Information] HTTP GET /js/components/input.js responded 200 in 0.1818 ms
2025-07-21 16:48:45 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.4827 ms
2025-07-21 16:48:45 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3161 ms
2025-07-21 16:48:45 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.3050 ms
2025-07-21 16:48:45 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2451 ms
2025-07-21 16:48:45 [Information] HTTP GET /images/login-background.png responded 200 in 1.4045 ms
2025-07-21 16:48:45 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.7242 ms
2025-07-21 16:48:45 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2866 ms
2025-07-21 16:48:52 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:48:52 [Information] HTTP POST /Account/Login responded 200 in 4.3192 ms
2025-07-21 16:48:52 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6073 ms
2025-07-21 16:48:52 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0695 ms
2025-07-21 16:48:52 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.2752 ms
2025-07-21 16:48:53 [Information] HTTP POST /Account/Login responded 200 in 154.3411 ms
2025-07-21 16:49:41 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:49:41 [Information] HTTP POST /Account/Login responded 200 in 3.2993 ms
2025-07-21 16:49:41 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3224 ms
2025-07-21 16:49:41 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.5540 ms
2025-07-21 16:49:41 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2313 ms
2025-07-21 16:49:41 [Information] HTTP GET /css/site.css responded 200 in 0.2740 ms
2025-07-21 16:49:41 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1627 ms
2025-07-21 16:49:41 [Information] HTTP GET /images/help.svg responded 200 in 0.1568 ms
2025-07-21 16:49:41 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2230 ms
2025-07-21 16:49:41 [Information] HTTP GET /images/logo.svg responded 200 in 0.3447 ms
2025-07-21 16:49:41 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2165 ms
2025-07-21 16:49:41 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.3656 ms
2025-07-21 16:49:41 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3936 ms
2025-07-21 16:49:41 [Information] HTTP GET /js/index.js responded 200 in 0.2606 ms
2025-07-21 16:49:41 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.4999 ms
2025-07-21 16:49:41 [Information] HTTP GET /css/site.css.map responded 200 in 0.1579 ms
2025-07-21 16:49:41 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.2512 ms
2025-07-21 16:49:41 [Information] HTTP GET /js/components/input.js responded 200 in 0.2524 ms
2025-07-21 16:49:41 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2215 ms
2025-07-21 16:49:41 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2426 ms
2025-07-21 16:49:41 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2019 ms
2025-07-21 16:49:41 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2333 ms
2025-07-21 16:49:41 [Information] HTTP GET /images/login-background.png responded 200 in 0.5406 ms
2025-07-21 16:49:41 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.3021 ms
2025-07-21 16:49:41 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.3100 ms
2025-07-21 16:49:42 [Information] HTTP GET /images/favicon.ico responded 200 in 0.1689 ms
2025-07-21 16:49:43 [Information] HTTP POST /Account/Login responded 200 in 114.2323 ms
2025-07-21 16:49:53 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:49:53 [Information] HTTP POST /Account/Login responded 200 in 3.4693 ms
2025-07-21 16:49:53 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3749 ms
2025-07-21 16:49:53 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2723 ms
2025-07-21 16:49:53 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.7156 ms
2025-07-21 16:49:53 [Information] HTTP GET /css/site.css responded 200 in 0.3415 ms
2025-07-21 16:49:53 [Information] HTTP GET /images/help.svg responded 200 in 0.1499 ms
2025-07-21 16:49:53 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1448 ms
2025-07-21 16:49:53 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2179 ms
2025-07-21 16:49:53 [Information] HTTP GET /images/logo.svg responded 200 in 0.3031 ms
2025-07-21 16:49:53 [Information] HTTP GET /css/site.css.map responded 200 in 0.2674 ms
2025-07-21 16:49:53 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.2607 ms
2025-07-21 16:49:53 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2311 ms
2025-07-21 16:49:53 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.5990 ms
2025-07-21 16:49:53 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3210 ms
2025-07-21 16:49:53 [Information] HTTP GET /js/index.js responded 200 in 0.1678 ms
2025-07-21 16:49:53 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2273 ms
2025-07-21 16:49:53 [Information] HTTP GET /js/components/input.js responded 200 in 0.2135 ms
2025-07-21 16:49:53 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1827 ms
2025-07-21 16:49:53 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3706 ms
2025-07-21 16:49:53 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2210 ms
2025-07-21 16:49:53 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2262 ms
2025-07-21 16:49:53 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.6741 ms
2025-07-21 16:49:53 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.1424 ms
2025-07-21 16:49:53 [Information] HTTP GET /images/login-background.png responded 200 in 0.5668 ms
2025-07-21 16:49:54 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2258 ms
2025-07-21 16:49:55 [Information] HTTP POST /Account/Login responded 200 in 98.1976 ms
2025-07-21 16:54:26 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:54:26 [Information] HTTP POST /Account/Login responded 200 in 3.8053 ms
2025-07-21 16:54:26 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3069 ms
2025-07-21 16:54:26 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.2476 ms
2025-07-21 16:54:26 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.3949 ms
2025-07-21 16:54:26 [Information] HTTP GET /css/site.css responded 200 in 0.4813 ms
2025-07-21 16:54:26 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1921 ms
2025-07-21 16:54:26 [Information] HTTP GET /images/help.svg responded 200 in 0.3570 ms
2025-07-21 16:54:26 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.1970 ms
2025-07-21 16:54:26 [Information] HTTP GET /images/logo.svg responded 200 in 0.3243 ms
2025-07-21 16:54:26 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2521 ms
2025-07-21 16:54:26 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.3818 ms
2025-07-21 16:54:26 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4261 ms
2025-07-21 16:54:26 [Information] HTTP GET /js/index.js responded 200 in 0.1757 ms
2025-07-21 16:54:26 [Information] HTTP GET /css/site.css.map responded 200 in 0.2193 ms
2025-07-21 16:54:26 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.2172 ms
2025-07-21 16:54:26 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2199 ms
2025-07-21 16:54:26 [Information] HTTP GET /js/components/input.js responded 200 in 0.2119 ms
2025-07-21 16:54:26 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1796 ms
2025-07-21 16:54:26 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.1969 ms
2025-07-21 16:54:26 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1613 ms
2025-07-21 16:54:26 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3387 ms
2025-07-21 16:54:26 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.2709 ms
2025-07-21 16:54:26 [Information] HTTP GET /images/login-background.png responded 200 in 0.5387 ms
2025-07-21 16:54:26 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.6744 ms
2025-07-21 16:54:27 [Information] HTTP GET /images/favicon.ico responded 200 in 0.1786 ms
2025-07-21 16:54:28 [Information] HTTP POST /Account/Login responded 200 in 154.8830 ms
2025-07-21 16:54:33 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:54:33 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-21 16:54:33 [Information] Contact?$filter=(No eq 'CT037767')
2025-07-21 16:54:33 [Information] HTTP POST /Account/Login responded 302 in 336.1285 ms
2025-07-21 16:54:33 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-21 16:54:34 [Information] HTTP GET /Account/Setup responded 200 in 89.7191 ms
2025-07-21 16:54:34 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3239 ms
2025-07-21 16:54:34 [Information] HTTP GET /images/step-active.png responded 200 in 0.3098 ms
2025-07-21 16:54:34 [Information] HTTP GET /images/info-circle-warning.svg responded 200 in 0.1902 ms
2025-07-21 16:54:34 [Information] HTTP GET /images/close.svg responded 200 in 0.1935 ms
2025-07-21 16:54:34 [Information] HTTP GET /images/step-next.png responded 200 in 0.1588 ms
2025-07-21 16:54:40 [Information] HTTP GET /Account/Login responded 200 in 2.1753 ms
2025-07-21 16:54:40 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5003 ms
2025-07-21 16:54:40 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0861 ms
2025-07-21 16:55:13 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:55:13 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-21 16:55:13 [Information] Contact?$filter=(No eq 'CT037767')
2025-07-21 16:55:13 [Information] HTTP POST /Account/Login responded 302 in 140.0986 ms
2025-07-21 16:55:13 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-21 16:55:13 [Information] HTTP GET /Account/Setup responded 200 in 61.7875 ms
2025-07-21 16:55:13 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4209 ms
2025-07-21 16:55:20 [Information] HTTP GET /Account/Login responded 200 in 2.5466 ms
2025-07-21 16:55:20 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4286 ms
2025-07-21 16:55:20 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0599 ms
2025-07-21 16:55:23 [Information] HTTP GET /Account/Login responded 200 in 2.5870 ms
2025-07-21 16:55:23 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3604 ms
2025-07-21 16:55:23 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.9267 ms
2025-07-21 16:55:23 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4125 ms
2025-07-21 16:55:23 [Information] HTTP GET /images/help.svg responded 200 in 0.2238 ms
2025-07-21 16:55:23 [Information] HTTP GET /css/site.css responded 200 in 0.4296 ms
2025-07-21 16:55:23 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2548 ms
2025-07-21 16:55:23 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2686 ms
2025-07-21 16:55:23 [Information] HTTP GET /images/logo.svg responded 200 in 0.4090 ms
2025-07-21 16:55:23 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2081 ms
2025-07-21 16:55:23 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.5758 ms
2025-07-21 16:55:23 [Information] HTTP GET /css/site.css.map responded 200 in 0.2036 ms
2025-07-21 16:55:23 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4650 ms
2025-07-21 16:55:23 [Information] HTTP GET /js/index.js responded 200 in 0.1910 ms
2025-07-21 16:55:23 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 21.3223 ms
2025-07-21 16:55:23 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3100 ms
2025-07-21 16:55:23 [Information] HTTP GET /js/components/input.js responded 200 in 0.2394 ms
2025-07-21 16:55:23 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.3548 ms
2025-07-21 16:55:23 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.1965 ms
2025-07-21 16:55:23 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1538 ms
2025-07-21 16:55:23 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2382 ms
2025-07-21 16:55:23 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.8392 ms
2025-07-21 16:55:23 [Information] HTTP GET /images/login-background.png responded 200 in 0.5471 ms
2025-07-21 16:55:23 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3634 ms
2025-07-21 16:55:45 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:55:45 [Information] HTTP POST /Account/Login responded 200 in 3.2322 ms
2025-07-21 16:55:45 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2890 ms
2025-07-21 16:55:45 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0662 ms
2025-07-21 16:55:46 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.4151 ms
2025-07-21 16:55:47 [Information] HTTP POST /Account/Login responded 200 in 110.9969 ms
2025-07-21 16:55:50 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:55:50 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-21 16:55:50 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-21 16:55:50 [Debug] Executing WebUtilitiesProxy.UpdateClinicianLastLogonAsync
2025-07-21 16:55:50 [Debug] Configured "Basic" authentication
2025-07-21 16:55:50 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-21 16:55:50 [Debug] Successfully executed WebUtilitiesProxy.UpdateClinicianLastLogonAsync in "00:00:00.3520392"ms
2025-07-21 16:55:50 [Debug] Augmenting SignInContext
2025-07-21 16:55:50 [Debug] Adding idp claim with value: local
2025-07-21 16:55:50 [Debug] Adding amr claim with value: pwd
2025-07-21 16:55:50 [Debug] Adding auth_time claim with value: **********
2025-07-21 16:55:50 [Information] HTTP POST /Account/Login responded 302 in 645.8172 ms
2025-07-21 16:55:50 [Information] HTTP GET / responded 302 in 3.2656 ms
2025-07-21 16:55:50 [Information] HTTP GET /Account/Login responded 200 in 2.8956 ms
2025-07-21 16:55:50 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 4.3159 ms
2025-07-21 16:56:17 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:56:17 [Information] HTTP POST /Account/Login responded 200 in 5.3033 ms
2025-07-21 16:56:17 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6876 ms
2025-07-21 16:56:17 [Information] HTTP GET /js/pages/login.js responded 200 in 0.7304 ms
2025-07-21 16:56:19 [Information] HTTP POST /Account/Login responded 200 in 110.3675 ms
2025-07-21 16:56:26 [Information] HTTP GET /Account/Login responded 200 in 2.9519 ms
2025-07-21 16:56:26 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4563 ms
2025-07-21 16:56:26 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0768 ms
2025-07-21 16:56:31 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-21 16:56:31 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-21 16:56:31 [Information] Contact?$filter=(No eq 'CT037767')
2025-07-21 16:56:32 [Information] HTTP POST /Account/Login responded 302 in 319.6205 ms
2025-07-21 16:56:32 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-21 16:56:32 [Information] HTTP GET /Account/Setup responded 200 in 64.7541 ms
2025-07-21 16:56:32 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 1.0552 ms
2025-07-21 16:56:32 [Information] HTTP GET /images/step-active.png responded 200 in 0.2303 ms
2025-07-21 16:56:32 [Information] HTTP GET /images/close.svg responded 200 in 0.2229 ms
2025-07-21 16:56:32 [Information] HTTP GET /images/step-next.png responded 200 in 0.2931 ms
2025-07-21 16:56:32 [Information] HTTP GET /images/info-circle-warning.svg responded 200 in 0.3213 ms
2025-07-21 21:39:31 [Error] [ServiceBus] Error message: The operation was canceled. (ServiceTimeout). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.; Exception: Azure.Messaging.ServiceBus.ServiceBusException: The operation was canceled. (ServiceTimeout). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
2025-07-21 21:39:31 [Error] [ServiceBus] Error message: The operation was canceled. (ServiceTimeout). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.; Exception: Azure.Messaging.ServiceBus.ServiceBusException: The operation was canceled. (ServiceTimeout). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
2025-07-21 21:39:31 [Error] [ServiceBus] Error message: The operation was canceled. (ServiceTimeout). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.; Exception: Azure.Messaging.ServiceBus.ServiceBusException: The operation was canceled. (ServiceTimeout). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
2025-07-21 21:39:31 [Error] [ServiceBus] Error message: The operation was canceled. (ServiceTimeout). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.; Exception: Azure.Messaging.ServiceBus.ServiceBusException: The operation was canceled. (ServiceTimeout). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
2025-07-21 21:39:31 [Error] [ServiceBus] Error message: The operation was canceled. (ServiceTimeout). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.; Exception: Azure.Messaging.ServiceBus.ServiceBusException: The operation was canceled. (ServiceTimeout). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
