2025-07-24 09:56:20 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-24 09:56:20 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-24 09:56:20 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-24 09:56:20 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-24 09:56:20 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-24 09:56:20 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-24 09:56:20 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-24 09:56:20 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-24 09:56:20 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-24 09:56:20 [Information] Seeding database...
2025-07-24 09:56:22 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-24 09:56:22 [Debug] Clients already populated
2025-07-24 09:56:22 [Debug] IdentityResources already populated
2025-07-24 09:56:22 [Debug] ApiScopes already populated
2025-07-24 09:56:22 [Debug] OIDC IdentityProviders already populated
2025-07-24 09:56:22 [Information] Done seeding database. Exiting.
2025-07-24 09:56:22 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-24 09:56:22 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-24 09:56:23 [Debug] Login Url: /Account/Login
2025-07-24 09:56:23 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-24 09:56:23 [Debug] Logout Url: /Account/Logout
2025-07-24 09:56:23 [Debug] ConsentUrl Url: /consent
2025-07-24 09:56:23 [Debug] Consent Return Url Parameter: returnUrl
2025-07-24 09:56:23 [Debug] Error Url: /home/<USER>
2025-07-24 09:56:23 [Debug] Error Id Parameter: errorId
2025-07-24 09:56:23 [Information] HTTP GET / responded 302 in 110.6206 ms
2025-07-24 09:56:23 [Debug] SignOutCalled set; processing post-signout session cleanup.
2025-07-24 09:56:23 [Information] HTTP GET /Account/Login responded 200 in 175.7674 ms
2025-07-24 09:56:23 [Information] HTTP GET /css/site.css responded 304 in 3.4288 ms
2025-07-24 09:56:23 [Information] HTTP GET /js/components/utils.js responded 304 in 0.1994 ms
2025-07-24 09:56:23 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.1075 ms
2025-07-24 09:56:23 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0757 ms
2025-07-24 09:56:23 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0949 ms
2025-07-24 09:56:28 [Information] HTTP GET /Account/Setup responded 200 in 37.4939 ms
2025-07-24 09:56:42 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 09:56:42 [Information] Contact?$filter=(No eq 'CT411887')
2025-07-24 09:56:42 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411887","Type":"Person","Organizational_Level_Code":"TRUST PHAR","Company_No":"CT031253","Company_Name":"Birmingham Womens and Children NHS Foundation Trus","Anonymise":false,"IntegrationCustomerNo":"","Name":"Test ClinicianTwo","First_Name":"Test","Middle_Name":"","Surname":"ClinicianTwo","Address":"","Address_2":"","City":"","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"TEST CLINICIANTWO","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"-","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"HUMIRA-AX","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"","Salutation_Code":"","Registration_Date":"2025-01-20","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":0,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 09:56:42 [Information] HTTP POST /Account/Setup responded 200 in 413.3815 ms
2025-07-24 09:57:27 [Information] Contact?$filter=(No eq 'CT411887')
2025-07-24 09:57:27 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411887","Type":"Person","Organizational_Level_Code":"TRUST PHAR","Company_No":"CT031253","Company_Name":"Birmingham Womens and Children NHS Foundation Trus","Anonymise":false,"IntegrationCustomerNo":"","Name":"Test ClinicianTwo","First_Name":"Test","Middle_Name":"","Surname":"ClinicianTwo","Address":"","Address_2":"","City":"","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"TEST CLINICIANTWO","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"-","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"HUMIRA-AX","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"","Salutation_Code":"","Registration_Date":"2025-01-20","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":0,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 09:57:27 [Information] HTTP GET /Account/Setup responded 200 in 97.7910 ms
2025-07-24 09:57:44 [Information] HTTP GET /Account/Login responded 200 in 51.0401 ms
2025-07-24 09:57:47 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 8.0850 ms
2025-07-24 09:57:47 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 304 in 0.2386 ms
2025-07-24 09:59:15 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.9591 ms
2025-07-24 09:59:15 [Information] HTTP GET /css/site.css.map responded 304 in 0.1011 ms
2025-07-24 09:59:17 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.7824 ms
2025-07-24 09:59:17 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.7704 ms
2025-07-24 09:59:17 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 5.8855 ms
2025-07-24 09:59:17 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 2.1052 ms
2025-07-24 09:59:17 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.4327 ms
2025-07-24 09:59:17 [Information] HTTP GET /images/help.svg responded 200 in 0.5111 ms
2025-07-24 09:59:17 [Information] HTTP GET /css/site.css responded 200 in 0.9283 ms
2025-07-24 09:59:17 [Information] HTTP GET /images/email-logo.png responded 200 in 0.3867 ms
2025-07-24 09:59:17 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.4016 ms
2025-07-24 09:59:17 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.6111 ms
2025-07-24 09:59:17 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.6300 ms
2025-07-24 09:59:17 [Information] HTTP GET /css/site.css.map responded 200 in 0.3252 ms
2025-07-24 09:59:17 [Information] HTTP GET /js/index.js responded 200 in 0.1419 ms
2025-07-24 09:59:17 [Information] HTTP GET /js/components/utils.js responded 200 in 0.2505 ms
2025-07-24 09:59:17 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.0798 ms
2025-07-24 09:59:17 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3292 ms
2025-07-24 09:59:17 [Information] HTTP GET /js/components/input.js responded 200 in 0.2013 ms
2025-07-24 09:59:17 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.3038 ms
2025-07-24 09:59:17 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3120 ms
2025-07-24 09:59:17 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.3107 ms
2025-07-24 09:59:17 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.3043 ms
2025-07-24 09:59:17 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.2852 ms
2025-07-24 09:59:17 [Information] HTTP GET /images/login-background.png responded 200 in 0.7285 ms
2025-07-24 09:59:17 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.9619 ms
2025-07-24 09:59:17 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3672 ms
2025-07-24 09:59:18 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.3875 ms
2025-07-24 09:59:58 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 09:59:58 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-24 09:59:58 [Debug] Configured "Basic" authentication
2025-07-24 09:59:58 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:00:02 [Information] Successfully executed WebUtilitiesProxy.GetContactNoFromEmailAsync in "00:00:03.1379267"ms
2025-07-24 10:00:02 [Information] Contact?$filter=(((No eq 'CT411980')))
2025-07-24 10:00:02 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:00:03 [Information] HTTP POST /Account/ForgotCTNumber responded 302 in 4302.0576 ms
2025-07-24 10:00:03 [Information] HTTP GET /Account/CTNumberSentSuccess responded 200 in 8.4678 ms
2025-07-24 10:00:03 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3899 ms
2025-07-24 10:01:48 [Information] HTTP GET /Account/Login responded 200 in 5.4443 ms
2025-07-24 10:01:48 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4097 ms
2025-07-24 10:01:48 [Information] HTTP GET /images/logo.svg responded 200 in 0.6097 ms
2025-07-24 10:01:48 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2347 ms
2025-07-24 10:01:48 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3001 ms
2025-07-24 10:01:48 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.5183 ms
2025-07-24 10:01:52 [Information] HTTP GET /Account/Login responded 200 in 4.3833 ms
2025-07-24 10:01:52 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6483 ms
2025-07-24 10:01:52 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4046 ms
2025-07-24 10:01:52 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.6309 ms
2025-07-24 10:01:52 [Information] HTTP GET /css/site.css responded 200 in 0.4045 ms
2025-07-24 10:01:52 [Information] HTTP GET /images/help.svg responded 200 in 0.2935 ms
2025-07-24 10:01:52 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2696 ms
2025-07-24 10:01:52 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2667 ms
2025-07-24 10:01:52 [Information] HTTP GET /images/logo.svg responded 200 in 0.5146 ms
2025-07-24 10:01:52 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.4250 ms
2025-07-24 10:01:52 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.6030 ms
2025-07-24 10:01:52 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.7126 ms
2025-07-24 10:01:52 [Information] HTTP GET /js/index.js responded 200 in 0.2679 ms
2025-07-24 10:01:52 [Information] HTTP GET /css/site.css.map responded 200 in 0.2624 ms
2025-07-24 10:01:52 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.2124 ms
2025-07-24 10:01:52 [Information] HTTP GET /js/components/utils.js responded 200 in 0.3747 ms
2025-07-24 10:01:52 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2617 ms
2025-07-24 10:01:52 [Information] HTTP GET /js/components/input.js responded 200 in 0.2787 ms
2025-07-24 10:01:52 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.3297 ms
2025-07-24 10:01:52 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3769 ms
2025-07-24 10:01:52 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2410 ms
2025-07-24 10:01:52 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.7999 ms
2025-07-24 10:01:52 [Information] HTTP GET /js/pages/login.js responded 200 in 0.4704 ms
2025-07-24 10:01:52 [Information] HTTP GET /images/login-background.png responded 200 in 0.7554 ms
2025-07-24 10:01:52 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.2251 ms
2025-07-24 10:01:52 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2746 ms
2025-07-24 10:01:54 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.8360 ms
2025-07-24 10:01:54 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4024 ms
2025-07-24 10:01:54 [Information] HTTP GET /images/email-logo.png responded 200 in 0.2510 ms
2025-07-24 10:01:54 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2432 ms
2025-07-24 10:01:55 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.3988 ms
2025-07-24 10:01:59 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.4402 ms
2025-07-24 10:02:45 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.5690 ms
2025-07-24 10:02:45 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3937 ms
2025-07-24 10:02:45 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.5755 ms
2025-07-24 10:02:45 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4678 ms
2025-07-24 10:02:45 [Information] HTTP GET /css/site.css responded 200 in 0.4638 ms
2025-07-24 10:02:45 [Information] HTTP GET /images/help.svg responded 200 in 0.1992 ms
2025-07-24 10:02:45 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2118 ms
2025-07-24 10:02:45 [Information] HTTP GET /images/email-logo.png responded 200 in 0.2481 ms
2025-07-24 10:02:45 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1831 ms
2025-07-24 10:02:45 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.3654 ms
2025-07-24 10:02:45 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3730 ms
2025-07-24 10:02:45 [Information] HTTP GET /js/index.js responded 200 in 0.2141 ms
2025-07-24 10:02:45 [Information] HTTP GET /js/components/utils.js responded 200 in 0.2779 ms
2025-07-24 10:02:45 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1678 ms
2025-07-24 10:02:45 [Information] HTTP GET /css/site.css.map responded 200 in 0.2953 ms
2025-07-24 10:02:45 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.5398 ms
2025-07-24 10:02:45 [Information] HTTP GET /js/components/input.js responded 200 in 0.3680 ms
2025-07-24 10:02:45 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1672 ms
2025-07-24 10:02:45 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2675 ms
2025-07-24 10:02:45 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1667 ms
2025-07-24 10:02:45 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2096 ms
2025-07-24 10:02:45 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.1616 ms
2025-07-24 10:02:45 [Information] HTTP GET /images/login-background.png responded 200 in 0.5491 ms
2025-07-24 10:02:45 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.2625 ms
2025-07-24 10:02:45 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2218 ms
2025-07-24 10:02:47 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.5610 ms
2025-07-24 10:03:40 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.5102 ms
2025-07-24 10:03:40 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4040 ms
2025-07-24 10:03:40 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.1531 ms
2025-07-24 10:03:40 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2170 ms
2025-07-24 10:03:40 [Information] HTTP GET /css/site.css responded 200 in 0.3541 ms
2025-07-24 10:03:40 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.3534 ms
2025-07-24 10:03:40 [Information] HTTP GET /images/help.svg responded 200 in 0.2942 ms
2025-07-24 10:03:40 [Information] HTTP GET /images/email-logo.png responded 200 in 0.2226 ms
2025-07-24 10:03:40 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.3023 ms
2025-07-24 10:03:40 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3807 ms
2025-07-24 10:03:40 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4069 ms
2025-07-24 10:03:40 [Information] HTTP GET /css/site.css.map responded 200 in 0.2493 ms
2025-07-24 10:03:40 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.1931 ms
2025-07-24 10:03:40 [Information] HTTP GET /js/index.js responded 200 in 0.1552 ms
2025-07-24 10:03:40 [Information] HTTP GET /js/components/utils.js responded 200 in 0.1646 ms
2025-07-24 10:03:40 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2590 ms
2025-07-24 10:03:40 [Information] HTTP GET /js/components/input.js responded 200 in 0.1773 ms
2025-07-24 10:03:40 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2614 ms
2025-07-24 10:03:40 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2862 ms
2025-07-24 10:03:40 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1940 ms
2025-07-24 10:03:40 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1999 ms
2025-07-24 10:03:40 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.2058 ms
2025-07-24 10:03:40 [Information] HTTP GET /images/login-background.png responded 200 in 0.5483 ms
2025-07-24 10:03:40 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.6295 ms
2025-07-24 10:03:40 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3413 ms
2025-07-24 10:03:42 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.6165 ms
2025-07-24 10:03:58 [Information] HTTP GET /Account/Login responded 200 in 2.4484 ms
2025-07-24 10:03:58 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3689 ms
2025-07-24 10:03:58 [Information] HTTP GET /images/logo.svg responded 200 in 0.3443 ms
2025-07-24 10:03:58 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2018 ms
2025-07-24 10:03:58 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2379 ms
2025-07-24 10:03:58 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2380 ms
2025-07-24 10:03:59 [Information] HTTP GET /Account/ForgotPassword responded 200 in 12.8685 ms
2025-07-24 10:03:59 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3747 ms
2025-07-24 10:03:59 [Information] HTTP GET /images/password-logo.png responded 200 in 0.1701 ms
2025-07-24 10:03:59 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.3426 ms
2025-07-24 10:04:06 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:04:06 [Information] HTTP POST /Account/ForgotPassword responded 200 in 9.1900 ms
2025-07-24 10:04:06 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4524 ms
2025-07-24 10:04:23 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.5452 ms
2025-07-24 10:04:23 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4566 ms
2025-07-24 10:04:23 [Information] HTTP GET /Account/Login responded 200 in 2.4215 ms
2025-07-24 10:04:23 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3830 ms
2025-07-24 10:04:24 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.1733 ms
2025-07-24 10:04:24 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3564 ms
2025-07-24 10:04:24 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 304 in 0.0774 ms
2025-07-24 10:08:56 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.6898 ms
2025-07-24 10:08:56 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3321 ms
2025-07-24 10:08:56 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.9901 ms
2025-07-24 10:08:56 [Information] HTTP GET /images/help.svg responded 200 in 0.4546 ms
2025-07-24 10:08:56 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.4614 ms
2025-07-24 10:08:56 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.7744 ms
2025-07-24 10:08:56 [Information] HTTP GET /css/site.css responded 200 in 0.7790 ms
2025-07-24 10:08:56 [Information] HTTP GET /images/email-logo.png responded 200 in 0.1919 ms
2025-07-24 10:08:56 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1642 ms
2025-07-24 10:08:56 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.5886 ms
2025-07-24 10:08:56 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3503 ms
2025-07-24 10:08:56 [Information] HTTP GET /js/index.js responded 200 in 0.1514 ms
2025-07-24 10:08:56 [Information] HTTP GET /css/site.css.map responded 200 in 0.2710 ms
2025-07-24 10:08:56 [Information] HTTP GET /js/components/utils.js responded 200 in 0.1662 ms
2025-07-24 10:08:56 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2160 ms
2025-07-24 10:08:56 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.0179 ms
2025-07-24 10:08:56 [Information] HTTP GET /js/components/input.js responded 200 in 0.2049 ms
2025-07-24 10:08:56 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2314 ms
2025-07-24 10:08:56 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2247 ms
2025-07-24 10:08:56 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2510 ms
2025-07-24 10:08:56 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1903 ms
2025-07-24 10:08:56 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.1836 ms
2025-07-24 10:08:56 [Information] HTTP GET /images/login-background.png responded 200 in 1.6744 ms
2025-07-24 10:08:56 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 2.6997 ms
2025-07-24 10:08:56 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2626 ms
2025-07-24 10:08:58 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.5463 ms
2025-07-24 10:09:09 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:09:09 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-24 10:09:09 [Debug] Configured "Basic" authentication
2025-07-24 10:09:09 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:09:10 [Information] Successfully executed WebUtilitiesProxy.GetContactNoFromEmailAsync in "00:00:00.3515898"ms
2025-07-24 10:09:10 [Information] Contact?$filter=(((No eq 'CT411980')))
2025-07-24 10:09:10 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:09:11 [Information] HTTP POST /Account/ForgotCTNumber responded 302 in 1518.3262 ms
2025-07-24 10:09:11 [Information] HTTP GET /Account/CTNumberSentSuccess responded 200 in 1.5354 ms
2025-07-24 10:09:11 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3601 ms
2025-07-24 10:09:13 [Information] HTTP GET /Account/Login responded 200 in 3.5145 ms
2025-07-24 10:09:13 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5104 ms
2025-07-24 10:09:13 [Information] HTTP GET /images/logo.svg responded 200 in 0.4460 ms
2025-07-24 10:09:13 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2106 ms
2025-07-24 10:09:13 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2119 ms
2025-07-24 10:09:13 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2980 ms
2025-07-24 10:09:15 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.1114 ms
2025-07-24 10:09:15 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4595 ms
2025-07-24 10:09:15 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 304 in 0.0581 ms
2025-07-24 10:09:56 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:09:56 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-24 10:09:56 [Debug] Configured "Basic" authentication
2025-07-24 10:09:56 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:09:56 [Information] Successfully executed WebUtilitiesProxy.GetContactNoFromEmailAsync in "00:00:00.3602547"ms
2025-07-24 10:09:56 [Information] Contact?$filter=(((No eq 'CT411980')))
2025-07-24 10:09:56 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:09:57 [Information] HTTP POST /Account/ForgotCTNumber responded 302 in 1267.5208 ms
2025-07-24 10:09:57 [Information] HTTP GET /Account/CTNumberSentSuccess responded 200 in 1.1928 ms
2025-07-24 10:09:57 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3390 ms
2025-07-24 10:09:59 [Information] HTTP GET /Account/Login responded 200 in 3.3605 ms
2025-07-24 10:09:59 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3418 ms
2025-07-24 10:10:03 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /images/logo.svg from origin: https://outlook.office.com because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:10:03 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /images/email-illustration.png from origin: https://outlook.office.com because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:10:03 [Information] HTTP OPTIONS /images/logo.svg responded 404 in 1.5365 ms
2025-07-24 10:10:03 [Information] HTTP OPTIONS /images/email-illustration.png responded 404 in 1.8335 ms
2025-07-24 10:10:03 [Information] HTTP GET /images/email-illustration.png responded 200 in 0.3575 ms
2025-07-24 10:10:03 [Information] HTTP GET /images/logo.svg responded 200 in 0.8699 ms
2025-07-24 10:10:12 [Information] HTTP GET /Account/ForgotPassword responded 200 in 2.0023 ms
2025-07-24 10:10:12 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4009 ms
2025-07-24 10:10:12 [Information] HTTP GET /images/password-logo.png responded 200 in 0.3722 ms
2025-07-24 10:10:12 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.5331 ms
2025-07-24 10:11:26 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.6968 ms
2025-07-24 10:11:26 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3228 ms
2025-07-24 10:11:26 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.6027 ms
2025-07-24 10:11:26 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.3730 ms
2025-07-24 10:11:26 [Information] HTTP GET /css/site.css responded 200 in 0.3643 ms
2025-07-24 10:11:26 [Information] HTTP GET /images/help.svg responded 200 in 0.3490 ms
2025-07-24 10:11:26 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.4032 ms
2025-07-24 10:11:27 [Information] HTTP GET /images/password-logo.png responded 200 in 0.3591 ms
2025-07-24 10:11:27 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2647 ms
2025-07-24 10:11:27 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.4403 ms
2025-07-24 10:11:27 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.5863 ms
2025-07-24 10:11:27 [Information] HTTP GET /css/site.css.map responded 200 in 0.3293 ms
2025-07-24 10:11:27 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.6660 ms
2025-07-24 10:11:27 [Information] HTTP GET /js/index.js responded 200 in 0.2739 ms
2025-07-24 10:11:27 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4498 ms
2025-07-24 10:11:27 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2098 ms
2025-07-24 10:11:27 [Information] HTTP GET /js/components/utils.js responded 200 in 0.3776 ms
2025-07-24 10:11:27 [Information] HTTP GET /js/components/input.js responded 200 in 0.3630 ms
2025-07-24 10:11:27 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.4988 ms
2025-07-24 10:11:27 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.4193 ms
2025-07-24 10:11:27 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.3300 ms
2025-07-24 10:11:27 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.4779 ms
2025-07-24 10:11:27 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.3399 ms
2025-07-24 10:11:27 [Information] HTTP GET /images/login-background.png responded 200 in 0.6126 ms
2025-07-24 10:11:27 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.5828 ms
2025-07-24 10:11:27 [Information] HTTP GET /images/favicon.ico responded 200 in 0.5024 ms
2025-07-24 10:11:33 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:11:33 [Information] HTTP POST /Account/ForgotPassword responded 200 in 3.5463 ms
2025-07-24 10:11:33 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3228 ms
2025-07-24 10:11:33 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0562 ms
2025-07-24 10:11:34 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.5993 ms
2025-07-24 10:11:42 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:11:42 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-24 10:11:42 [Debug] Configured "Basic" authentication
2025-07-24 10:11:42 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:11:42 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3702434"ms
2025-07-24 10:11:42 [Information] HTTP POST /Account/ForgotPassword responded 200 in 386.7544 ms
2025-07-24 10:11:42 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3293 ms
2025-07-24 10:11:42 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0666 ms
2025-07-24 10:11:50 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:11:50 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-24 10:11:50 [Debug] Configured "Basic" authentication
2025-07-24 10:11:50 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:11:51 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3297565"ms
2025-07-24 10:11:51 [Information] HTTP POST /Account/ForgotPassword responded 200 in 335.7902 ms
2025-07-24 10:11:51 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3535 ms
2025-07-24 10:11:51 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0632 ms
2025-07-24 10:11:53 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:11:53 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-24 10:11:53 [Debug] Configured "Basic" authentication
2025-07-24 10:11:53 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:11:54 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3268873"ms
2025-07-24 10:11:54 [Information] HTTP POST /Account/ForgotPassword responded 200 in 333.0698 ms
2025-07-24 10:11:54 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3619 ms
2025-07-24 10:14:47 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:14:47 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-24 10:14:47 [Debug] Configured "Basic" authentication
2025-07-24 10:14:47 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:14:47 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3677300"ms
2025-07-24 10:14:47 [Information] HTTP POST /Account/ForgotPassword responded 200 in 372.3485 ms
2025-07-24 10:14:47 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3581 ms
2025-07-24 10:14:47 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.9045 ms
2025-07-24 10:14:47 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2290 ms
2025-07-24 10:14:47 [Information] HTTP GET /images/help.svg responded 200 in 0.2405 ms
2025-07-24 10:14:47 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.3331 ms
2025-07-24 10:14:47 [Information] HTTP GET /css/site.css responded 200 in 0.7969 ms
2025-07-24 10:14:47 [Information] HTTP GET /images/password-logo.png responded 200 in 0.2230 ms
2025-07-24 10:14:47 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1633 ms
2025-07-24 10:14:47 [Information] HTTP GET /css/site.css.map responded 200 in 0.2373 ms
2025-07-24 10:14:47 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 2.8067 ms
2025-07-24 10:14:47 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 2.9090 ms
2025-07-24 10:14:47 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 3.7761 ms
2025-07-24 10:14:47 [Information] HTTP GET /js/index.js responded 200 in 0.1832 ms
2025-07-24 10:14:47 [Information] HTTP GET /js/components/utils.js responded 200 in 0.1538 ms
2025-07-24 10:14:47 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1871 ms
2025-07-24 10:14:47 [Information] HTTP GET /js/components/input.js responded 200 in 0.2149 ms
2025-07-24 10:14:47 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1724 ms
2025-07-24 10:14:47 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.1707 ms
2025-07-24 10:14:47 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2496 ms
2025-07-24 10:14:47 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1602 ms
2025-07-24 10:14:47 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.1405 ms
2025-07-24 10:14:47 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.2592 ms
2025-07-24 10:14:47 [Information] HTTP GET /images/login-background.png responded 200 in 0.7666 ms
2025-07-24 10:14:47 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.1311 ms
2025-07-24 10:14:47 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3281 ms
2025-07-24 10:14:51 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:14:51 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-24 10:14:51 [Debug] Configured "Basic" authentication
2025-07-24 10:14:51 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:14:51 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3331509"ms
2025-07-24 10:14:51 [Information] HTTP POST /Account/ForgotPassword responded 200 in 338.9686 ms
2025-07-24 10:14:51 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2988 ms
2025-07-24 10:15:03 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.7789 ms
2025-07-24 10:15:03 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3852 ms
2025-07-24 10:15:03 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2891 ms
2025-07-24 10:15:03 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0501 ms
2025-07-24 10:15:03 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0792 ms
2025-07-24 10:15:04 [Information] HTTP GET /Account/Login responded 200 in 2.9601 ms
2025-07-24 10:15:04 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3128 ms
2025-07-24 10:15:04 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.3740 ms
2025-07-24 10:15:04 [Information] HTTP GET /images/logo.svg responded 200 in 5.1049 ms
2025-07-24 10:15:04 [Information] HTTP GET /js/pages/login.js responded 200 in 0.5220 ms
2025-07-24 10:15:05 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.1267 ms
2025-07-24 10:15:05 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3135 ms
2025-07-24 10:15:05 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.1542 ms
2025-07-24 10:15:05 [Information] HTTP GET /images/email-logo.png responded 200 in 0.4176 ms
2025-07-24 10:15:13 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:15:13 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-24 10:15:13 [Debug] Configured "Basic" authentication
2025-07-24 10:15:13 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:15:13 [Information] Successfully executed WebUtilitiesProxy.GetContactNoFromEmailAsync in "00:00:00.3511436"ms
2025-07-24 10:15:13 [Information] Contact?$filter=(((No eq 'CT411980')))
2025-07-24 10:15:13 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:15:14 [Information] HTTP POST /Account/ForgotCTNumber responded 302 in 1448.2369 ms
2025-07-24 10:15:14 [Information] HTTP GET /Account/CTNumberSentSuccess responded 200 in 0.8663 ms
2025-07-24 10:15:14 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3624 ms
2025-07-24 10:15:24 [Information] HTTP GET /Account/Login responded 200 in 1.9339 ms
2025-07-24 10:15:24 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2853 ms
2025-07-24 10:15:24 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0826 ms
2025-07-24 10:15:25 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.1538 ms
2025-07-24 10:15:25 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3036 ms
2025-07-24 10:15:25 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0595 ms
2025-07-24 10:15:26 [Information] HTTP GET /Account/Login responded 200 in 2.0019 ms
2025-07-24 10:15:26 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3024 ms
2025-07-24 10:15:27 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.0162 ms
2025-07-24 10:15:27 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2802 ms
2025-07-24 10:15:27 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 304 in 0.0589 ms
2025-07-24 10:16:17 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.5048 ms
2025-07-24 10:16:17 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2690 ms
2025-07-24 10:16:17 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.6984 ms
2025-07-24 10:16:17 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4938 ms
2025-07-24 10:16:17 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1937 ms
2025-07-24 10:16:17 [Information] HTTP GET /css/site.css responded 200 in 0.3182 ms
2025-07-24 10:16:17 [Information] HTTP GET /images/help.svg responded 200 in 0.3367 ms
2025-07-24 10:16:17 [Information] HTTP GET /images/email-logo.png responded 200 in 0.2305 ms
2025-07-24 10:16:17 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1378 ms
2025-07-24 10:16:17 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.3833 ms
2025-07-24 10:16:17 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.2902 ms
2025-07-24 10:16:17 [Information] HTTP GET /js/index.js responded 200 in 0.1737 ms
2025-07-24 10:16:17 [Information] HTTP GET /js/components/utils.js responded 200 in 0.1794 ms
2025-07-24 10:16:17 [Information] HTTP GET /css/site.css.map responded 200 in 0.2539 ms
2025-07-24 10:16:17 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1792 ms
2025-07-24 10:16:17 [Information] HTTP GET /js/components/input.js responded 200 in 0.1466 ms
2025-07-24 10:16:17 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.5926 ms
2025-07-24 10:16:17 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1857 ms
2025-07-24 10:16:17 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.1746 ms
2025-07-24 10:16:17 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2241 ms
2025-07-24 10:16:17 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2163 ms
2025-07-24 10:16:17 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.1866 ms
2025-07-24 10:16:17 [Information] HTTP GET /images/login-background.png responded 200 in 0.6785 ms
2025-07-24 10:16:17 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.9101 ms
2025-07-24 10:16:17 [Information] HTTP GET /images/favicon.ico responded 200 in 0.1981 ms
2025-07-24 10:16:18 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.3093 ms
2025-07-24 10:16:28 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:16:28 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-24 10:16:28 [Debug] Configured "Basic" authentication
2025-07-24 10:16:28 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:16:29 [Information] Successfully executed WebUtilitiesProxy.GetContactNoFromEmailAsync in "00:00:00.3451846"ms
2025-07-24 10:16:29 [Information] Contact?$filter=(((No eq 'CT411980')))
2025-07-24 10:16:29 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:16:30 [Information] HTTP POST /Account/ForgotCTNumber responded 302 in 1353.4932 ms
2025-07-24 10:16:30 [Information] HTTP GET /Account/CTNumberSentSuccess responded 200 in 0.8454 ms
2025-07-24 10:16:30 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3454 ms
2025-07-24 10:16:34 [Information] HTTP GET /Account/Login responded 200 in 3.1226 ms
2025-07-24 10:16:34 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2972 ms
2025-07-24 10:16:34 [Information] HTTP GET /images/logo.svg responded 200 in 0.2647 ms
2025-07-24 10:16:34 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2416 ms
2025-07-24 10:16:34 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2537 ms
2025-07-24 10:16:34 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2008 ms
2025-07-24 10:17:24 [Information] HTTP GET /Account/Login responded 200 in 1.9065 ms
2025-07-24 10:17:24 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2707 ms
2025-07-24 10:17:24 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.9559 ms
2025-07-24 10:17:24 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2145 ms
2025-07-24 10:17:24 [Information] HTTP GET /css/site.css responded 200 in 0.4481 ms
2025-07-24 10:17:24 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1413 ms
2025-07-24 10:17:24 [Information] HTTP GET /images/help.svg responded 200 in 0.2164 ms
2025-07-24 10:17:24 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2426 ms
2025-07-24 10:17:24 [Information] HTTP GET /images/logo.svg responded 200 in 0.2798 ms
2025-07-24 10:17:24 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2478 ms
2025-07-24 10:17:24 [Information] HTTP GET /css/site.css.map responded 200 in 0.2030 ms
2025-07-24 10:17:24 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4036 ms
2025-07-24 10:17:24 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.7018 ms
2025-07-24 10:17:24 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3757 ms
2025-07-24 10:17:24 [Information] HTTP GET /js/index.js responded 200 in 0.1872 ms
2025-07-24 10:17:24 [Information] HTTP GET /js/components/utils.js responded 200 in 0.2061 ms
2025-07-24 10:17:24 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1490 ms
2025-07-24 10:17:24 [Information] HTTP GET /js/components/input.js responded 200 in 0.1994 ms
2025-07-24 10:17:24 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1995 ms
2025-07-24 10:17:24 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.1889 ms
2025-07-24 10:17:24 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1388 ms
2025-07-24 10:17:24 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1792 ms
2025-07-24 10:17:24 [Information] HTTP GET /js/pages/login.js responded 200 in 0.1789 ms
2025-07-24 10:17:24 [Information] HTTP GET /images/login-background.png responded 200 in 0.6663 ms
2025-07-24 10:17:24 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.0036 ms
2025-07-24 10:17:25 [Information] HTTP GET /images/favicon.ico responded 200 in 0.1609 ms
2025-07-24 10:17:26 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 0.9105 ms
2025-07-24 10:17:26 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3267 ms
2025-07-24 10:17:26 [Information] HTTP GET /images/email-logo.png responded 200 in 0.1999 ms
2025-07-24 10:17:26 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2191 ms
2025-07-24 10:17:26 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.2452 ms
2025-07-24 10:17:27 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.4014 ms
2025-07-24 10:17:35 [Information] HTTP GET /Account/Login responded 200 in 2.0809 ms
2025-07-24 10:17:35 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3794 ms
2025-07-24 10:17:36 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.0558 ms
2025-07-24 10:17:36 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3959 ms
2025-07-24 10:17:36 [Information] HTTP GET /images/password-logo.png responded 200 in 0.3053 ms
2025-07-24 10:17:36 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.2301 ms
2025-07-24 10:17:41 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:17:41 [Information] HTTP POST /Account/ForgotPassword responded 200 in 4.1216 ms
2025-07-24 10:17:41 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3866 ms
2025-07-24 10:17:41 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0779 ms
2025-07-24 10:17:49 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:17:49 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-24 10:17:49 [Debug] Configured "Basic" authentication
2025-07-24 10:17:49 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:17:49 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3557165"ms
2025-07-24 10:17:49 [Information] HTTP POST /Account/ForgotPassword responded 200 in 361.4257 ms
2025-07-24 10:17:49 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3398 ms
2025-07-24 10:17:49 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.1317 ms
2025-07-24 10:17:50 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.2691 ms
2025-07-24 10:17:50 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5412 ms
2025-07-24 10:17:50 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0792 ms
2025-07-24 10:18:06 [Information] HTTP GET /Account/Login responded 200 in 1.9137 ms
2025-07-24 10:18:06 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3155 ms
2025-07-24 10:18:08 [Information] HTTP GET /Account/Setup responded 200 in 1.7272 ms
2025-07-24 10:18:08 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2914 ms
2025-07-24 10:18:08 [Information] HTTP GET /images/step-active.png responded 200 in 4.3163 ms
2025-07-24 10:18:08 [Information] HTTP GET /images/step-next.png responded 200 in 3.9256 ms
2025-07-24 10:18:20 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:18:20 [Information] Contact?$filter=(No eq 'CT123444')
2025-07-24 10:18:20 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123444","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT042799","Company_Name":"DO NOT USE Complete Fertility Ltd - CP","Anonymise":false,"IntegrationCustomerNo":"CFLTD-CP","Name":"CT123444","First_Name":"CT123444","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123444","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"7504697","Bill_to_Customer_No":"CFLTD-CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Finished - Dormant Account","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-06","Date_Received_Documents":"2020-07-03","Deregistration_Date":"2021-07-25","Last_Date_Modified":"2023-01-24","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:18:20 [Information] HTTP POST /Account/Setup responded 200 in 197.6389 ms
2025-07-24 10:18:20 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3973 ms
2025-07-24 10:18:20 [Information] HTTP GET /images/user-logo.png responded 200 in 0.3992 ms
2025-07-24 10:18:20 [Information] HTTP GET /images/shield-tick.svg responded 200 in 0.4831 ms
2025-07-24 10:18:20 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0734 ms
2025-07-24 10:18:20 [Information] HTTP GET /js/pages/create-account-birthdate-step.js responded 200 in 0.5605 ms
2025-07-24 10:18:34 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:18:34 [Information] Contact?$filter=(No eq 'CT123444' and DOB eq 1994-01-01 and Type eq 'Person' and Status eq 'Active')
2025-07-24 10:18:34 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[]}
2025-07-24 10:18:34 [Information] HTTP POST /Account/Setup responded 200 in 73.9977 ms
2025-07-24 10:18:34 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2938 ms
2025-07-24 10:18:37 [Information] Contact?$filter=(No eq 'CT123444')
2025-07-24 10:18:37 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123444","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT042799","Company_Name":"DO NOT USE Complete Fertility Ltd - CP","Anonymise":false,"IntegrationCustomerNo":"CFLTD-CP","Name":"CT123444","First_Name":"CT123444","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123444","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"7504697","Bill_to_Customer_No":"CFLTD-CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Finished - Dormant Account","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-06","Date_Received_Documents":"2020-07-03","Deregistration_Date":"2021-07-25","Last_Date_Modified":"2023-01-24","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:18:37 [Information] HTTP GET /Account/Setup/ responded 200 in 67.7731 ms
2025-07-24 10:18:37 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2963 ms
2025-07-24 10:18:37 [Information] HTTP GET /images/info-circle-warning.svg responded 200 in 0.4333 ms
2025-07-24 10:18:37 [Information] HTTP GET /images/close.svg responded 200 in 0.2940 ms
2025-07-24 10:18:43 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:18:43 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-24 10:18:43 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:18:43 [Information] HTTP POST /Account/Setup responded 200 in 94.3194 ms
2025-07-24 10:18:43 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3007 ms
2025-07-24 10:18:58 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:18:58 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-24 10:18:58 [Debug] Configured "Basic" authentication
2025-07-24 10:18:58 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:18:58 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3242846"ms
2025-07-24 10:18:58 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-24 10:18:58 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:18:59 [Information] Executing WebUtilitiesProxy.UpdatePortalOptInAsync
2025-07-24 10:18:59 [Debug] Configured "Basic" authentication
2025-07-24 10:18:59 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:19:00 [Information] Successfully executed WebUtilitiesProxy.UpdatePortalOptInAsync in "00:00:00.3635760"ms
2025-07-24 10:19:00 [Information] HTTP POST /Account/Setup responded 200 in 1796.4626 ms
2025-07-24 10:19:00 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3247 ms
2025-07-24 10:19:00 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0661 ms
2025-07-24 10:19:20 [Information] HTTP GET /Account/Login responded 200 in 2.4836 ms
2025-07-24 10:19:20 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3184 ms
2025-07-24 10:19:21 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 0.9091 ms
2025-07-24 10:19:21 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2514 ms
2025-07-24 10:19:21 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 304 in 0.0646 ms
2025-07-24 10:21:42 [Information] HTTP GET /Account/Login responded 200 in 3.4017 ms
2025-07-24 10:21:42 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2603 ms
2025-07-24 10:21:42 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0839 ms
2025-07-24 10:21:43 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.6167 ms
2025-07-24 10:21:43 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5309 ms
2025-07-24 10:21:43 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0871 ms
2025-07-24 10:24:53 [Information] HTTP GET /Account/Login responded 200 in 3.1048 ms
2025-07-24 10:24:53 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2762 ms
2025-07-24 10:24:53 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0883 ms
2025-07-24 10:25:21 [Information] HTTP GET /Account/Login responded 200 in 1.9932 ms
2025-07-24 10:25:21 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2956 ms
2025-07-24 10:25:21 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.5115 ms
2025-07-24 10:25:21 [Information] HTTP GET /images/help.svg responded 200 in 0.2814 ms
2025-07-24 10:25:21 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.3723 ms
2025-07-24 10:25:21 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2480 ms
2025-07-24 10:25:21 [Information] HTTP GET /css/site.css responded 200 in 0.4413 ms
2025-07-24 10:25:21 [Information] HTTP GET /images/logo.svg responded 200 in 0.4463 ms
2025-07-24 10:25:21 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.1548 ms
2025-07-24 10:25:21 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2467 ms
2025-07-24 10:25:21 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.3681 ms
2025-07-24 10:25:21 [Information] HTTP GET /css/site.css.map responded 200 in 0.1965 ms
2025-07-24 10:25:21 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.6518 ms
2025-07-24 10:25:21 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.5533 ms
2025-07-24 10:25:21 [Information] HTTP GET /js/index.js responded 200 in 0.2275 ms
2025-07-24 10:25:21 [Information] HTTP GET /js/components/utils.js responded 200 in 0.2136 ms
2025-07-24 10:25:21 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1410 ms
2025-07-24 10:25:21 [Information] HTTP GET /js/components/input.js responded 200 in 0.2357 ms
2025-07-24 10:25:21 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2347 ms
2025-07-24 10:25:21 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.1969 ms
2025-07-24 10:25:21 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1646 ms
2025-07-24 10:25:21 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2119 ms
2025-07-24 10:25:21 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2085 ms
2025-07-24 10:25:21 [Information] HTTP GET /images/login-background.png responded 200 in 0.7138 ms
2025-07-24 10:25:21 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.1730 ms
2025-07-24 10:25:21 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2306 ms
2025-07-24 10:25:22 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.2680 ms
2025-07-24 10:25:33 [Information] HTTP GET /Account/Setup responded 200 in 1.0964 ms
2025-07-24 10:25:33 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3514 ms
2025-07-24 10:25:33 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0579 ms
2025-07-24 10:25:33 [Information] HTTP GET /images/step-active.png responded 200 in 0.3346 ms
2025-07-24 10:25:33 [Information] HTTP GET /images/step-next.png responded 200 in 0.3780 ms
2025-07-24 10:28:09 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /images/email-illustration.png from origin: https://outlook.office.com because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:28:09 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /images/logo.svg from origin: https://outlook.office.com because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:28:09 [Information] HTTP OPTIONS /images/logo.svg responded 404 in 1.6626 ms
2025-07-24 10:28:09 [Information] HTTP OPTIONS /images/email-illustration.png responded 404 in 1.9905 ms
2025-07-24 10:28:09 [Information] HTTP GET /images/logo.svg responded 200 in 0.2572 ms
2025-07-24 10:28:09 [Information] HTTP GET /images/email-illustration.png responded 200 in 0.4869 ms
2025-07-24 10:28:16 [Information] HTTP GET /Account/Setup responded 200 in 1.6263 ms
2025-07-24 10:28:16 [Information] HTTP GET /images/help.svg responded 200 in 0.2510 ms
2025-07-24 10:28:16 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.8620 ms
2025-07-24 10:28:16 [Information] HTTP GET /css/site.css responded 200 in 0.3505 ms
2025-07-24 10:28:16 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.3429 ms
2025-07-24 10:28:16 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4369 ms
2025-07-24 10:28:16 [Information] HTTP GET /images/logo.svg responded 200 in 0.2817 ms
2025-07-24 10:28:16 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.1608 ms
2025-07-24 10:28:16 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.3477 ms
2025-07-24 10:28:16 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3420 ms
2025-07-24 10:28:16 [Information] HTTP GET /js/index.js responded 200 in 0.2510 ms
2025-07-24 10:28:16 [Information] HTTP GET /js/components/utils.js responded 200 in 0.1612 ms
2025-07-24 10:28:16 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2477 ms
2025-07-24 10:28:16 [Information] HTTP GET /js/components/input.js responded 200 in 0.1426 ms
2025-07-24 10:28:16 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2031 ms
2025-07-24 10:28:16 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2106 ms
2025-07-24 10:28:16 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1849 ms
2025-07-24 10:28:16 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1418 ms
2025-07-24 10:28:16 [Information] HTTP GET /images/step-active.png responded 200 in 0.5884 ms
2025-07-24 10:28:16 [Information] HTTP GET /images/step-next.png responded 200 in 0.2344 ms
2025-07-24 10:28:16 [Information] HTTP GET /images/login-background.png responded 200 in 0.5365 ms
2025-07-24 10:28:16 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2673 ms
2025-07-24 10:28:23 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.4253 ms
2025-07-24 10:28:25 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:28:25 [Information] Contact?$filter=(No eq 'CT123313')
2025-07-24 10:28:25 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123313","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT024972","Company_Name":"CARE Fertility Derby - CP","Anonymise":false,"IntegrationCustomerNo":"CFD-CP","Name":"CT123313","First_Name":"CT123313","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123313","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"**********","Bill_to_Customer_No":"CFD-CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Finished - Dormant Account","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-03","Date_Received_Documents":"2020-07-03","Deregistration_Date":"2021-03-29","Last_Date_Modified":"2023-01-24","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:28:25 [Information] HTTP POST /Account/Setup responded 200 in 210.7799 ms
2025-07-24 10:28:25 [Information] HTTP GET /images/user-logo.png responded 200 in 0.2861 ms
2025-07-24 10:28:25 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1978 ms
2025-07-24 10:28:25 [Information] HTTP GET /images/shield-tick.svg responded 200 in 0.2055 ms
2025-07-24 10:28:25 [Information] HTTP GET /js/pages/create-account-birthdate-step.js responded 200 in 0.3093 ms
2025-07-24 10:28:27 [Information] Contact?$filter=(No eq 'CT123313')
2025-07-24 10:28:27 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123313","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT024972","Company_Name":"CARE Fertility Derby - CP","Anonymise":false,"IntegrationCustomerNo":"CFD-CP","Name":"CT123313","First_Name":"CT123313","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123313","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"**********","Bill_to_Customer_No":"CFD-CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Finished - Dormant Account","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-03","Date_Received_Documents":"2020-07-03","Deregistration_Date":"2021-03-29","Last_Date_Modified":"2023-01-24","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:28:27 [Information] HTTP GET /Account/Setup/ responded 200 in 68.9542 ms
2025-07-24 10:28:27 [Information] HTTP GET /images/info-circle-warning.svg responded 200 in 0.2404 ms
2025-07-24 10:28:27 [Information] HTTP GET /images/close.svg responded 200 in 0.1999 ms
2025-07-24 10:28:31 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:28:31 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-24 10:28:32 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-24","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:28:32 [Information] HTTP POST /Account/Setup responded 200 in 77.9505 ms
2025-07-24 10:28:32 [Information] HTTP GET /images/email-logo.png responded 200 in 0.2396 ms
2025-07-24 10:28:39 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:28:39 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-24 10:28:39 [Debug] Configured "Basic" authentication
2025-07-24 10:28:39 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:28:40 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3301693"ms
2025-07-24 10:28:40 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-24 10:28:40 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-24","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:28:41 [Information] Executing WebUtilitiesProxy.UpdatePortalOptInAsync
2025-07-24 10:28:41 [Debug] Configured "Basic" authentication
2025-07-24 10:28:41 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:28:41 [Information] Successfully executed WebUtilitiesProxy.UpdatePortalOptInAsync in "00:00:00.3476869"ms
2025-07-24 10:28:41 [Information] HTTP POST /Account/Setup responded 200 in 1657.7839 ms
2025-07-24 10:28:41 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0745 ms
2025-07-24 10:28:42 [Information] HTTP GET /Account/Login responded 200 in 2.7057 ms
2025-07-24 10:28:42 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2698 ms
2025-07-24 10:28:42 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2838 ms
2025-07-24 10:28:43 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.1436 ms
2025-07-24 10:28:43 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.4376 ms
2025-07-24 10:28:47 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:28:47 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-24 10:28:47 [Debug] Configured "Basic" authentication
2025-07-24 10:28:47 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:28:48 [Information] Successfully executed WebUtilitiesProxy.GetContactNoFromEmailAsync in "00:00:00.4989173"ms
2025-07-24 10:28:48 [Information] Contact?$filter=(((No eq 'CT411980')))
2025-07-24 10:28:48 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQTBBREVBTVFBNUFEZ0FNQUFBQUFBQTExOzExMTMxNzg0MDkzMDsn\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-24","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:28:49 [Information] HTTP POST /Account/ForgotCTNumber responded 302 in 1450.1980 ms
2025-07-24 10:28:49 [Information] HTTP GET /Account/CTNumberSentSuccess responded 200 in 0.7194 ms
2025-07-24 10:28:50 [Information] HTTP GET /Account/Login responded 200 in 1.8338 ms
2025-07-24 10:28:51 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.0568 ms
2025-07-24 10:28:51 [Information] HTTP GET /images/password-logo.png responded 200 in 0.2406 ms
2025-07-24 10:28:51 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.2635 ms
2025-07-24 10:28:57 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:28:57 [Information] HTTP POST /Account/ForgotPassword responded 200 in 3.8493 ms
2025-07-24 10:29:03 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:29:03 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-24 10:29:03 [Debug] Configured "Basic" authentication
2025-07-24 10:29:03 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-24 10:29:03 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3228946"ms
2025-07-24 10:29:03 [Information] HTTP POST /Account/ForgotPassword responded 200 in 327.2056 ms
2025-07-24 10:29:06 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.4417 ms
2025-07-24 10:29:07 [Information] HTTP GET /Account/Login responded 200 in 1.8856 ms
2025-07-24 10:30:12 [Information] HTTP GET /Account/Login responded 200 in 2.0494 ms
2025-07-24 10:30:12 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3380 ms
2025-07-24 10:30:12 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0799 ms
2025-07-24 10:30:12 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0267 ms
2025-07-24 10:30:17 [Information] HTTP POST /Account/Login responded 200 in 158.8618 ms
2025-07-24 10:30:18 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-24 10:30:18 [Information] Contact?$filter=(No eq 'CT123333')
2025-07-24 10:30:18 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123333","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT100884","Company_Name":"Addenbrookes Hospital Fasenra - CP","Anonymise":false,"IntegrationCustomerNo":"ADHF-CP","Name":"CT123333","First_Name":"CT123333","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123333","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"3482798","Bill_to_Customer_No":"ADHF-CP","Therapy":"FASENRA","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"SEVERE E ASTHMA","Status":"Finished Patient Moved Centre","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2020-07-03","Date_Received_Documents":"2020-07-27","Deregistration_Date":"2021-01-12","Last_Date_Modified":"2021-10-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Experienced","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"No","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":true,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"***********","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"CT100898","Prescription_Handler_Name":"PX requests Fasenra Addenbrookes Hospital","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:30:18 [Information] Contact?$filter=(No eq 'CT100884')
2025-07-24 10:30:18 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXhBREFBTUFBNEFEZ0FOQUFBQUFBQTExOzExMTE5MzM2MTEyMDsn\"","No":"CT100884","Type":"Company","Organizational_Level_Code":"","Company_No":"CT100884","Company_Name":"Addenbrookes Hospital Fasenra - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Fasenra - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"66 ST. HUGHS GREEN","Address_2":"","City":"GORLESTON","County":"GREAT YARMOUTH","Post_Code":"NR31 7NH","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL FASENRA - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"FASENRA","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2019-11-14","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2021-10-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-24 10:30:18 [Information] {"Username":"CT123333","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNEAFSPPVV4D:0000000B","TimeStamp":"2025-07-24T07:30:18.4291758","ProcessId":41908,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-24 10:30:18 [Information] HTTP POST /Account/Login responded 200 in 290.0185 ms
2025-07-24 10:30:18 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2758 ms
