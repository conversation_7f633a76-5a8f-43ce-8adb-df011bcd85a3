2025-07-25 10:17:27 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-25 10:17:27 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-25 10:17:27 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-25 10:17:27 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-25 10:17:27 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-25 10:17:27 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-25 10:17:27 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-25 10:17:27 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-25 10:17:27 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-25 10:17:27 [Information] Seeding database...
2025-07-25 10:17:28 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-25 10:17:28 [Debug] Clients already populated
2025-07-25 10:17:28 [Debug] IdentityResources already populated
2025-07-25 10:17:28 [Debug] ApiScopes already populated
2025-07-25 10:17:28 [Debug] OIDC IdentityProviders already populated
2025-07-25 10:17:28 [Information] Done seeding database. Exiting.
2025-07-25 10:17:29 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-25 10:17:29 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-25 10:17:29 [Debug] Login Url: /Account/Login
2025-07-25 10:17:29 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-25 10:17:29 [Debug] Logout Url: /Account/Logout
2025-07-25 10:17:29 [Debug] ConsentUrl Url: /consent
2025-07-25 10:17:29 [Debug] Consent Return Url Parameter: returnUrl
2025-07-25 10:17:29 [Debug] Error Url: /home/<USER>
2025-07-25 10:17:29 [Debug] Error Id Parameter: errorId
2025-07-25 10:17:29 [Information] HTTP GET / responded 302 in 70.7929 ms
2025-07-25 10:17:30 [Information] HTTP GET /Account/Login responded 200 in 158.3474 ms
2025-07-25 10:17:30 [Information] HTTP GET /css/site.css responded 304 in 2.9028 ms
2025-07-25 10:17:30 [Information] HTTP GET /js/components/utils.js responded 304 in 0.2177 ms
2025-07-25 10:17:30 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.0928 ms
2025-07-25 10:17:30 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0979 ms
2025-07-25 10:17:30 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0859 ms
2025-07-25 10:17:34 [Information] HTTP GET /Account/Setup responded 200 in 38.6432 ms
2025-07-25 10:17:46 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-25 10:17:46 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-25 10:17:46 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-24","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-25 10:17:46 [Information] HTTP POST /Account/Setup responded 200 in 414.3468 ms
2025-07-25 10:17:46 [Information] HTTP GET /images/arrow-left.svg responded 200 in 6.5858 ms
2025-07-25 10:17:46 [Information] HTTP GET /images/email-logo.png responded 200 in 10.6210 ms
2025-07-25 10:17:51 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-25 10:17:51 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-25 10:17:51 [Debug] Configured "Basic" authentication
2025-07-25 10:17:51 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-25 10:17:52 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.6511329"ms
2025-07-25 10:17:52 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-25 10:17:52 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-24","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-25 10:17:53 [Information] Executing WebUtilitiesProxy.UpdatePortalOptInAsync
2025-07-25 10:17:53 [Debug] Configured "Basic" authentication
2025-07-25 10:17:53 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-25 10:17:53 [Information] Successfully executed WebUtilitiesProxy.UpdatePortalOptInAsync in "00:00:00.3703536"ms
2025-07-25 10:17:53 [Information] HTTP POST /Account/Setup responded 200 in 2314.3463 ms
2025-07-25 10:19:33 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /images/email-illustration.png from origin: https://outlook.office.com because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-25 10:19:33 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /images/logo.svg from origin: https://outlook.office.com because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-25 10:19:33 [Information] HTTP OPTIONS /images/email-illustration.png responded 404 in 1.9467 ms
2025-07-25 10:19:33 [Information] HTTP OPTIONS /images/logo.svg responded 404 in 2.1199 ms
2025-07-25 10:19:36 [Information] HTTP GET /Account/Setup responded 200 in 71.0094 ms
2025-07-25 10:19:36 [Information] HTTP GET /images/password-logo.png responded 200 in 0.7843 ms
2025-07-25 10:19:36 [Information] HTTP GET /images/step-check.svg responded 200 in 0.3294 ms
2025-07-25 10:19:36 [Information] HTTP GET /images/check.svg responded 200 in 0.3148 ms
2025-07-25 10:23:00 [Information] HTTP GET /images/check-green.svg responded 200 in 0.3846 ms
2025-07-25 10:23:03 [Information] HTTP GET /images/eye.svg responded 200 in 0.3246 ms
2025-07-25 10:23:15 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-25 10:23:15 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-25 10:23:15 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-25","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-25 10:23:15 [Information] Executing WebUtilitiesProxy.ChangePasswordByEmailAsync
2025-07-25 10:23:15 [Debug] Configured "Basic" authentication
2025-07-25 10:23:15 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-25 10:23:15 [Information] Successfully executed WebUtilitiesProxy.ChangePasswordByEmailAsync in "00:00:00.3767821"ms
2025-07-25 10:23:15 [Information] Executing WebUtilitiesProxy.ChangePasswordByUserAsync
2025-07-25 10:23:15 [Debug] Configured "Basic" authentication
2025-07-25 10:23:15 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-25 10:23:16 [Information] Successfully executed WebUtilitiesProxy.ChangePasswordByUserAsync in "00:00:00.3443731"ms
2025-07-25 10:23:16 [Information] HTTP POST /Account/Setup responded 200 in 1015.6009 ms
2025-07-25 10:23:16 [Information] HTTP GET /images/success-icon.svg responded 200 in 0.4141 ms
2025-07-25 10:23:17 [Information] HTTP GET /Account/Setup/ responded 200 in 9.4375 ms
2025-07-25 10:23:17 [Information] HTTP GET /images/2fa-logo.png responded 200 in 0.2838 ms
2025-07-25 10:23:17 [Information] HTTP GET /js/pages/setup-2fa-step.js responded 200 in 0.5909 ms
2025-07-25 10:23:38 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.7904 ms
2025-07-25 10:23:38 [Information] HTTP GET /css/site.css.map responded 304 in 0.1215 ms
2025-07-25 10:24:44 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-25 10:24:44 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-25 10:24:44 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-25 10:24:44 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-25 10:24:44 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-25 10:24:44 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-25 10:24:44 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-25 10:24:44 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-25 10:24:44 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-25 10:24:44 [Information] Seeding database...
2025-07-25 10:24:45 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-25 10:24:45 [Debug] Clients already populated
2025-07-25 10:24:45 [Debug] IdentityResources already populated
2025-07-25 10:24:45 [Debug] ApiScopes already populated
2025-07-25 10:24:45 [Debug] OIDC IdentityProviders already populated
2025-07-25 10:24:45 [Information] Done seeding database. Exiting.
2025-07-25 10:24:45 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-25 10:24:45 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-25 10:24:46 [Debug] Login Url: /Account/Login
2025-07-25 10:24:46 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-25 10:24:46 [Debug] Logout Url: /Account/Logout
2025-07-25 10:24:46 [Debug] ConsentUrl Url: /consent
2025-07-25 10:24:46 [Debug] Consent Return Url Parameter: returnUrl
2025-07-25 10:24:46 [Debug] Error Url: /home/<USER>
2025-07-25 10:24:46 [Debug] Error Id Parameter: errorId
2025-07-25 10:24:46 [Information] HTTP GET / responded 302 in 66.9439 ms
2025-07-25 10:24:46 [Information] HTTP GET /Account/Login responded 200 in 161.6533 ms
2025-07-25 10:24:53 [Information] HTTP GET /Account/Setup/ responded 200 in 71.1423 ms
2025-07-25 10:24:53 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 2.2790 ms
2025-07-25 10:24:55 [Information] HTTP GET /Account/Setup/ responded 200 in 13.6462 ms
2025-07-25 10:24:55 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.8132 ms
2025-07-25 10:24:55 [Information] HTTP GET /images/help.svg responded 200 in 4.4193 ms
2025-07-25 10:24:55 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 9.9276 ms
2025-07-25 10:24:55 [Information] HTTP GET /images/help-blue.svg responded 200 in 4.4663 ms
2025-07-25 10:24:55 [Information] HTTP GET /css/site.css responded 200 in 4.7797 ms
2025-07-25 10:24:55 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 4.9292 ms
2025-07-25 10:24:55 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.7334 ms
2025-07-25 10:24:55 [Information] HTTP GET /images/2fa-logo.png responded 200 in 0.7310 ms
2025-07-25 10:24:55 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.9678 ms
2025-07-25 10:24:55 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 1.3115 ms
2025-07-25 10:24:55 [Information] HTTP GET /css/site.css.map responded 200 in 0.5743 ms
2025-07-25 10:24:55 [Information] HTTP GET /js/index.js responded 200 in 0.4442 ms
2025-07-25 10:24:55 [Information] HTTP GET /js/components/utils.js responded 200 in 0.4876 ms
2025-07-25 10:24:55 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 5.1636 ms
2025-07-25 10:24:55 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.4819 ms
2025-07-25 10:24:55 [Information] HTTP GET /js/components/input.js responded 200 in 0.3368 ms
2025-07-25 10:24:55 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.4717 ms
2025-07-25 10:24:55 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3564 ms
2025-07-25 10:24:55 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.4518 ms
2025-07-25 10:24:55 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2878 ms
2025-07-25 10:24:55 [Information] HTTP GET /js/pages/setup-2fa-step.js responded 200 in 0.5177 ms
2025-07-25 10:24:55 [Information] HTTP GET /images/step-check.svg responded 200 in 0.4659 ms
2025-07-25 10:24:55 [Information] HTTP GET /images/step-active.png responded 200 in 0.4451 ms
2025-07-25 10:24:56 [Information] HTTP GET /images/login-background.png responded 200 in 1.6400 ms
2025-07-25 10:24:56 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 2.4648 ms
2025-07-25 10:24:56 [Information] HTTP GET /images/favicon.ico responded 200 in 0.4183 ms
2025-07-25 10:24:57 [Information] HTTP GET /images/error-warning.svg responded 200 in 1.1052 ms
2025-07-25 10:26:33 [Information] HTTP GET /Account/Setup/ responded 200 in 3.9017 ms
2025-07-25 10:26:33 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4349 ms
2025-07-25 10:26:33 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.8544 ms
2025-07-25 10:26:33 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4713 ms
2025-07-25 10:26:33 [Information] HTTP GET /images/help.svg responded 200 in 0.3533 ms
2025-07-25 10:26:33 [Information] HTTP GET /css/site.css responded 200 in 0.5344 ms
2025-07-25 10:26:33 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2829 ms
2025-07-25 10:26:33 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2944 ms
2025-07-25 10:26:33 [Information] HTTP GET /images/2fa-logo.png responded 200 in 0.3538 ms
2025-07-25 10:26:33 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.8120 ms
2025-07-25 10:26:33 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.9075 ms
2025-07-25 10:26:33 [Information] HTTP GET /css/site.css.map responded 200 in 0.4359 ms
2025-07-25 10:26:33 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.8150 ms
2025-07-25 10:26:33 [Information] HTTP GET /js/index.js responded 200 in 0.4058 ms
2025-07-25 10:26:33 [Information] HTTP GET /js/components/utils.js responded 200 in 0.3154 ms
2025-07-25 10:26:33 [Information] HTTP GET /js/components/input.js responded 200 in 0.3550 ms
2025-07-25 10:26:33 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3580 ms
2025-07-25 10:26:33 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2816 ms
2025-07-25 10:26:33 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2193 ms
2025-07-25 10:26:33 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2398 ms
2025-07-25 10:26:33 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1893 ms
2025-07-25 10:26:33 [Information] HTTP GET /js/pages/setup-2fa-step.js responded 200 in 0.3027 ms
2025-07-25 10:26:33 [Information] HTTP GET /images/step-check.svg responded 200 in 0.2372 ms
2025-07-25 10:26:33 [Information] HTTP GET /images/step-active.png responded 200 in 0.2353 ms
2025-07-25 10:26:34 [Information] HTTP GET /images/login-background.png responded 200 in 0.5963 ms
2025-07-25 10:26:34 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.2394 ms
2025-07-25 10:26:34 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2565 ms
2025-07-25 10:26:39 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.5893 ms
2025-07-25 10:27:08 [Information] HTTP GET /Account/Setup/ responded 200 in 1.8583 ms
2025-07-25 10:27:08 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3963 ms
2025-07-25 10:27:08 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.6691 ms
2025-07-25 10:27:08 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.3425 ms
2025-07-25 10:27:08 [Information] HTTP GET /css/site.css responded 200 in 0.3671 ms
2025-07-25 10:27:08 [Information] HTTP GET /images/help.svg responded 200 in 0.2118 ms
2025-07-25 10:27:08 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2109 ms
2025-07-25 10:27:08 [Information] HTTP GET /images/2fa-logo.png responded 200 in 0.2469 ms
2025-07-25 10:27:08 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1319 ms
2025-07-25 10:27:08 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4020 ms
2025-07-25 10:27:08 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4359 ms
2025-07-25 10:27:08 [Information] HTTP GET /css/site.css.map responded 200 in 0.2479 ms
2025-07-25 10:27:08 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.0982 ms
2025-07-25 10:27:08 [Information] HTTP GET /js/index.js responded 200 in 0.2128 ms
2025-07-25 10:27:08 [Information] HTTP GET /js/components/utils.js responded 200 in 0.1824 ms
2025-07-25 10:27:08 [Information] HTTP GET /js/components/input.js responded 200 in 0.2357 ms
2025-07-25 10:27:08 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2637 ms
2025-07-25 10:27:08 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2104 ms
2025-07-25 10:27:08 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3513 ms
2025-07-25 10:27:08 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2622 ms
2025-07-25 10:27:08 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1916 ms
2025-07-25 10:27:08 [Information] HTTP GET /js/pages/setup-2fa-step.js responded 200 in 0.2573 ms
2025-07-25 10:27:08 [Information] HTTP GET /images/step-check.svg responded 200 in 0.2848 ms
2025-07-25 10:27:08 [Information] HTTP GET /images/step-active.png responded 200 in 0.2243 ms
2025-07-25 10:27:08 [Information] HTTP GET /images/login-background.png responded 200 in 0.5915 ms
2025-07-25 10:27:08 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.8110 ms
2025-07-25 10:27:08 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3309 ms
2025-07-25 10:27:13 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.5597 ms
2025-07-25 10:28:47 [Information] HTTP GET /Account/Setup/ responded 200 in 2.2881 ms
2025-07-25 10:28:47 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3994 ms
2025-07-25 10:28:47 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.7185 ms
2025-07-25 10:28:47 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.3224 ms
2025-07-25 10:28:47 [Information] HTTP GET /css/site.css responded 200 in 0.3632 ms
2025-07-25 10:28:47 [Information] HTTP GET /images/help.svg responded 200 in 0.1490 ms
2025-07-25 10:28:47 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1640 ms
2025-07-25 10:28:47 [Information] HTTP GET /images/2fa-logo.png responded 200 in 0.2988 ms
2025-07-25 10:28:47 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.3480 ms
2025-07-25 10:28:47 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3832 ms
2025-07-25 10:28:47 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4223 ms
2025-07-25 10:28:47 [Information] HTTP GET /css/site.css.map responded 200 in 0.3005 ms
2025-07-25 10:28:47 [Information] HTTP GET /js/index.js responded 200 in 0.1378 ms
2025-07-25 10:28:47 [Information] HTTP GET /js/components/utils.js responded 200 in 0.1150 ms
2025-07-25 10:28:47 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.0004 ms
2025-07-25 10:28:47 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2004 ms
2025-07-25 10:28:47 [Information] HTTP GET /js/components/input.js responded 200 in 0.1792 ms
2025-07-25 10:28:47 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1739 ms
2025-07-25 10:28:47 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2715 ms
2025-07-25 10:28:47 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1423 ms
2025-07-25 10:28:47 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2097 ms
2025-07-25 10:28:47 [Information] HTTP GET /js/pages/setup-2fa-step.js responded 200 in 0.1897 ms
2025-07-25 10:28:47 [Information] HTTP GET /images/step-check.svg responded 200 in 0.2386 ms
2025-07-25 10:28:47 [Information] HTTP GET /images/step-active.png responded 200 in 0.1940 ms
2025-07-25 10:28:47 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.1742 ms
2025-07-25 10:28:47 [Information] HTTP GET /images/login-background.png responded 200 in 0.6489 ms
2025-07-25 10:28:48 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3211 ms
2025-07-25 10:31:53 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-25 10:31:53 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-25 10:31:53 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-25 10:31:53 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-25 10:31:53 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-25 10:31:53 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-25 10:31:53 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-25 10:31:53 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-25 10:31:53 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-25 10:31:53 [Information] Seeding database...
2025-07-25 10:31:54 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-25 10:31:54 [Debug] Clients already populated
2025-07-25 10:31:54 [Debug] IdentityResources already populated
2025-07-25 10:31:54 [Debug] ApiScopes already populated
2025-07-25 10:31:54 [Debug] OIDC IdentityProviders already populated
2025-07-25 10:31:55 [Information] Done seeding database. Exiting.
2025-07-25 10:31:55 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-25 10:31:55 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-25 10:31:56 [Debug] Login Url: /Account/Login
2025-07-25 10:31:56 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-25 10:31:56 [Debug] Logout Url: /Account/Logout
2025-07-25 10:31:56 [Debug] ConsentUrl Url: /consent
2025-07-25 10:31:56 [Debug] Consent Return Url Parameter: returnUrl
2025-07-25 10:31:56 [Debug] Error Url: /home/<USER>
2025-07-25 10:31:56 [Debug] Error Id Parameter: errorId
2025-07-25 10:31:56 [Information] HTTP GET / responded 302 in 58.4462 ms
2025-07-25 10:31:56 [Information] HTTP GET /Account/Login responded 200 in 163.8703 ms
2025-07-25 10:31:56 [Information] HTTP GET /images/info-circle.svg responded 200 in 1.5797 ms
2025-07-25 10:31:56 [Information] HTTP GET /images/logo.svg responded 200 in 6.2170 ms
2025-07-25 10:31:56 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.4096 ms
2025-07-25 10:31:56 [Information] HTTP GET /js/pages/login.js responded 200 in 0.4347 ms
2025-07-25 10:31:56 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 1.0514 ms
2025-07-25 10:32:01 [Information] HTTP GET /Account/Setup/ responded 200 in 41.8736 ms
2025-07-25 10:32:01 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.1299 ms
2025-07-25 10:32:01 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 2.1656 ms
2025-07-25 10:32:01 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 1.0953 ms
2025-07-25 10:32:01 [Information] HTTP GET /images/help.svg responded 200 in 0.4282 ms
2025-07-25 10:32:01 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.4076 ms
2025-07-25 10:32:01 [Information] HTTP GET /css/site.css responded 200 in 0.6734 ms
2025-07-25 10:32:01 [Information] HTTP GET /images/2fa-logo.png responded 200 in 0.3292 ms
2025-07-25 10:32:01 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2711 ms
2025-07-25 10:32:01 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 1.0299 ms
2025-07-25 10:32:01 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.5530 ms
2025-07-25 10:32:01 [Information] HTTP GET /js/index.js responded 200 in 0.2320 ms
2025-07-25 10:32:01 [Information] HTTP GET /css/site.css.map responded 200 in 0.4323 ms
2025-07-25 10:32:01 [Information] HTTP GET /js/components/utils.js responded 200 in 0.2743 ms
2025-07-25 10:32:01 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 3.8842 ms
2025-07-25 10:32:01 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3222 ms
2025-07-25 10:32:01 [Information] HTTP GET /js/components/input.js responded 200 in 0.4106 ms
2025-07-25 10:32:01 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2780 ms
2025-07-25 10:32:01 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2840 ms
2025-07-25 10:32:01 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.3137 ms
2025-07-25 10:32:01 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2569 ms
2025-07-25 10:32:01 [Information] HTTP GET /js/pages/setup-2fa-step.js responded 200 in 0.3873 ms
2025-07-25 10:32:01 [Information] HTTP GET /images/step-check.svg responded 200 in 0.3231 ms
2025-07-25 10:32:01 [Information] HTTP GET /images/step-active.png responded 200 in 0.2666 ms
2025-07-25 10:32:01 [Information] HTTP GET /images/login-background.png responded 200 in 1.2551 ms
2025-07-25 10:32:01 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 2.0838 ms
2025-07-25 10:32:02 [Information] HTTP GET /images/favicon.ico responded 200 in 0.4099 ms
2025-07-25 10:33:09 [Information] HTTP GET /Account/Setup/ responded 200 in 8.3513 ms
2025-07-25 10:33:09 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4623 ms
2025-07-25 10:33:09 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4073 ms
2025-07-25 10:33:09 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.9677 ms
2025-07-25 10:33:09 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2539 ms
2025-07-25 10:33:09 [Information] HTTP GET /css/site.css responded 200 in 0.5238 ms
2025-07-25 10:33:09 [Information] HTTP GET /images/help.svg responded 200 in 0.2814 ms
2025-07-25 10:33:09 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2260 ms
2025-07-25 10:33:09 [Information] HTTP GET /images/2fa-logo.png responded 200 in 0.2876 ms
2025-07-25 10:33:09 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.7962 ms
2025-07-25 10:33:09 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.6980 ms
2025-07-25 10:33:09 [Information] HTTP GET /js/index.js responded 200 in 0.3682 ms
2025-07-25 10:33:09 [Information] HTTP GET /js/components/utils.js responded 200 in 0.2492 ms
2025-07-25 10:33:09 [Information] HTTP GET /css/site.css.map responded 200 in 0.2961 ms
2025-07-25 10:33:09 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 4.2247 ms
2025-07-25 10:33:09 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3011 ms
2025-07-25 10:33:09 [Information] HTTP GET /js/components/input.js responded 200 in 0.2015 ms
2025-07-25 10:33:09 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.4813 ms
2025-07-25 10:33:09 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.5588 ms
2025-07-25 10:33:09 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2637 ms
2025-07-25 10:33:09 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1233 ms
2025-07-25 10:33:09 [Information] HTTP GET /js/pages/setup-2fa-step.js responded 200 in 0.2831 ms
2025-07-25 10:33:09 [Information] HTTP GET /images/step-check.svg responded 200 in 0.2737 ms
2025-07-25 10:33:09 [Information] HTTP GET /images/step-active.png responded 200 in 0.2257 ms
2025-07-25 10:33:10 [Information] HTTP GET /images/login-background.png responded 200 in 3.0358 ms
2025-07-25 10:33:10 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 3.5653 ms
2025-07-25 10:33:10 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2975 ms
2025-07-25 10:36:28 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-25 10:36:28 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-25 10:36:28 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-25 10:36:28 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-25 10:36:28 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-25 10:36:28 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-25 10:36:28 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-25 10:36:28 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-25 10:36:28 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-25 10:36:28 [Information] Seeding database...
2025-07-25 10:36:29 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-25 10:36:30 [Debug] Clients already populated
2025-07-25 10:36:30 [Debug] IdentityResources already populated
2025-07-25 10:36:30 [Debug] ApiScopes already populated
2025-07-25 10:36:30 [Debug] OIDC IdentityProviders already populated
2025-07-25 10:36:30 [Information] Done seeding database. Exiting.
2025-07-25 10:36:30 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-25 10:36:30 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-25 10:36:31 [Debug] Login Url: /Account/Login
2025-07-25 10:36:31 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-25 10:36:31 [Debug] Logout Url: /Account/Logout
2025-07-25 10:36:31 [Debug] ConsentUrl Url: /consent
2025-07-25 10:36:31 [Debug] Consent Return Url Parameter: returnUrl
2025-07-25 10:36:31 [Debug] Error Url: /home/<USER>
2025-07-25 10:36:31 [Debug] Error Id Parameter: errorId
2025-07-25 10:36:31 [Information] HTTP GET / responded 302 in 59.1599 ms
2025-07-25 10:36:31 [Information] HTTP GET /Account/Login responded 200 in 157.5725 ms
2025-07-25 10:36:31 [Information] HTTP GET /images/info-circle.svg responded 200 in 6.1115 ms
2025-07-25 10:36:31 [Information] HTTP GET /images/logo.svg responded 200 in 6.4880 ms
2025-07-25 10:36:31 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.6503 ms
2025-07-25 10:36:31 [Information] HTTP GET /js/pages/login.js responded 200 in 0.4374 ms
2025-07-25 10:36:31 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 1.4304 ms
2025-07-25 10:36:35 [Information] HTTP GET /Account/Setup/ responded 200 in 39.6068 ms
2025-07-25 10:36:35 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 1.2765 ms
2025-07-25 10:36:35 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.1785 ms
2025-07-25 10:36:35 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2668 ms
2025-07-25 10:36:35 [Information] HTTP GET /images/help.svg responded 200 in 0.2770 ms
2025-07-25 10:36:35 [Information] HTTP GET /css/site.css responded 200 in 0.5702 ms
2025-07-25 10:36:35 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2137 ms
2025-07-25 10:36:35 [Information] HTTP GET /images/2fa-logo.png responded 200 in 0.3385 ms
2025-07-25 10:36:35 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.3321 ms
2025-07-25 10:36:35 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.6454 ms
2025-07-25 10:36:35 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.5259 ms
2025-07-25 10:36:35 [Information] HTTP GET /js/index.js responded 200 in 0.2848 ms
2025-07-25 10:36:35 [Information] HTTP GET /js/components/utils.js responded 200 in 0.3284 ms
2025-07-25 10:36:35 [Information] HTTP GET /css/site.css.map responded 200 in 0.3553 ms
2025-07-25 10:36:35 [Information] HTTP GET /js/components/input.js responded 200 in 0.2485 ms
2025-07-25 10:36:35 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.4740 ms
2025-07-25 10:36:35 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.2532 ms
2025-07-25 10:36:35 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.5200 ms
2025-07-25 10:36:35 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.7027 ms
2025-07-25 10:36:35 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2855 ms
2025-07-25 10:36:35 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.3492 ms
2025-07-25 10:36:35 [Information] HTTP GET /js/pages/setup-2fa-step.js responded 200 in 0.4030 ms
2025-07-25 10:36:35 [Information] HTTP GET /images/step-check.svg responded 200 in 0.3733 ms
2025-07-25 10:36:35 [Information] HTTP GET /images/step-active.png responded 200 in 0.2810 ms
2025-07-25 10:36:35 [Information] HTTP GET /images/login-background.png responded 200 in 1.2871 ms
2025-07-25 10:36:35 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.7625 ms
2025-07-25 10:36:35 [Information] HTTP GET /images/favicon.ico responded 200 in 0.4022 ms
2025-07-25 10:36:39 [Information] HTTP GET /images/error-warning.svg responded 200 in 1.1166 ms
2025-07-25 10:36:47 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-25 10:36:47 [Information] [ServiceBus] Queue 'user-data-management-queue' already exists.
2025-07-25 10:36:49 [Information] HTTP POST /Account/Setup responded 200 in 2262.1565 ms
2025-07-25 10:36:49 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.7388 ms
2025-07-25 10:36:49 [Information] HTTP GET /images/email-logo.png responded 200 in 0.4764 ms
2025-07-25 10:36:49 [Information] HTTP GET /js/pages/2fe-step.js responded 200 in 0.3746 ms
2025-07-25 10:37:12 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /images/email-illustration.png from origin: https://outlook.office.com because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-25 10:37:12 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /images/logo.svg from origin: https://outlook.office.com because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-25 10:37:12 [Information] HTTP OPTIONS /images/email-illustration.png responded 404 in 2.8838 ms
2025-07-25 10:37:12 [Information] HTTP OPTIONS /images/logo.svg responded 404 in 4.3644 ms
2025-07-25 10:37:12 [Information] HTTP GET /images/email-illustration.png responded 200 in 1.0457 ms
2025-07-25 10:37:12 [Information] HTTP GET /images/logo.svg responded 200 in 0.7219 ms
2025-07-25 10:37:52 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-25 10:37:52 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-25 10:37:52 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-25 10:37:52 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-25 10:37:52 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-25 10:37:52 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-25 10:37:52 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-25 10:37:52 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-25 10:37:52 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-25 10:37:52 [Information] Seeding database...
2025-07-25 10:37:53 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-25 10:37:54 [Debug] Clients already populated
2025-07-25 10:37:54 [Debug] IdentityResources already populated
2025-07-25 10:37:54 [Debug] ApiScopes already populated
2025-07-25 10:37:54 [Debug] OIDC IdentityProviders already populated
2025-07-25 10:37:54 [Information] Done seeding database. Exiting.
2025-07-25 10:37:54 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-25 10:37:54 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-25 10:37:55 [Debug] Login Url: /Account/Login
2025-07-25 10:37:55 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-25 10:37:55 [Debug] Logout Url: /Account/Logout
2025-07-25 10:37:55 [Debug] ConsentUrl Url: /consent
2025-07-25 10:37:55 [Debug] Consent Return Url Parameter: returnUrl
2025-07-25 10:37:55 [Debug] Error Url: /home/<USER>
2025-07-25 10:37:55 [Debug] Error Id Parameter: errorId
2025-07-25 10:37:55 [Information] HTTP GET / responded 302 in 58.6685 ms
2025-07-25 10:37:55 [Information] HTTP GET /Account/Login responded 200 in 164.4001 ms
2025-07-25 10:37:55 [Information] HTTP GET /images/info-circle.svg responded 200 in 4.8691 ms
2025-07-25 10:37:55 [Information] HTTP GET /images/logo.svg responded 200 in 5.4196 ms
2025-07-25 10:37:55 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.4431 ms
2025-07-25 10:37:55 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2855 ms
2025-07-25 10:37:55 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 1.0192 ms
2025-07-25 10:37:58 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 6.7362 ms
2025-07-25 10:37:58 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.7199 ms
2025-07-25 10:37:59 [Information] HTTP GET /Account/Setup/ responded 200 in 33.9603 ms
2025-07-25 10:37:59 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4857 ms
2025-07-25 10:37:59 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2535 ms
2025-07-25 10:37:59 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.1940 ms
2025-07-25 10:37:59 [Information] HTTP GET /images/help.svg responded 200 in 0.3378 ms
2025-07-25 10:37:59 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.3240 ms
2025-07-25 10:37:59 [Information] HTTP GET /css/site.css responded 200 in 0.5639 ms
2025-07-25 10:37:59 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.3156 ms
2025-07-25 10:37:59 [Information] HTTP GET /images/2fa-logo.png responded 200 in 0.3592 ms
2025-07-25 10:37:59 [Information] HTTP GET /css/site.css.map responded 200 in 0.4201 ms
2025-07-25 10:37:59 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.5936 ms
2025-07-25 10:37:59 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.6646 ms
2025-07-25 10:37:59 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.8623 ms
2025-07-25 10:37:59 [Information] HTTP GET /js/index.js responded 200 in 0.3030 ms
2025-07-25 10:37:59 [Information] HTTP GET /js/components/utils.js responded 200 in 0.2646 ms
2025-07-25 10:37:59 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2562 ms
2025-07-25 10:37:59 [Information] HTTP GET /js/components/input.js responded 200 in 0.3451 ms
2025-07-25 10:37:59 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2459 ms
2025-07-25 10:37:59 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2638 ms
2025-07-25 10:37:59 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2501 ms
2025-07-25 10:37:59 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2141 ms
2025-07-25 10:37:59 [Information] HTTP GET /js/pages/setup-2fa-step.js responded 200 in 0.2690 ms
2025-07-25 10:37:59 [Information] HTTP GET /images/step-check.svg responded 200 in 0.2634 ms
2025-07-25 10:37:59 [Information] HTTP GET /images/step-active.png responded 200 in 0.2476 ms
2025-07-25 10:37:59 [Information] HTTP GET /images/login-background.png responded 200 in 0.9849 ms
2025-07-25 10:37:59 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 28.3396 ms
2025-07-25 10:38:00 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3267 ms
2025-07-25 10:38:02 [Information] HTTP GET /images/error-warning.svg responded 200 in 1.2641 ms
2025-07-25 10:38:13 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-25 10:38:14 [Information] [ServiceBus] Queue 'user-data-management-queue' already exists.
2025-07-25 10:38:15 [Information] HTTP POST /Account/Setup responded 200 in 2033.7398 ms
2025-07-25 10:38:15 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6604 ms
2025-07-25 10:38:15 [Information] HTTP GET /images/email-logo.png responded 200 in 0.4497 ms
2025-07-25 10:38:15 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.1692 ms
2025-07-25 10:38:15 [Information] HTTP GET /js/pages/2fe-step.js responded 200 in 0.5111 ms
