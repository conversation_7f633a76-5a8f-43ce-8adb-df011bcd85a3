2025-07-23 19:52:20 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 19:52:20 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 19:52:20 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 19:52:20 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 19:52:20 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 19:52:20 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 19:52:20 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 19:52:20 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 19:52:20 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 19:52:20 [Information] Seeding database...
2025-07-23 19:52:21 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 19:52:21 [Debug] Clients already populated
2025-07-23 19:52:21 [Debug] IdentityResources already populated
2025-07-23 19:52:21 [Debug] ApiScopes already populated
2025-07-23 19:52:21 [Debug] OIDC IdentityProviders already populated
2025-07-23 19:52:22 [Information] Done seeding database. Exiting.
2025-07-23 19:52:22 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 19:52:22 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 19:52:23 [Debug] Login Url: /Account/Login
2025-07-23 19:52:23 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 19:52:23 [Debug] Logout Url: /Account/Logout
2025-07-23 19:52:23 [Debug] ConsentUrl Url: /consent
2025-07-23 19:52:23 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 19:52:23 [Debug] Error Url: /home/<USER>
2025-07-23 19:52:23 [Debug] Error Id Parameter: errorId
2025-07-23 19:52:23 [Information] HTTP GET / responded 302 in 66.5069 ms
2025-07-23 19:52:23 [Information] HTTP GET /Account/Login responded 200 in 164.0492 ms
2025-07-23 19:52:23 [Information] HTTP GET /images/help.svg responded 200 in 3.2445 ms
2025-07-23 19:52:23 [Information] HTTP GET /images/help-blue.svg responded 200 in 3.2420 ms
2025-07-23 19:52:23 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 7.0457 ms
2025-07-23 19:52:23 [Information] HTTP GET /css/site.css responded 200 in 7.0435 ms
2025-07-23 19:52:23 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 8.5591 ms
2025-07-23 19:52:23 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.8585 ms
2025-07-23 19:52:23 [Information] HTTP GET /images/logo.svg responded 200 in 0.8652 ms
2025-07-23 19:52:23 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.3821 ms
2025-07-23 19:52:23 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4686 ms
2025-07-23 19:52:23 [Information] HTTP GET /js/index.js responded 200 in 0.2145 ms
2025-07-23 19:52:23 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.5342 ms
2025-07-23 19:52:23 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2063 ms
2025-07-23 19:52:23 [Information] HTTP GET /js/components/input.js responded 200 in 0.2137 ms
2025-07-23 19:52:23 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1840 ms
2025-07-23 19:52:23 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2131 ms
2025-07-23 19:52:23 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1650 ms
2025-07-23 19:52:23 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2295 ms
2025-07-23 19:52:23 [Information] HTTP GET /images/login-background.png responded 200 in 1.0768 ms
2025-07-23 19:52:23 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3284 ms
2025-07-23 19:59:34 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 19:59:34 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 19:59:34 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 19:59:34 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 19:59:34 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 19:59:34 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 19:59:34 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 19:59:34 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 19:59:34 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 19:59:34 [Information] Seeding database...
2025-07-23 19:59:36 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 19:59:36 [Debug] Clients already populated
2025-07-23 19:59:36 [Debug] IdentityResources already populated
2025-07-23 19:59:36 [Debug] ApiScopes already populated
2025-07-23 19:59:36 [Debug] OIDC IdentityProviders already populated
2025-07-23 19:59:36 [Information] Done seeding database. Exiting.
2025-07-23 19:59:36 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 19:59:36 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 19:59:37 [Debug] Login Url: /Account/Login
2025-07-23 19:59:37 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 19:59:37 [Debug] Logout Url: /Account/Logout
2025-07-23 19:59:37 [Debug] ConsentUrl Url: /consent
2025-07-23 19:59:37 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 19:59:37 [Debug] Error Url: /home/<USER>
2025-07-23 19:59:37 [Debug] Error Id Parameter: errorId
2025-07-23 19:59:37 [Information] HTTP GET / responded 302 in 66.2432 ms
2025-07-23 19:59:37 [Information] HTTP GET /Account/Login responded 200 in 157.8662 ms
2025-07-23 19:59:37 [Information] HTTP GET /js/pages/login.js responded 304 in 2.0517 ms
2025-07-23 19:59:37 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 2.0858 ms
2025-07-23 20:03:58 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 20:03:58 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 20:03:58 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 20:03:58 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 20:03:58 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 20:03:58 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 20:03:58 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 20:03:58 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 20:03:58 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 20:03:58 [Information] Seeding database...
2025-07-23 20:03:59 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 20:04:00 [Debug] Clients already populated
2025-07-23 20:04:00 [Debug] IdentityResources already populated
2025-07-23 20:04:00 [Debug] ApiScopes already populated
2025-07-23 20:04:00 [Debug] OIDC IdentityProviders already populated
2025-07-23 20:04:00 [Information] Done seeding database. Exiting.
2025-07-23 20:04:00 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 20:04:00 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 20:04:01 [Debug] Login Url: /Account/Login
2025-07-23 20:04:01 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 20:04:01 [Debug] Logout Url: /Account/Logout
2025-07-23 20:04:01 [Debug] ConsentUrl Url: /consent
2025-07-23 20:04:01 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 20:04:01 [Debug] Error Url: /home/<USER>
2025-07-23 20:04:01 [Debug] Error Id Parameter: errorId
2025-07-23 20:04:01 [Information] HTTP GET / responded 302 in 68.7960 ms
2025-07-23 20:04:01 [Information] HTTP GET /Account/Login responded 200 in 152.4539 ms
2025-07-23 20:04:01 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 2.8327 ms
2025-07-23 20:04:01 [Information] HTTP GET /js/pages/login.js responded 200 in 10.8549 ms
2025-07-23 20:04:17 [Information] HTTP GET /Account/Login responded 200 in 49.2411 ms
2025-07-23 20:04:17 [Information] HTTP GET /js/pages/login.js responded 304 in 0.3193 ms
2025-07-23 20:04:23 [Information] HTTP GET /Account/Login responded 200 in 14.7916 ms
2025-07-23 20:05:33 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:05:33 [Information] HTTP POST /Account/Login responded 302 in 35.9008 ms
2025-07-23 20:05:33 [Information] HTTP GET / responded 302 in 1.3203 ms
2025-07-23 20:05:33 [Information] HTTP GET /Account/Login responded 200 in 3.8306 ms
2025-07-23 20:05:33 [Information] HTTP GET /js/pages/login.js responded 304 in 0.1841 ms
2025-07-23 20:05:33 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.1799 ms
2025-07-23 20:06:34 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 1.2753 ms
2025-07-23 20:06:34 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 4.5416 ms
2025-07-23 20:06:34 [Information] HTTP GET /css/site.css.map responded 200 in 1.1461 ms
2025-07-23 20:06:34 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 4.3813 ms
2025-07-23 20:06:38 [Information] HTTP GET /Account/Login responded 200 in 4.5796 ms
2025-07-23 20:06:38 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6656 ms
2025-07-23 20:06:38 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.3894 ms
2025-07-23 20:06:38 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.3223 ms
2025-07-23 20:06:38 [Information] HTTP GET /images/help.svg responded 200 in 0.3751 ms
2025-07-23 20:06:38 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4552 ms
2025-07-23 20:06:38 [Information] HTTP GET /css/site.css responded 200 in 0.4842 ms
2025-07-23 20:06:38 [Information] HTTP GET /images/logo.svg responded 200 in 0.5304 ms
2025-07-23 20:06:38 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2289 ms
2025-07-23 20:06:38 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.4039 ms
2025-07-23 20:06:38 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.7007 ms
2025-07-23 20:06:38 [Information] HTTP GET /css/site.css.map responded 200 in 0.4558 ms
2025-07-23 20:06:38 [Information] HTTP GET /js/index.js responded 200 in 0.2226 ms
2025-07-23 20:06:38 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.5139 ms
2025-07-23 20:06:38 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.9910 ms
2025-07-23 20:06:38 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3618 ms
2025-07-23 20:06:38 [Information] HTTP GET /js/components/input.js responded 200 in 0.3214 ms
2025-07-23 20:06:38 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.3518 ms
2025-07-23 20:06:38 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3304 ms
2025-07-23 20:06:38 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.4478 ms
2025-07-23 20:06:38 [Information] HTTP GET /js/pages/login.js responded 200 in 0.4303 ms
2025-07-23 20:06:38 [Information] HTTP GET /images/login-background.png responded 200 in 1.4891 ms
2025-07-23 20:06:38 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.8733 ms
2025-07-23 20:06:38 [Information] HTTP GET /images/favicon.ico responded 200 in 0.4263 ms
2025-07-23 20:07:02 [Information] HTTP GET /Account/Login responded 200 in 2.7530 ms
2025-07-23 20:07:02 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4832 ms
2025-07-23 20:07:02 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4235 ms
2025-07-23 20:07:02 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.8816 ms
2025-07-23 20:07:02 [Information] HTTP GET /css/site.css responded 200 in 0.3708 ms
2025-07-23 20:07:02 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1967 ms
2025-07-23 20:07:02 [Information] HTTP GET /images/help.svg responded 200 in 0.2042 ms
2025-07-23 20:07:02 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.3090 ms
2025-07-23 20:07:02 [Information] HTTP GET /images/logo.svg responded 200 in 0.3745 ms
2025-07-23 20:07:02 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2630 ms
2025-07-23 20:07:02 [Information] HTTP GET /css/site.css.map responded 200 in 0.2961 ms
2025-07-23 20:07:02 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 3.0113 ms
2025-07-23 20:07:02 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 4.2993 ms
2025-07-23 20:07:02 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4587 ms
2025-07-23 20:07:02 [Information] HTTP GET /js/index.js responded 200 in 0.4485 ms
2025-07-23 20:07:02 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2581 ms
2025-07-23 20:07:02 [Information] HTTP GET /js/components/input.js responded 200 in 0.1449 ms
2025-07-23 20:07:02 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2899 ms
2025-07-23 20:07:02 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2349 ms
2025-07-23 20:07:02 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2378 ms
2025-07-23 20:07:02 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2611 ms
2025-07-23 20:07:02 [Information] HTTP GET /images/login-background.png responded 200 in 0.5737 ms
2025-07-23 20:07:02 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.6978 ms
2025-07-23 20:07:02 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3713 ms
2025-07-23 20:08:59 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.4875 ms
2025-07-23 20:11:00 [Information] HTTP GET /Account/Login responded 200 in 34.9490 ms
2025-07-23 20:11:00 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6907 ms
2025-07-23 20:11:00 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.6075 ms
2025-07-23 20:11:00 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2422 ms
2025-07-23 20:11:00 [Information] HTTP GET /images/help.svg responded 200 in 0.3641 ms
2025-07-23 20:11:00 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.5543 ms
2025-07-23 20:11:00 [Information] HTTP GET /css/site.css responded 200 in 0.5273 ms
2025-07-23 20:11:00 [Information] HTTP GET /images/logo.svg responded 200 in 0.3303 ms
2025-07-23 20:11:00 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.1721 ms
2025-07-23 20:11:00 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2450 ms
2025-07-23 20:11:00 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.3152 ms
2025-07-23 20:11:00 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3887 ms
2025-07-23 20:11:00 [Information] HTTP GET /js/index.js responded 200 in 0.2134 ms
2025-07-23 20:11:00 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2179 ms
2025-07-23 20:11:00 [Information] HTTP GET /css/site.css.map responded 200 in 0.3971 ms
2025-07-23 20:11:00 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.0893 ms
2025-07-23 20:11:00 [Information] HTTP GET /js/components/input.js responded 200 in 0.1597 ms
2025-07-23 20:11:00 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1567 ms
2025-07-23 20:11:00 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1923 ms
2025-07-23 20:11:00 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.4277 ms
2025-07-23 20:11:00 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2538 ms
2025-07-23 20:11:00 [Information] HTTP GET /images/login-background.png responded 200 in 0.7537 ms
2025-07-23 20:11:00 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.3331 ms
2025-07-23 20:11:00 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2144 ms
2025-07-23 20:11:13 [Information] HTTP GET /Account/Login responded 200 in 2.8177 ms
2025-07-23 20:11:13 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.1480 ms
2025-07-23 20:11:13 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4100 ms
2025-07-23 20:11:13 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.6065 ms
2025-07-23 20:11:13 [Information] HTTP GET /css/site.css responded 200 in 0.3968 ms
2025-07-23 20:11:13 [Information] HTTP GET /images/help.svg responded 200 in 0.1805 ms
2025-07-23 20:11:13 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.3088 ms
2025-07-23 20:11:13 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2368 ms
2025-07-23 20:11:13 [Information] HTTP GET /images/logo.svg responded 200 in 0.3468 ms
2025-07-23 20:11:13 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2425 ms
2025-07-23 20:11:13 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.3360 ms
2025-07-23 20:11:13 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3536 ms
2025-07-23 20:11:13 [Information] HTTP GET /js/index.js responded 200 in 0.1908 ms
2025-07-23 20:11:13 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1567 ms
2025-07-23 20:11:13 [Information] HTTP GET /js/components/input.js responded 200 in 0.1694 ms
2025-07-23 20:11:13 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1663 ms
2025-07-23 20:11:13 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2031 ms
2025-07-23 20:11:13 [Information] HTTP GET /css/site.css.map responded 200 in 0.1924 ms
2025-07-23 20:11:13 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.2776 ms
2025-07-23 20:11:13 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1342 ms
2025-07-23 20:11:13 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3500 ms
2025-07-23 20:11:13 [Information] HTTP GET /images/login-background.png responded 200 in 0.5018 ms
2025-07-23 20:11:13 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.2289 ms
2025-07-23 20:11:14 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3013 ms
2025-07-23 20:11:18 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.5038 ms
2025-07-23 20:11:20 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:11:20 [Information] HTTP POST /Account/Login responded 200 in 14.9173 ms
2025-07-23 20:11:20 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5573 ms
2025-07-23 20:11:20 [Information] HTTP GET /css/site.css responded 304 in 0.0875 ms
2025-07-23 20:11:20 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0353 ms
2025-07-23 20:11:20 [Information] HTTP GET /css/site.css.map responded 304 in 0.0925 ms
2025-07-23 20:12:14 [Information] HTTP POST /Account/Login responded 200 in 244.5552 ms
2025-07-23 20:12:20 [Information] HTTP GET /Account/Login responded 200 in 2.7047 ms
2025-07-23 20:12:20 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5292 ms
2025-07-23 20:12:20 [Information] HTTP GET /css/site.css responded 304 in 0.0728 ms
2025-07-23 20:12:20 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0442 ms
2025-07-23 20:12:20 [Information] HTTP GET /css/site.css.map responded 304 in 0.0886 ms
2025-07-23 20:12:24 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:12:24 [Information] HTTP POST /Account/Login responded 302 in 4.3450 ms
2025-07-23 20:12:24 [Information] HTTP GET / responded 302 in 1.2319 ms
2025-07-23 20:12:24 [Information] HTTP GET /Account/Login responded 200 in 2.4911 ms
2025-07-23 20:12:24 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4011 ms
2025-07-23 20:12:42 [Information] HTTP GET /Account/Login responded 200 in 2.4146 ms
2025-07-23 20:12:43 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4549 ms
2025-07-23 20:12:43 [Information] HTTP GET /css/site.css responded 304 in 0.0535 ms
2025-07-23 20:12:43 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0535 ms
2025-07-23 20:12:43 [Information] HTTP GET /css/site.css.map responded 304 in 0.0964 ms
2025-07-23 20:12:45 [Information] HTTP GET /Account/Login responded 200 in 3.0925 ms
2025-07-23 20:12:45 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5347 ms
2025-07-23 20:12:46 [Information] HTTP GET /Account/Login responded 200 in 2.3896 ms
2025-07-23 20:12:46 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3333 ms
2025-07-23 20:12:56 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:12:56 [Information] HTTP POST /Account/Login responded 302 in 3.4945 ms
2025-07-23 20:12:56 [Information] HTTP GET / responded 302 in 0.6620 ms
2025-07-23 20:12:56 [Information] HTTP GET /Account/Login responded 200 in 2.0964 ms
2025-07-23 20:12:56 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5012 ms
2025-07-23 20:12:56 [Information] HTTP GET /css/site.css responded 304 in 0.0500 ms
2025-07-23 20:12:56 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0414 ms
2025-07-23 20:12:56 [Information] HTTP GET /css/site.css.map responded 304 in 0.0669 ms
2025-07-23 20:13:34 [Information] HTTP GET /Account/Login responded 200 in 2.7807 ms
2025-07-23 20:13:34 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3101 ms
2025-07-23 20:13:34 [Information] HTTP GET /css/site.css responded 304 in 0.0434 ms
2025-07-23 20:13:34 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.0528 ms
2025-07-23 20:13:34 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0292 ms
2025-07-23 20:13:34 [Information] HTTP GET /css/site.css.map responded 304 in 0.0585 ms
2025-07-23 20:13:37 [Information] HTTP GET /Account/Login responded 200 in 2.1808 ms
2025-07-23 20:13:37 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4552 ms
2025-07-23 20:13:37 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.7328 ms
2025-07-23 20:13:37 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2955 ms
2025-07-23 20:13:37 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1497 ms
2025-07-23 20:13:37 [Information] HTTP GET /css/site.css responded 200 in 0.3170 ms
2025-07-23 20:13:37 [Information] HTTP GET /images/help.svg responded 200 in 0.2476 ms
2025-07-23 20:13:37 [Information] HTTP GET /images/logo.svg responded 200 in 0.3939 ms
2025-07-23 20:13:37 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.1709 ms
2025-07-23 20:13:37 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.3059 ms
2025-07-23 20:13:37 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.3408 ms
2025-07-23 20:13:37 [Information] HTTP GET /css/site.css.map responded 200 in 0.2298 ms
2025-07-23 20:13:37 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3078 ms
2025-07-23 20:13:37 [Information] HTTP GET /js/index.js responded 200 in 0.2781 ms
2025-07-23 20:13:37 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.6336 ms
2025-07-23 20:13:37 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3360 ms
2025-07-23 20:13:37 [Information] HTTP GET /js/components/input.js responded 200 in 0.1349 ms
2025-07-23 20:13:37 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1929 ms
2025-07-23 20:13:37 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.1760 ms
2025-07-23 20:13:37 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2195 ms
2025-07-23 20:13:37 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2678 ms
2025-07-23 20:13:37 [Information] HTTP GET /images/login-background.png responded 200 in 0.6941 ms
2025-07-23 20:13:37 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.5797 ms
2025-07-23 20:13:38 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2085 ms
2025-07-23 20:13:46 [Information] HTTP GET /images/eye.svg responded 200 in 0.4412 ms
2025-07-23 20:13:47 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:13:47 [Information] HTTP POST /Account/Login responded 302 in 3.3935 ms
2025-07-23 20:13:47 [Information] HTTP GET / responded 302 in 0.6526 ms
2025-07-23 20:13:47 [Information] HTTP GET /Account/Login responded 200 in 2.0429 ms
2025-07-23 20:13:47 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.2971 ms
2025-07-23 20:14:28 [Debug] Start authorize request protocol validation
2025-07-23 20:14:28 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:14:28 [Information] HTTP GET /Account/Login responded 200 in 141.8816 ms
2025-07-23 20:14:28 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3733 ms
2025-07-23 20:14:28 [Information] HTTP GET /css/site.css responded 304 in 0.0601 ms
2025-07-23 20:14:28 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0395 ms
2025-07-23 20:14:28 [Information] HTTP GET /css/site.css.map responded 304 in 0.0796 ms
2025-07-23 20:14:33 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 10.7379 ms
2025-07-23 20:14:33 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5259 ms
2025-07-23 20:14:33 [Information] HTTP GET /images/email-logo.png responded 200 in 0.3085 ms
2025-07-23 20:14:33 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2183 ms
2025-07-23 20:14:33 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.2289 ms
2025-07-23 20:14:35 [Debug] Start authorize request protocol validation
2025-07-23 20:14:35 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:14:35 [Information] HTTP GET /Account/Login responded 200 in 15.5808 ms
2025-07-23 20:14:35 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5867 ms
2025-07-23 20:14:39 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:14:39 [Debug] Start authorize request protocol validation
2025-07-23 20:14:39 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:14:39 [Information] HTTP POST /Account/Login responded 302 in 18.5718 ms
2025-07-23 20:14:39 [Information] HTTP GET / responded 302 in 0.6415 ms
2025-07-23 20:14:39 [Information] HTTP GET /Account/Login responded 200 in 2.6074 ms
2025-07-23 20:14:39 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3215 ms
2025-07-23 20:15:17 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 20:15:17 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 20:15:17 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 20:15:18 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 20:15:18 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 20:15:18 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 20:15:18 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 20:15:18 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 20:15:18 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 20:15:18 [Information] Seeding database...
2025-07-23 20:15:19 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 20:15:19 [Debug] Clients already populated
2025-07-23 20:15:19 [Debug] IdentityResources already populated
2025-07-23 20:15:19 [Debug] ApiScopes already populated
2025-07-23 20:15:19 [Debug] OIDC IdentityProviders already populated
2025-07-23 20:15:19 [Information] Done seeding database. Exiting.
2025-07-23 20:15:19 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 20:15:19 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 20:15:20 [Debug] Login Url: /Account/Login
2025-07-23 20:15:20 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 20:15:20 [Debug] Logout Url: /Account/Logout
2025-07-23 20:15:20 [Debug] ConsentUrl Url: /consent
2025-07-23 20:15:20 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 20:15:20 [Debug] Error Url: /home/<USER>
2025-07-23 20:15:20 [Debug] Error Id Parameter: errorId
2025-07-23 20:15:20 [Information] HTTP GET / responded 302 in 61.7457 ms
2025-07-23 20:15:20 [Information] HTTP GET /Account/Login responded 200 in 166.6707 ms
2025-07-23 20:15:20 [Information] HTTP GET /css/site.css responded 304 in 2.3491 ms
2025-07-23 20:15:20 [Information] HTTP GET /js/pages/login.js responded 304 in 0.2168 ms
2025-07-23 20:15:25 [Debug] Start authorize request protocol validation
2025-07-23 20:15:25 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:15:25 [Information] HTTP GET /Account/Login responded 200 in 187.3936 ms
2025-07-23 20:15:35 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:15:35 [Debug] Start authorize request protocol validation
2025-07-23 20:15:35 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:15:35 [Information] HTTP POST /Account/Login responded 302 in 60.8942 ms
2025-07-23 20:15:35 [Information] HTTP GET / responded 302 in 0.9227 ms
2025-07-23 20:15:35 [Information] HTTP GET /Account/Login responded 200 in 3.5620 ms
2025-07-23 20:16:23 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 20:16:23 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 20:16:23 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 20:16:23 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 20:16:23 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 20:16:23 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 20:16:23 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 20:16:23 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 20:16:23 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 20:16:23 [Information] Seeding database...
2025-07-23 20:16:24 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 20:16:25 [Debug] Clients already populated
2025-07-23 20:16:25 [Debug] IdentityResources already populated
2025-07-23 20:16:25 [Debug] ApiScopes already populated
2025-07-23 20:16:25 [Debug] OIDC IdentityProviders already populated
2025-07-23 20:16:25 [Information] Done seeding database. Exiting.
2025-07-23 20:16:25 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 20:16:25 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 20:16:25 [Debug] Login Url: /Account/Login
2025-07-23 20:16:25 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 20:16:25 [Debug] Logout Url: /Account/Logout
2025-07-23 20:16:25 [Debug] ConsentUrl Url: /consent
2025-07-23 20:16:25 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 20:16:25 [Debug] Error Url: /home/<USER>
2025-07-23 20:16:25 [Debug] Error Id Parameter: errorId
2025-07-23 20:16:25 [Information] HTTP GET / responded 302 in 66.3487 ms
2025-07-23 20:16:26 [Information] HTTP GET /Account/Login responded 200 in 151.8960 ms
2025-07-23 20:16:26 [Information] HTTP GET /css/site.css responded 200 in 7.8699 ms
2025-07-23 20:16:26 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.2243 ms
2025-07-23 20:16:26 [Information] HTTP GET /js/pages/login.js responded 200 in 0.6342 ms
2025-07-23 20:16:44 [Debug] Start authorize request protocol validation
2025-07-23 20:16:45 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:16:45 [Information] HTTP GET /Account/Login responded 200 in 182.7371 ms
2025-07-23 20:16:45 [Information] HTTP GET /css/site.css responded 304 in 0.3439 ms
2025-07-23 20:16:45 [Information] HTTP GET /js/pages/login.js responded 304 in 0.1106 ms
2025-07-23 20:16:54 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:16:54 [Debug] Start authorize request protocol validation
2025-07-23 20:16:54 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:16:54 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 20:16:55 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 20:16:55 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-23 20:16:55 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXlBREFBTVFBekFEVUFOQUFBQUFBQTExOzExMTI0MzQ1MDAxMDsn\"","No":"CT201354","Type":"Company","Organizational_Level_Code":"","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Aimovig  - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"ADDENBROOKES HOSPITAL","Address_2":"HILLS ROAD","City":"","County":"","Post_Code":"CB2 0QQ","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL AIMOVIG  - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2022-01-11","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-08-14","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 20:16:55 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 20:16:55 [Debug] Configured "Basic" authentication
2025-07-23 20:16:55 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:16:55 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.6710184"ms
2025-07-23 20:16:55 [Debug] Augmenting SignInContext
2025-07-23 20:16:56 [Debug] Adding idp claim with value: local
2025-07-23 20:16:56 [Debug] Adding amr claim with value: pwd
2025-07-23 20:16:56 [Debug] Adding auth_time claim with value: **********
2025-07-23 20:16:56 [Information] HTTP POST /Account/Login responded 302 in 1342.7078 ms
2025-07-23 20:16:56 [Debug] Request path /connect/authorize/callback matched to endpoint type Authorize
2025-07-23 20:16:56 [Debug] Endpoint enabled: Authorize, successfully created handler: Duende.IdentityServer.Endpoints.AuthorizeCallbackEndpoint
2025-07-23 20:16:56 [Information] Invoking IdentityServer endpoint: Duende.IdentityServer.Endpoints.AuthorizeCallbackEndpoint for /connect/authorize/callback
2025-07-23 20:16:56 [Debug] Start authorize callback request
2025-07-23 20:16:56 [Debug] User in authorize request: 82fbbc38-bc49-4f86-9575-f7e14dfac56f
2025-07-23 20:16:56 [Debug] Start authorize request protocol validation
2025-07-23 20:16:56 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:16:56 [Error] Request validation failed
2025-07-23 20:16:56 [Information] {"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:16:56 [Information] {"ClientId":"web-client-app","ClientName":null,"RedirectUri":null,"Endpoint":"Authorize","SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","Scopes":"","GrantType":null,"Error":"unauthorized_client","ErrorDescription":"Unknown client or client not enabled","Category":"Token","Name":"Token Issued Failure","EventType":"Failure","Id":2001,"Message":null,"ActivityId":"0HNEA1IJQQ9KR:00000013","TimeStamp":"2025-07-23T17:16:56.0478492","ProcessId":38792,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"TokenIssuedFailureEvent"}
2025-07-23 20:16:56 [Information] HTTP GET /connect/authorize/callback responded 302 in 64.5905 ms
2025-07-23 20:16:56 [Information] HTTP GET /home/<USER>
2025-07-23 20:16:56 [Information] HTTP GET /css/site.css responded 304 in 0.1144 ms
2025-07-23 20:17:29 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 20:17:29 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 20:17:29 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 20:17:29 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 20:17:29 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 20:17:29 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 20:17:29 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 20:17:29 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 20:17:29 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 20:17:29 [Information] Seeding database...
2025-07-23 20:17:30 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 20:17:30 [Debug] Clients already populated
2025-07-23 20:17:30 [Debug] IdentityResources already populated
2025-07-23 20:17:30 [Debug] ApiScopes already populated
2025-07-23 20:17:30 [Debug] OIDC IdentityProviders already populated
2025-07-23 20:17:30 [Information] Done seeding database. Exiting.
2025-07-23 20:17:30 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 20:17:31 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 20:17:31 [Debug] Login Url: /Account/Login
2025-07-23 20:17:31 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 20:17:31 [Debug] Logout Url: /Account/Logout
2025-07-23 20:17:31 [Debug] ConsentUrl Url: /consent
2025-07-23 20:17:31 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 20:17:31 [Debug] Error Url: /home/<USER>
2025-07-23 20:17:31 [Debug] Error Id Parameter: errorId
2025-07-23 20:17:31 [Information] HTTP GET / responded 302 in 66.9844 ms
2025-07-23 20:17:31 [Information] HTTP GET /Account/Login responded 200 in 155.2867 ms
2025-07-23 20:17:31 [Information] HTTP GET /css/site.css responded 200 in 6.7032 ms
2025-07-23 20:17:31 [Information] HTTP GET /js/pages/login.js responded 200 in 1.3314 ms
2025-07-23 20:17:39 [Debug] Start authorize request protocol validation
2025-07-23 20:17:39 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:17:39 [Information] HTTP GET /Account/Login responded 200 in 200.4520 ms
2025-07-23 20:17:39 [Information] HTTP GET /css/site.css responded 304 in 0.1547 ms
2025-07-23 20:17:39 [Information] HTTP GET /js/pages/login.js responded 304 in 0.1685 ms
2025-07-23 20:17:41 [Information] HTTP GET /css/site.css responded 304 in 0.1133 ms
2025-07-23 20:17:41 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 1.1827 ms
2025-07-23 20:17:41 [Information] HTTP GET /css/site.css.map responded 200 in 0.4904 ms
2025-07-23 20:17:43 [Debug] Start authorize request protocol validation
2025-07-23 20:17:43 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:17:43 [Information] HTTP GET /Account/Login responded 200 in 23.6831 ms
2025-07-23 20:17:43 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.9563 ms
2025-07-23 20:17:43 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.6544 ms
2025-07-23 20:17:43 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.3760 ms
2025-07-23 20:17:43 [Information] HTTP GET /css/site.css responded 200 in 0.5411 ms
2025-07-23 20:17:43 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.3816 ms
2025-07-23 20:17:43 [Information] HTTP GET /images/help.svg responded 200 in 0.3943 ms
2025-07-23 20:17:43 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.3867 ms
2025-07-23 20:17:43 [Information] HTTP GET /images/logo.svg responded 200 in 0.6298 ms
2025-07-23 20:17:43 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.3911 ms
2025-07-23 20:17:43 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.5921 ms
2025-07-23 20:17:43 [Information] HTTP GET /css/site.css.map responded 200 in 0.3509 ms
2025-07-23 20:17:43 [Information] HTTP GET /js/index.js responded 200 in 0.2260 ms
2025-07-23 20:17:43 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.6515 ms
2025-07-23 20:17:43 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 3.3155 ms
2025-07-23 20:17:43 [Information] HTTP GET /js/components/input.js responded 200 in 0.3151 ms
2025-07-23 20:17:43 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.4021 ms
2025-07-23 20:17:43 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2751 ms
2025-07-23 20:17:43 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.4003 ms
2025-07-23 20:17:43 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.3328 ms
2025-07-23 20:17:43 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3076 ms
2025-07-23 20:17:44 [Information] HTTP GET /images/login-background.png responded 200 in 0.8002 ms
2025-07-23 20:17:44 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.6311 ms
2025-07-23 20:17:44 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2902 ms
2025-07-23 20:17:55 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:17:55 [Debug] Start authorize request protocol validation
2025-07-23 20:17:55 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:17:55 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 20:17:56 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 20:17:56 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-23 20:17:56 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXlBREFBTVFBekFEVUFOQUFBQUFBQTExOzExMTI0MzQ1MDAxMDsn\"","No":"CT201354","Type":"Company","Organizational_Level_Code":"","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Aimovig  - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"ADDENBROOKES HOSPITAL","Address_2":"HILLS ROAD","City":"","County":"","Post_Code":"CB2 0QQ","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL AIMOVIG  - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2022-01-11","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-08-14","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 20:17:56 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 20:17:56 [Debug] Configured "Basic" authentication
2025-07-23 20:17:56 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:17:56 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.6349014"ms
2025-07-23 20:17:57 [Debug] Augmenting SignInContext
2025-07-23 20:17:57 [Debug] Adding idp claim with value: local
2025-07-23 20:17:57 [Debug] Adding amr claim with value: pwd
2025-07-23 20:17:57 [Debug] Adding auth_time claim with value: **********
2025-07-23 20:17:57 [Information] HTTP POST /Account/Login responded 302 in 1234.8922 ms
2025-07-23 20:17:57 [Debug] Request path /connect/authorize/callback matched to endpoint type Authorize
2025-07-23 20:17:57 [Debug] Endpoint enabled: Authorize, successfully created handler: Duende.IdentityServer.Endpoints.AuthorizeCallbackEndpoint
2025-07-23 20:17:57 [Information] Invoking IdentityServer endpoint: Duende.IdentityServer.Endpoints.AuthorizeCallbackEndpoint for /connect/authorize/callback
2025-07-23 20:17:57 [Debug] Start authorize callback request
2025-07-23 20:17:57 [Debug] User in authorize request: 82fbbc38-bc49-4f86-9575-f7e14dfac56f
2025-07-23 20:17:57 [Debug] Start authorize request protocol validation
2025-07-23 20:17:57 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:17:57 [Error] Request validation failed
2025-07-23 20:17:57 [Information] {"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:17:57 [Information] {"ClientId":"web-client-app","ClientName":null,"RedirectUri":null,"Endpoint":"Authorize","SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","Scopes":"","GrantType":null,"Error":"unauthorized_client","ErrorDescription":"Unknown client or client not enabled","Category":"Token","Name":"Token Issued Failure","EventType":"Failure","Id":2001,"Message":null,"ActivityId":"0HNEA1J7B86NU:00000047","TimeStamp":"2025-07-23T17:17:57.1636913","ProcessId":3416,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"TokenIssuedFailureEvent"}
2025-07-23 20:17:57 [Information] HTTP GET /connect/authorize/callback responded 302 in 58.3800 ms
2025-07-23 20:17:57 [Information] HTTP GET /home/<USER>
2025-07-23 20:17:57 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.7398 ms
2025-07-23 20:17:57 [Information] HTTP GET /css/site.css responded 304 in 0.1691 ms
2025-07-23 20:17:57 [Information] HTTP GET /css/site.css.map responded 304 in 0.0954 ms
2025-07-23 20:18:01 [Debug] Start authorize request protocol validation
2025-07-23 20:18:01 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:18:01 [Information] HTTP GET /Account/Login responded 200 in 14.1666 ms
2025-07-23 20:18:01 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6618 ms
2025-07-23 20:18:01 [Information] HTTP GET /css/site.css.map responded 304 in 0.1217 ms
2025-07-23 20:18:08 [Debug] Start authorize request protocol validation
2025-07-23 20:18:08 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:18:08 [Information] HTTP GET /Account/Login responded 200 in 17.6670 ms
2025-07-23 20:18:08 [Information] HTTP GET /css/site.css responded 304 in 0.1362 ms
2025-07-23 20:18:08 [Information] HTTP GET /js/pages/login.js responded 200 in 0.4223 ms
2025-07-23 20:18:11 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5970 ms
2025-07-23 20:18:11 [Information] HTTP GET /css/site.css.map responded 304 in 0.1273 ms
2025-07-23 20:18:12 [Debug] Start authorize request protocol validation
2025-07-23 20:18:12 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:18:12 [Information] HTTP GET /Account/Login responded 200 in 13.0491 ms
2025-07-23 20:18:12 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6832 ms
2025-07-23 20:18:12 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.6568 ms
2025-07-23 20:18:12 [Information] HTTP GET /css/site.css responded 200 in 0.4524 ms
2025-07-23 20:18:12 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.5140 ms
2025-07-23 20:18:12 [Information] HTTP GET /images/help.svg responded 200 in 0.3852 ms
2025-07-23 20:18:12 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1900 ms
2025-07-23 20:18:12 [Information] HTTP GET /images/logo.svg responded 200 in 0.3691 ms
2025-07-23 20:18:12 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2380 ms
2025-07-23 20:18:12 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2784 ms
2025-07-23 20:18:12 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4074 ms
2025-07-23 20:18:12 [Information] HTTP GET /css/site.css.map responded 200 in 0.3038 ms
2025-07-23 20:18:12 [Information] HTTP GET /js/index.js responded 200 in 0.1496 ms
2025-07-23 20:18:12 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4833 ms
2025-07-23 20:18:12 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.7055 ms
2025-07-23 20:18:12 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3308 ms
2025-07-23 20:18:12 [Information] HTTP GET /js/components/input.js responded 200 in 0.2482 ms
2025-07-23 20:18:12 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2647 ms
2025-07-23 20:18:12 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2378 ms
2025-07-23 20:18:12 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2443 ms
2025-07-23 20:18:12 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2786 ms
2025-07-23 20:18:12 [Information] HTTP GET /images/login-background.png responded 200 in 0.9731 ms
2025-07-23 20:18:12 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.8370 ms
2025-07-23 20:18:12 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2899 ms
2025-07-23 20:18:23 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:18:23 [Debug] Start authorize request protocol validation
2025-07-23 20:18:23 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:18:23 [Information] HTTP POST /Account/Login responded 302 in 15.4664 ms
2025-07-23 20:18:23 [Information] HTTP GET / responded 302 in 0.8499 ms
2025-07-23 20:18:23 [Information] HTTP GET /Account/Login responded 200 in 2.1543 ms
2025-07-23 20:18:23 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5712 ms
2025-07-23 20:18:23 [Information] HTTP GET /css/site.css responded 304 in 0.0740 ms
2025-07-23 20:18:23 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0648 ms
2025-07-23 20:18:23 [Information] HTTP GET /css/site.css.map responded 304 in 0.0772 ms
2025-07-23 20:19:51 [Information] HTTP GET /Account/Login responded 200 in 3.0974 ms
2025-07-23 20:19:51 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5564 ms
2025-07-23 20:19:51 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.4146 ms
2025-07-23 20:19:51 [Information] HTTP GET /css/site.css responded 200 in 0.3804 ms
2025-07-23 20:19:51 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4621 ms
2025-07-23 20:19:51 [Information] HTTP GET /images/help.svg responded 200 in 0.2989 ms
2025-07-23 20:19:51 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1878 ms
2025-07-23 20:19:51 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.3257 ms
2025-07-23 20:19:51 [Information] HTTP GET /images/logo.svg responded 200 in 0.5535 ms
2025-07-23 20:19:51 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2959 ms
2025-07-23 20:19:51 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4233 ms
2025-07-23 20:19:51 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4358 ms
2025-07-23 20:19:51 [Information] HTTP GET /js/index.js responded 200 in 0.2754 ms
2025-07-23 20:19:51 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2322 ms
2025-07-23 20:19:51 [Information] HTTP GET /js/components/input.js responded 200 in 0.2404 ms
2025-07-23 20:19:51 [Information] HTTP GET /css/site.css.map responded 200 in 0.1878 ms
2025-07-23 20:19:51 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.5224 ms
2025-07-23 20:19:51 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2089 ms
2025-07-23 20:19:51 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.4562 ms
2025-07-23 20:19:51 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2245 ms
2025-07-23 20:19:51 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2421 ms
2025-07-23 20:19:51 [Information] HTTP GET /images/login-background.png responded 200 in 0.7060 ms
2025-07-23 20:19:51 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.7853 ms
2025-07-23 20:19:51 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2356 ms
2025-07-23 20:19:55 [Debug] Start authorize request protocol validation
2025-07-23 20:19:55 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:19:55 [Information] HTTP GET /Account/Login responded 200 in 17.4006 ms
2025-07-23 20:19:55 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0647 ms
2025-07-23 20:19:56 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4909 ms
2025-07-23 20:20:04 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:20:04 [Debug] Start authorize request protocol validation
2025-07-23 20:20:04 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:20:04 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 20:20:04 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 20:20:04 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-23 20:20:04 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXlBREFBTVFBekFEVUFOQUFBQUFBQTExOzExMTI0MzQ1MDAxMDsn\"","No":"CT201354","Type":"Company","Organizational_Level_Code":"","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Aimovig  - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"ADDENBROOKES HOSPITAL","Address_2":"HILLS ROAD","City":"","County":"","Post_Code":"CB2 0QQ","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL AIMOVIG  - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2022-01-11","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-08-14","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 20:20:04 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 20:20:04 [Debug] Configured "Basic" authentication
2025-07-23 20:20:04 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:20:04 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.3373417"ms
2025-07-23 20:20:04 [Debug] Augmenting SignInContext
2025-07-23 20:20:04 [Debug] Adding idp claim with value: local
2025-07-23 20:20:04 [Debug] Adding amr claim with value: pwd
2025-07-23 20:20:04 [Debug] Adding auth_time claim with value: **********
2025-07-23 20:20:04 [Information] HTTP POST /Account/Login responded 302 in 618.0411 ms
2025-07-23 20:20:04 [Debug] Request path /connect/authorize/callback matched to endpoint type Authorize
2025-07-23 20:20:04 [Debug] Endpoint enabled: Authorize, successfully created handler: Duende.IdentityServer.Endpoints.AuthorizeCallbackEndpoint
2025-07-23 20:20:04 [Information] Invoking IdentityServer endpoint: Duende.IdentityServer.Endpoints.AuthorizeCallbackEndpoint for /connect/authorize/callback
2025-07-23 20:20:04 [Debug] Start authorize callback request
2025-07-23 20:20:04 [Debug] User in authorize request: 82fbbc38-bc49-4f86-9575-f7e14dfac56f
2025-07-23 20:20:04 [Debug] Start authorize request protocol validation
2025-07-23 20:20:04 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:20:04 [Error] Request validation failed
2025-07-23 20:20:04 [Information] {"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:20:04 [Information] {"ClientId":"web-client-app","ClientName":null,"RedirectUri":null,"Endpoint":"Authorize","SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","Scopes":"","GrantType":null,"Error":"unauthorized_client","ErrorDescription":"Unknown client or client not enabled","Category":"Token","Name":"Token Issued Failure","EventType":"Failure","Id":2001,"Message":null,"ActivityId":"0HNEA1J7B86NU:000000D7","TimeStamp":"2025-07-23T17:20:04.6558448","ProcessId":3416,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"TokenIssuedFailureEvent"}
2025-07-23 20:20:04 [Information] HTTP GET /connect/authorize/callback responded 302 in 35.0410 ms
2025-07-23 20:20:04 [Information] HTTP GET /home/<USER>
2025-07-23 20:20:04 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5378 ms
2025-07-23 20:20:28 [Debug] Start authorize request protocol validation
2025-07-23 20:20:28 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:20:28 [Information] HTTP GET /Account/Login responded 200 in 17.3460 ms
2025-07-23 20:20:28 [Information] HTTP GET /css/site.css responded 304 in 0.0572 ms
2025-07-23 20:20:28 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2557 ms
2025-07-23 20:20:40 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:20:40 [Debug] Start authorize request protocol validation
2025-07-23 20:20:40 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:20:40 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 20:20:40 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 20:20:40 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-23 20:20:40 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXlBREFBTVFBekFEVUFOQUFBQUFBQTExOzExMTI0MzQ1MDAxMDsn\"","No":"CT201354","Type":"Company","Organizational_Level_Code":"","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Aimovig  - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"ADDENBROOKES HOSPITAL","Address_2":"HILLS ROAD","City":"","County":"","Post_Code":"CB2 0QQ","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL AIMOVIG  - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2022-01-11","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-08-14","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 20:20:40 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 20:20:40 [Debug] Configured "Basic" authentication
2025-07-23 20:20:40 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:20:41 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.3350994"ms
2025-07-23 20:20:41 [Information] {"Username":"CT411980","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNEA1J7B86NU:000000E3","TimeStamp":"2025-07-23T17:20:41.2825948","ProcessId":3416,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-23 20:20:41 [Debug] Start authorize request protocol validation
2025-07-23 20:20:41 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:20:41 [Information] HTTP POST /Account/Login responded 200 in 510.6654 ms
2025-07-23 20:20:41 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0727 ms
2025-07-23 20:20:41 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.3222 ms
2025-07-23 20:20:43 [Information] HTTP POST /Account/Login responded 200 in 128.7989 ms
2025-07-23 20:20:45 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:20:45 [Debug] Start authorize request protocol validation
2025-07-23 20:20:45 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:20:45 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 20:20:45 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 20:20:45 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-23 20:20:45 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXlBREFBTVFBekFEVUFOQUFBQUFBQTExOzExMTI0MzQ1MDAxMDsn\"","No":"CT201354","Type":"Company","Organizational_Level_Code":"","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Aimovig  - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"ADDENBROOKES HOSPITAL","Address_2":"HILLS ROAD","City":"","County":"","Post_Code":"CB2 0QQ","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL AIMOVIG  - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2022-01-11","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-08-14","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 20:20:45 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 20:20:45 [Debug] Configured "Basic" authentication
2025-07-23 20:20:45 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:20:45 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.3607957"ms
2025-07-23 20:20:45 [Information] {"Username":"CT411980","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNEA1J7B86NU:000000EB","TimeStamp":"2025-07-23T17:20:45.9336417","ProcessId":3416,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-23 20:20:45 [Debug] Start authorize request protocol validation
2025-07-23 20:20:45 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 20:20:45 [Information] HTTP POST /Account/Login responded 200 in 528.7005 ms
2025-07-23 20:20:45 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0532 ms
2025-07-23 20:20:47 [Information] HTTP POST /Account/Login responded 200 in 101.2398 ms
2025-07-23 20:27:05 [Information] HTTP GET /Account/ForgotPassword responded 200 in 19.1644 ms
2025-07-23 20:27:05 [Information] HTTP GET /css/site.css responded 304 in 0.0720 ms
2025-07-23 20:27:05 [Information] HTTP GET /images/password-logo.png responded 200 in 0.3488 ms
2025-07-23 20:27:05 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1876 ms
2025-07-23 20:27:05 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.2171 ms
2025-07-23 20:27:05 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.0570 ms
2025-07-23 20:27:23 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:27:23 [Information] HTTP POST /Account/ForgotPassword responded 200 in 17.2726 ms
2025-07-23 20:28:05 [Information] HTTP GET /css/site.css responded 304 in 0.1059 ms
2025-07-23 20:28:05 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4700 ms
2025-07-23 20:28:05 [Information] HTTP GET /css/site.css.map responded 304 in 0.0644 ms
2025-07-23 20:28:07 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:28:07 [Information] HTTP POST /Account/ForgotPassword responded 200 in 4.7798 ms
2025-07-23 20:28:07 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5633 ms
2025-07-23 20:28:07 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.9674 ms
2025-07-23 20:28:07 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2536 ms
2025-07-23 20:28:07 [Information] HTTP GET /css/site.css responded 200 in 0.4031 ms
2025-07-23 20:28:07 [Information] HTTP GET /images/help.svg responded 200 in 0.1879 ms
2025-07-23 20:28:07 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2228 ms
2025-07-23 20:28:07 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1829 ms
2025-07-23 20:28:07 [Information] HTTP GET /images/password-logo.png responded 200 in 0.2290 ms
2025-07-23 20:28:07 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4058 ms
2025-07-23 20:28:07 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3968 ms
2025-07-23 20:28:07 [Information] HTTP GET /js/index.js responded 200 in 0.1968 ms
2025-07-23 20:28:07 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1552 ms
2025-07-23 20:28:07 [Information] HTTP GET /css/site.css.map responded 200 in 0.2150 ms
2025-07-23 20:28:07 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.1638 ms
2025-07-23 20:28:07 [Information] HTTP GET /js/components/input.js responded 200 in 0.2945 ms
2025-07-23 20:28:07 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1769 ms
2025-07-23 20:28:07 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2420 ms
2025-07-23 20:28:07 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1738 ms
2025-07-23 20:28:07 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.2038 ms
2025-07-23 20:28:07 [Information] HTTP GET /images/login-background.png responded 200 in 1.6627 ms
2025-07-23 20:28:07 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.9542 ms
2025-07-23 20:28:07 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2194 ms
2025-07-23 20:28:12 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.2294 ms
2025-07-23 20:33:17 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 20:33:17 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 20:33:17 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 20:33:17 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 20:33:17 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 20:33:17 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 20:33:17 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 20:33:17 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 20:33:17 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 20:33:17 [Information] Seeding database...
2025-07-23 20:33:18 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 20:33:19 [Debug] Clients already populated
2025-07-23 20:33:19 [Debug] IdentityResources already populated
2025-07-23 20:33:19 [Debug] ApiScopes already populated
2025-07-23 20:33:19 [Debug] OIDC IdentityProviders already populated
2025-07-23 20:33:19 [Information] Done seeding database. Exiting.
2025-07-23 20:33:19 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 20:33:19 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 20:33:19 [Debug] Login Url: /Account/Login
2025-07-23 20:33:19 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 20:33:19 [Debug] Logout Url: /Account/Logout
2025-07-23 20:33:19 [Debug] ConsentUrl Url: /consent
2025-07-23 20:33:19 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 20:33:19 [Debug] Error Url: /home/<USER>
2025-07-23 20:33:19 [Debug] Error Id Parameter: errorId
2025-07-23 20:33:19 [Information] HTTP GET / responded 302 in 71.2268 ms
2025-07-23 20:33:19 [Information] HTTP GET /Account/Login responded 200 in 151.7840 ms
2025-07-23 20:33:20 [Information] HTTP GET /css/site.css responded 304 in 2.5434 ms
2025-07-23 20:33:20 [Information] HTTP GET /images/info-circle.svg responded 200 in 2.4442 ms
2025-07-23 20:33:20 [Information] HTTP GET /images/logo.svg responded 200 in 5.9078 ms
2025-07-23 20:33:20 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.3673 ms
2025-07-23 20:33:20 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3220 ms
2025-07-23 20:33:20 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.5992 ms
2025-07-23 20:33:20 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.1367 ms
2025-07-23 20:33:26 [Information] HTTP GET /Account/Login responded 200 in 56.9177 ms
2025-07-23 20:33:27 [Information] HTTP GET /Account/ForgotPassword responded 200 in 23.7096 ms
2025-07-23 20:33:27 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.1781 ms
2025-07-23 20:33:27 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.4464 ms
2025-07-23 20:33:33 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:33:33 [Information] HTTP POST /Account/ForgotPassword responded 200 in 20.6790 ms
2025-07-23 20:33:33 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.1880 ms
2025-07-23 20:33:42 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 1.1096 ms
2025-07-23 20:33:42 [Information] HTTP GET /css/site.css.map responded 304 in 0.1018 ms
2025-07-23 20:33:43 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:33:43 [Information] HTTP POST /Account/ForgotPassword responded 200 in 7.3580 ms
2025-07-23 20:33:43 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 1.4231 ms
2025-07-23 20:33:43 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.3125 ms
2025-07-23 20:33:43 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4060 ms
2025-07-23 20:33:43 [Information] HTTP GET /css/site.css responded 200 in 0.7625 ms
2025-07-23 20:33:43 [Information] HTTP GET /images/help.svg responded 200 in 0.2856 ms
2025-07-23 20:33:43 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.3729 ms
2025-07-23 20:33:43 [Information] HTTP GET /images/password-logo.png responded 200 in 0.3070 ms
2025-07-23 20:33:43 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.3447 ms
2025-07-23 20:33:43 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.7866 ms
2025-07-23 20:33:43 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.8019 ms
2025-07-23 20:33:43 [Information] HTTP GET /js/index.js responded 200 in 0.4016 ms
2025-07-23 20:33:43 [Information] HTTP GET /css/site.css.map responded 200 in 0.3836 ms
2025-07-23 20:33:43 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2851 ms
2025-07-23 20:33:43 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 3.6224 ms
2025-07-23 20:33:44 [Information] HTTP GET /js/components/input.js responded 200 in 0.4339 ms
2025-07-23 20:33:44 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.3493 ms
2025-07-23 20:33:44 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2557 ms
2025-07-23 20:33:44 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3739 ms
2025-07-23 20:33:44 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.3374 ms
2025-07-23 20:33:44 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.2528 ms
2025-07-23 20:33:44 [Information] HTTP GET /images/login-background.png responded 200 in 1.3382 ms
2025-07-23 20:33:44 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.6233 ms
2025-07-23 20:33:44 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3903 ms
2025-07-23 20:34:01 [Information] HTTP GET /images/error-warning.svg responded 200 in 1.1885 ms
2025-07-23 20:36:14 [Information] HTTP GET /Account/ForgotPassword responded 200 in 2.6074 ms
2025-07-23 20:36:14 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5497 ms
2025-07-23 20:36:14 [Information] HTTP GET /css/site.css responded 304 in 0.0969 ms
2025-07-23 20:36:14 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.3294 ms
2025-07-23 20:36:14 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0928 ms
2025-07-23 20:36:14 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0872 ms
2025-07-23 20:36:14 [Information] HTTP GET /css/site.css.map responded 304 in 0.0996 ms
2025-07-23 20:36:27 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:36:27 [Information] HTTP POST /Account/ForgotPassword responded 200 in 3.4239 ms
2025-07-23 20:36:27 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5722 ms
2025-07-23 20:36:35 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.9835 ms
2025-07-23 20:36:35 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5432 ms
2025-07-23 20:36:47 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:36:47 [Information] HTTP POST /Account/ForgotPassword responded 200 in 3.2868 ms
2025-07-23 20:36:47 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5965 ms
2025-07-23 20:36:47 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.1134 ms
2025-07-23 20:36:47 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0763 ms
2025-07-23 20:40:35 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 20:40:35 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 20:40:35 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 20:40:35 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 20:40:35 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 20:40:35 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 20:40:35 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 20:40:35 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 20:40:35 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 20:40:35 [Information] Seeding database...
2025-07-23 20:40:36 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 20:40:36 [Debug] Clients already populated
2025-07-23 20:40:36 [Debug] IdentityResources already populated
2025-07-23 20:40:36 [Debug] ApiScopes already populated
2025-07-23 20:40:36 [Debug] OIDC IdentityProviders already populated
2025-07-23 20:40:36 [Information] Done seeding database. Exiting.
2025-07-23 20:40:37 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 20:40:37 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 20:40:37 [Debug] Login Url: /Account/Login
2025-07-23 20:40:37 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 20:40:37 [Debug] Logout Url: /Account/Logout
2025-07-23 20:40:37 [Debug] ConsentUrl Url: /consent
2025-07-23 20:40:37 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 20:40:37 [Debug] Error Url: /home/<USER>
2025-07-23 20:40:37 [Debug] Error Id Parameter: errorId
2025-07-23 20:40:37 [Information] HTTP GET / responded 302 in 66.5667 ms
2025-07-23 20:40:37 [Information] HTTP GET /Account/Login responded 200 in 148.2491 ms
2025-07-23 20:40:37 [Information] HTTP GET /css/site.css responded 304 in 2.8391 ms
2025-07-23 20:40:37 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.3653 ms
2025-07-23 20:40:37 [Information] HTTP GET /images/logo.svg responded 200 in 4.3302 ms
2025-07-23 20:40:37 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0961 ms
2025-07-23 20:40:37 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.1166 ms
2025-07-23 20:40:37 [Information] HTTP GET /js/pages/login.js responded 200 in 1.0816 ms
2025-07-23 20:40:41 [Information] HTTP GET /Account/Login responded 200 in 56.5686 ms
2025-07-23 20:40:43 [Information] HTTP GET /Account/ForgotPassword responded 200 in 24.1562 ms
2025-07-23 20:40:43 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.2901 ms
2025-07-23 20:40:46 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:40:46 [Information] HTTP POST /Account/ForgotPassword responded 200 in 21.0089 ms
2025-07-23 20:42:48 [Information] HTTP GET /Account/ForgotPassword responded 200 in 5.9671 ms
2025-07-23 20:42:48 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.1070 ms
2025-07-23 20:42:48 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0748 ms
2025-07-23 20:42:50 [Information] HTTP GET /Account/Login responded 200 in 4.9286 ms
2025-07-23 20:42:50 [Information] HTTP GET /js/pages/login.js responded 304 in 0.1321 ms
2025-07-23 20:43:49 [Information] HTTP GET /css/site.css responded 304 in 0.1129 ms
2025-07-23 20:43:49 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 1.0602 ms
2025-07-23 20:43:49 [Information] HTTP GET /css/site.css.map responded 304 in 0.1127 ms
2025-07-23 20:43:51 [Information] HTTP GET /Account/Login responded 200 in 2.9666 ms
2025-07-23 20:43:51 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.9010 ms
2025-07-23 20:43:51 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2968 ms
2025-07-23 20:43:51 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.0962 ms
2025-07-23 20:43:51 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2573 ms
2025-07-23 20:43:51 [Information] HTTP GET /images/help.svg responded 200 in 0.3611 ms
2025-07-23 20:43:51 [Information] HTTP GET /css/site.css responded 200 in 0.6129 ms
2025-07-23 20:43:51 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2933 ms
2025-07-23 20:43:51 [Information] HTTP GET /images/logo.svg responded 200 in 0.4310 ms
2025-07-23 20:43:51 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.3054 ms
2025-07-23 20:43:51 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.6084 ms
2025-07-23 20:43:51 [Information] HTTP GET /css/site.css.map responded 200 in 0.2930 ms
2025-07-23 20:43:51 [Information] HTTP GET /js/index.js responded 200 in 0.2742 ms
2025-07-23 20:43:51 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.8665 ms
2025-07-23 20:43:51 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 3.0694 ms
2025-07-23 20:43:51 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2694 ms
2025-07-23 20:43:51 [Information] HTTP GET /js/components/input.js responded 200 in 0.2949 ms
2025-07-23 20:43:51 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.3137 ms
2025-07-23 20:43:51 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2465 ms
2025-07-23 20:43:51 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2846 ms
2025-07-23 20:43:51 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2892 ms
2025-07-23 20:43:51 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3331 ms
2025-07-23 20:43:51 [Information] HTTP GET /images/login-background.png responded 200 in 0.7400 ms
2025-07-23 20:43:51 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.1198 ms
2025-07-23 20:43:51 [Information] HTTP GET /images/favicon.ico responded 200 in 0.4228 ms
2025-07-23 20:43:52 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.7777 ms
2025-07-23 20:43:52 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.8306 ms
2025-07-23 20:43:52 [Information] HTTP GET /images/password-logo.png responded 200 in 0.4252 ms
2025-07-23 20:43:52 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.3247 ms
2025-07-23 20:43:52 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.3633 ms
2025-07-23 20:43:56 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:43:56 [Information] HTTP POST /Account/ForgotPassword responded 200 in 6.9638 ms
2025-07-23 20:43:56 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.7933 ms
2025-07-23 20:43:56 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.1958 ms
2025-07-23 20:44:01 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:44:01 [Information] HTTP POST /Account/ForgotPassword responded 200 in 3.8998 ms
2025-07-23 20:44:01 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.9087 ms
2025-07-23 20:44:01 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.1489 ms
2025-07-23 20:44:01 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4495 ms
2025-07-23 20:44:01 [Information] HTTP GET /css/site.css responded 200 in 0.6605 ms
2025-07-23 20:44:01 [Information] HTTP GET /images/help.svg responded 200 in 0.2946 ms
2025-07-23 20:44:01 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2674 ms
2025-07-23 20:44:01 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2062 ms
2025-07-23 20:44:01 [Information] HTTP GET /images/password-logo.png responded 200 in 0.3684 ms
2025-07-23 20:44:01 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.5991 ms
2025-07-23 20:44:01 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.6281 ms
2025-07-23 20:44:01 [Information] HTTP GET /css/site.css.map responded 200 in 0.3201 ms
2025-07-23 20:44:01 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.7736 ms
2025-07-23 20:44:01 [Information] HTTP GET /js/index.js responded 200 in 0.3455 ms
2025-07-23 20:44:01 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2772 ms
2025-07-23 20:44:01 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2761 ms
2025-07-23 20:44:01 [Information] HTTP GET /js/components/input.js responded 200 in 0.2493 ms
2025-07-23 20:44:01 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3334 ms
2025-07-23 20:44:01 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1761 ms
2025-07-23 20:44:01 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2349 ms
2025-07-23 20:44:01 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.2728 ms
2025-07-23 20:44:01 [Information] HTTP GET /images/login-background.png responded 200 in 1.5388 ms
2025-07-23 20:44:01 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.9589 ms
2025-07-23 20:44:01 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3451 ms
2025-07-23 20:45:26 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:45:26 [Information] HTTP POST /Account/ForgotPassword responded 200 in 3.0001 ms
2025-07-23 20:45:26 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5099 ms
2025-07-23 20:45:26 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2224 ms
2025-07-23 20:45:26 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.0316 ms
2025-07-23 20:45:26 [Information] HTTP GET /css/site.css responded 200 in 0.4542 ms
2025-07-23 20:45:26 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2481 ms
2025-07-23 20:45:26 [Information] HTTP GET /images/help.svg responded 200 in 0.2479 ms
2025-07-23 20:45:26 [Information] HTTP GET /images/password-logo.png responded 200 in 0.2317 ms
2025-07-23 20:45:26 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1681 ms
2025-07-23 20:45:26 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4135 ms
2025-07-23 20:45:26 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4472 ms
2025-07-23 20:45:26 [Information] HTTP GET /js/index.js responded 200 in 0.2432 ms
2025-07-23 20:45:26 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1615 ms
2025-07-23 20:45:26 [Information] HTTP GET /css/site.css.map responded 200 in 0.2942 ms
2025-07-23 20:45:26 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.3280 ms
2025-07-23 20:45:26 [Information] HTTP GET /js/components/input.js responded 200 in 0.2597 ms
2025-07-23 20:45:26 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1830 ms
2025-07-23 20:45:26 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2839 ms
2025-07-23 20:45:26 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.3479 ms
2025-07-23 20:45:26 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.4545 ms
2025-07-23 20:45:26 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.2294 ms
2025-07-23 20:45:26 [Information] HTTP GET /images/login-background.png responded 200 in 0.9242 ms
2025-07-23 20:45:26 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.6668 ms
2025-07-23 20:45:26 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2449 ms
2025-07-23 20:45:27 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.3451 ms
2025-07-23 20:46:13 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:46:13 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 20:46:13 [Debug] Configured "Basic" authentication
2025-07-23 20:46:13 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:46:13 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.7089135"ms
2025-07-23 20:46:13 [Information] HTTP POST /Account/ForgotPassword responded 200 in 757.0974 ms
2025-07-23 20:46:13 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6439 ms
2025-07-23 20:46:13 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0604 ms
2025-07-23 20:49:33 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:49:33 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 20:49:33 [Debug] Configured "Basic" authentication
2025-07-23 20:49:33 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:49:34 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3698624"ms
2025-07-23 20:49:34 [Information] HTTP POST /Account/ForgotPassword responded 200 in 375.0597 ms
2025-07-23 20:49:34 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5556 ms
2025-07-23 20:49:34 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.7683 ms
2025-07-23 20:49:34 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.7803 ms
2025-07-23 20:49:34 [Information] HTTP GET /images/help.svg responded 200 in 0.2244 ms
2025-07-23 20:49:34 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2784 ms
2025-07-23 20:49:34 [Information] HTTP GET /css/site.css responded 200 in 0.9932 ms
2025-07-23 20:49:34 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2011 ms
2025-07-23 20:49:34 [Information] HTTP GET /images/password-logo.png responded 200 in 0.2440 ms
2025-07-23 20:49:34 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4967 ms
2025-07-23 20:49:34 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3556 ms
2025-07-23 20:49:34 [Information] HTTP GET /js/index.js responded 200 in 0.1719 ms
2025-07-23 20:49:34 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1989 ms
2025-07-23 20:49:34 [Information] HTTP GET /js/components/input.js responded 200 in 0.1487 ms
2025-07-23 20:49:34 [Information] HTTP GET /css/site.css.map responded 200 in 0.4491 ms
2025-07-23 20:49:34 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.9634 ms
2025-07-23 20:49:34 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1372 ms
2025-07-23 20:49:34 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.1692 ms
2025-07-23 20:49:34 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2257 ms
2025-07-23 20:49:34 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2437 ms
2025-07-23 20:49:34 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.1858 ms
2025-07-23 20:49:34 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.1480 ms
2025-07-23 20:49:34 [Information] HTTP GET /images/login-background.png responded 200 in 0.9343 ms
2025-07-23 20:49:34 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.6691 ms
2025-07-23 20:49:34 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2326 ms
2025-07-23 20:49:36 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:49:36 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 20:49:36 [Debug] Configured "Basic" authentication
2025-07-23 20:49:36 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:49:36 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3388739"ms
2025-07-23 20:49:36 [Information] HTTP POST /Account/ForgotPassword responded 200 in 342.9886 ms
2025-07-23 20:49:36 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5594 ms
2025-07-23 20:50:01 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:50:01 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 20:50:01 [Debug] Configured "Basic" authentication
2025-07-23 20:50:01 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:50:02 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3384316"ms
2025-07-23 20:50:02 [Information] HTTP POST /Account/ForgotPassword responded 200 in 342.5212 ms
2025-07-23 20:50:02 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5594 ms
2025-07-23 20:50:02 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.8838 ms
2025-07-23 20:50:02 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.1949 ms
2025-07-23 20:50:02 [Information] HTTP GET /images/help.svg responded 200 in 0.2898 ms
2025-07-23 20:50:02 [Information] HTTP GET /css/site.css responded 200 in 0.5129 ms
2025-07-23 20:50:02 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2632 ms
2025-07-23 20:50:02 [Information] HTTP GET /images/password-logo.png responded 200 in 0.2112 ms
2025-07-23 20:50:02 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2307 ms
2025-07-23 20:50:02 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4418 ms
2025-07-23 20:50:02 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.5823 ms
2025-07-23 20:50:02 [Information] HTTP GET /css/site.css.map responded 200 in 0.2206 ms
2025-07-23 20:50:02 [Information] HTTP GET /js/index.js responded 200 in 0.1941 ms
2025-07-23 20:50:02 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.3984 ms
2025-07-23 20:50:02 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1869 ms
2025-07-23 20:50:02 [Information] HTTP GET /js/components/input.js responded 200 in 0.1716 ms
2025-07-23 20:50:02 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1932 ms
2025-07-23 20:50:02 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2208 ms
2025-07-23 20:50:02 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1641 ms
2025-07-23 20:50:02 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.4183 ms
2025-07-23 20:50:02 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.2483 ms
2025-07-23 20:50:02 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.2030 ms
2025-07-23 20:50:02 [Information] HTTP GET /images/login-background.png responded 200 in 0.5158 ms
2025-07-23 20:50:02 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.0107 ms
2025-07-23 20:50:02 [Information] HTTP GET /images/favicon.ico responded 200 in 0.6291 ms
2025-07-23 20:50:02 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:50:02 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 20:50:02 [Debug] Configured "Basic" authentication
2025-07-23 20:50:02 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:50:03 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3255277"ms
2025-07-23 20:50:03 [Information] HTTP POST /Account/ForgotPassword responded 200 in 329.7137 ms
2025-07-23 20:50:03 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4835 ms
2025-07-23 20:50:03 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0552 ms
2025-07-23 20:50:04 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:50:04 [Information] HTTP POST /Account/ForgotPassword responded 400 in 32.1342 ms
2025-07-23 20:50:04 [Debug] SignOutCalled set; processing post-signout session cleanup.
2025-07-23 20:50:16 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 20:50:16 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 20:50:16 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 20:50:17 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 20:50:17 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 20:50:17 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 20:50:17 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 20:50:17 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 20:50:17 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 20:50:17 [Information] Seeding database...
2025-07-23 20:50:18 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 20:50:18 [Debug] Clients already populated
2025-07-23 20:50:18 [Debug] IdentityResources already populated
2025-07-23 20:50:18 [Debug] ApiScopes already populated
2025-07-23 20:50:18 [Debug] OIDC IdentityProviders already populated
2025-07-23 20:50:18 [Information] Done seeding database. Exiting.
2025-07-23 20:50:18 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 20:50:18 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 20:50:19 [Debug] Login Url: /Account/Login
2025-07-23 20:50:19 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 20:50:19 [Debug] Logout Url: /Account/Logout
2025-07-23 20:50:19 [Debug] ConsentUrl Url: /consent
2025-07-23 20:50:19 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 20:50:19 [Debug] Error Url: /home/<USER>
2025-07-23 20:50:19 [Debug] Error Id Parameter: errorId
2025-07-23 20:50:19 [Information] HTTP GET / responded 302 in 59.8860 ms
2025-07-23 20:50:19 [Information] HTTP GET /Account/Login responded 200 in 173.0361 ms
2025-07-23 20:50:19 [Information] HTTP GET /images/help.svg responded 200 in 7.1096 ms
2025-07-23 20:50:19 [Information] HTTP GET /css/site.css responded 200 in 7.8394 ms
2025-07-23 20:50:19 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 8.4557 ms
2025-07-23 20:50:19 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 7.7713 ms
2025-07-23 20:50:19 [Information] HTTP GET /images/help-blue.svg responded 200 in 7.0911 ms
2025-07-23 20:50:19 [Information] HTTP GET /images/logo.svg responded 200 in 0.8165 ms
2025-07-23 20:50:19 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.5388 ms
2025-07-23 20:50:19 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.5847 ms
2025-07-23 20:50:19 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.7519 ms
2025-07-23 20:50:19 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 1.1363 ms
2025-07-23 20:50:19 [Information] HTTP GET /js/index.js responded 200 in 0.4727 ms
2025-07-23 20:50:19 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.4671 ms
2025-07-23 20:50:19 [Information] HTTP GET /js/components/input.js responded 200 in 0.3081 ms
2025-07-23 20:50:19 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.3057 ms
2025-07-23 20:50:19 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.4935 ms
2025-07-23 20:50:19 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.3366 ms
2025-07-23 20:50:19 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2367 ms
2025-07-23 20:50:19 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2753 ms
2025-07-23 20:50:19 [Information] HTTP GET /images/login-background.png responded 200 in 0.7929 ms
2025-07-23 20:50:19 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3709 ms
2025-07-23 20:50:31 [Information] HTTP GET /Account/ForgotPassword responded 200 in 31.8257 ms
2025-07-23 20:50:31 [Information] HTTP GET /images/password-logo.png responded 200 in 0.3438 ms
2025-07-23 20:50:31 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2518 ms
2025-07-23 20:50:31 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.3290 ms
2025-07-23 20:50:31 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.3521 ms
2025-07-23 20:50:48 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:50:48 [Information] HTTP POST /Account/ForgotPassword responded 200 in 21.9942 ms
2025-07-23 20:50:48 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.8399 ms
2025-07-23 20:50:55 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:50:55 [Information] HTTP POST /Account/ForgotPassword responded 302 in 17.3894 ms
2025-07-23 20:50:55 [Error] Exception reading protected message
System.Security.Cryptography.CryptographicException: The provided payload cannot be decrypted because it was not protected with this protection provider. For more information go to https://aka.ms/aspnet/dataprotectionwarning
   at Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingBasedDataProtector.UnprotectCore(Byte[] protectedData, Boolean allowOperationsOnRevokedKeys, UnprotectStatus& status)
   at Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingBasedDataProtector.Unprotect(Byte[] protectedData)
   at Duende.IdentityServer.Stores.ProtectedDataMessageStore`1.ReadAsync(String value) in /_/identity-server/src/IdentityServer/Stores/Default/ProtectedDataMessageStore.cs:line 56
2025-07-23 20:50:55 [Information] HTTP GET /home/<USER>
2025-07-23 20:50:55 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.1530 ms
2025-07-23 20:51:22 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:51:22 [Information] HTTP POST /Account/ForgotPassword responded 200 in 3.5383 ms
2025-07-23 20:51:22 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.3096 ms
2025-07-23 20:51:26 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:51:26 [Information] HTTP POST /Account/ForgotPassword responded 302 in 3.2466 ms
2025-07-23 20:51:27 [Error] Exception reading protected message
System.Security.Cryptography.CryptographicException: The provided payload cannot be decrypted because it was not protected with this protection provider. For more information go to https://aka.ms/aspnet/dataprotectionwarning
   at Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingBasedDataProtector.UnprotectCore(Byte[] protectedData, Boolean allowOperationsOnRevokedKeys, UnprotectStatus& status)
   at Microsoft.AspNetCore.DataProtection.KeyManagement.KeyRingBasedDataProtector.Unprotect(Byte[] protectedData)
   at Duende.IdentityServer.Stores.ProtectedDataMessageStore`1.ReadAsync(String value) in /_/identity-server/src/IdentityServer/Stores/Default/ProtectedDataMessageStore.cs:line 56
2025-07-23 20:51:27 [Information] HTTP GET /home/<USER>
2025-07-23 20:51:41 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 20:51:41 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 20:51:41 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 20:51:41 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 20:51:41 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 20:51:41 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 20:51:41 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 20:51:41 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 20:51:41 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 20:51:41 [Information] Seeding database...
2025-07-23 20:51:42 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 20:51:42 [Debug] Clients already populated
2025-07-23 20:51:42 [Debug] IdentityResources already populated
2025-07-23 20:51:42 [Debug] ApiScopes already populated
2025-07-23 20:51:42 [Debug] OIDC IdentityProviders already populated
2025-07-23 20:51:43 [Information] Done seeding database. Exiting.
2025-07-23 20:51:43 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 20:51:43 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 20:51:43 [Debug] Login Url: /Account/Login
2025-07-23 20:51:43 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 20:51:43 [Debug] Logout Url: /Account/Logout
2025-07-23 20:51:43 [Debug] ConsentUrl Url: /consent
2025-07-23 20:51:43 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 20:51:43 [Debug] Error Url: /home/<USER>
2025-07-23 20:51:43 [Debug] Error Id Parameter: errorId
2025-07-23 20:51:43 [Information] HTTP GET / responded 302 in 61.3856 ms
2025-07-23 20:51:43 [Information] HTTP GET /Account/Login responded 200 in 166.2780 ms
2025-07-23 20:51:43 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 1.7850 ms
2025-07-23 20:51:50 [Information] HTTP GET /Account/Login responded 200 in 51.6956 ms
2025-07-23 20:51:51 [Information] HTTP GET /Account/ForgotPassword responded 200 in 23.3206 ms
2025-07-23 20:51:51 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.2491 ms
2025-07-23 20:51:54 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:51:55 [Information] HTTP POST /Account/ForgotPassword responded 200 in 15.1178 ms
2025-07-23 20:51:55 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.1561 ms
2025-07-23 20:52:20 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:52:20 [Information] HTTP POST /Account/ForgotPassword responded 200 in 15.2516 ms
2025-07-23 20:52:20 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0985 ms
2025-07-23 20:52:28 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:52:28 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 20:52:28 [Debug] Configured "Basic" authentication
2025-07-23 20:52:28 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:52:28 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.6553245"ms
2025-07-23 20:52:28 [Information] HTTP POST /Account/ForgotPassword responded 200 in 695.3814 ms
2025-07-23 20:52:28 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0953 ms
2025-07-23 20:52:28 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0899 ms
2025-07-23 20:52:38 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.7613 ms
2025-07-23 20:52:38 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.1007 ms
2025-07-23 20:52:38 [Information] HTTP GET /Account/Login responded 200 in 5.1899 ms
2025-07-23 20:52:40 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 8.1986 ms
2025-07-23 20:52:40 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 1.4135 ms
2025-07-23 20:52:40 [Information] HTTP GET /images/email-logo.png responded 200 in 3.0049 ms
2025-07-23 20:53:06 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 20:53:06 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 20:53:06 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 20:53:06 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 20:53:06 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 20:53:06 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 20:53:06 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 20:53:06 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 20:53:06 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 20:53:06 [Information] Seeding database...
2025-07-23 20:53:08 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 20:53:08 [Debug] Clients already populated
2025-07-23 20:53:08 [Debug] IdentityResources already populated
2025-07-23 20:53:08 [Debug] ApiScopes already populated
2025-07-23 20:53:08 [Debug] OIDC IdentityProviders already populated
2025-07-23 20:53:08 [Information] Done seeding database. Exiting.
2025-07-23 20:53:08 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 20:53:08 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 20:53:09 [Debug] Login Url: /Account/Login
2025-07-23 20:53:09 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 20:53:09 [Debug] Logout Url: /Account/Logout
2025-07-23 20:53:09 [Debug] ConsentUrl Url: /consent
2025-07-23 20:53:09 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 20:53:09 [Debug] Error Url: /home/<USER>
2025-07-23 20:53:09 [Debug] Error Id Parameter: errorId
2025-07-23 20:53:09 [Information] HTTP GET / responded 302 in 57.7927 ms
2025-07-23 20:53:09 [Information] HTTP GET /Account/Login responded 200 in 164.2156 ms
2025-07-23 20:54:12 [Information] HTTP GET /Account/Login responded 200 in 51.6122 ms
2025-07-23 20:54:12 [Information] HTTP GET /js/pages/login.js responded 304 in 1.8702 ms
2025-07-23 20:54:12 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 2.0143 ms
2025-07-23 20:54:12 [Information] HTTP GET /css/site.css responded 304 in 2.1087 ms
2025-07-23 20:54:14 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 13.4666 ms
2025-07-23 20:57:29 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 1.7391 ms
2025-07-23 20:57:29 [Information] HTTP GET /css/site.css.map responded 200 in 1.9311 ms
2025-07-23 20:57:29 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 5.2855 ms
2025-07-23 20:57:29 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 3.6381 ms
2025-07-23 20:57:30 [Information] HTTP GET /Account/Login responded 200 in 10.8080 ms
2025-07-23 20:57:30 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4478 ms
2025-07-23 20:57:30 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.1853 ms
2025-07-23 20:57:30 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0639 ms
2025-07-23 20:57:32 [Information] HTTP GET /Account/Login responded 200 in 2.9112 ms
2025-07-23 20:57:32 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4777 ms
2025-07-23 20:57:32 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.8191 ms
2025-07-23 20:57:32 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4396 ms
2025-07-23 20:57:32 [Information] HTTP GET /css/site.css responded 200 in 0.3103 ms
2025-07-23 20:57:32 [Information] HTTP GET /images/help.svg responded 200 in 0.4243 ms
2025-07-23 20:57:32 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.4228 ms
2025-07-23 20:57:32 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.3067 ms
2025-07-23 20:57:32 [Information] HTTP GET /images/logo.svg responded 200 in 0.4887 ms
2025-07-23 20:57:32 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.3637 ms
2025-07-23 20:57:32 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4786 ms
2025-07-23 20:57:32 [Information] HTTP GET /css/site.css.map responded 200 in 0.4471 ms
2025-07-23 20:57:32 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.6643 ms
2025-07-23 20:57:32 [Information] HTTP GET /js/index.js responded 200 in 0.1805 ms
2025-07-23 20:57:32 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4856 ms
2025-07-23 20:57:32 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3174 ms
2025-07-23 20:57:32 [Information] HTTP GET /js/components/input.js responded 200 in 0.1855 ms
2025-07-23 20:57:32 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2504 ms
2025-07-23 20:57:32 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2290 ms
2025-07-23 20:57:32 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2601 ms
2025-07-23 20:57:32 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.3227 ms
2025-07-23 20:57:32 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3882 ms
2025-07-23 20:57:32 [Information] HTTP GET /images/login-background.png responded 200 in 0.8327 ms
2025-07-23 20:57:32 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.1713 ms
2025-07-23 20:57:32 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3285 ms
2025-07-23 20:57:34 [Information] HTTP GET /Account/ForgotPassword responded 200 in 16.8407 ms
2025-07-23 20:57:34 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3976 ms
2025-07-23 20:57:34 [Information] HTTP GET /images/password-logo.png responded 200 in 0.2808 ms
2025-07-23 20:57:34 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1759 ms
2025-07-23 20:57:34 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.2585 ms
2025-07-23 20:57:37 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:57:37 [Information] HTTP POST /Account/ForgotPassword responded 200 in 20.5451 ms
2025-07-23 20:57:37 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3972 ms
2025-07-23 20:57:43 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:57:43 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 20:57:43 [Debug] Configured "Basic" authentication
2025-07-23 20:57:43 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:57:44 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.6688537"ms
2025-07-23 20:57:44 [Information] HTTP POST /Account/ForgotPassword responded 200 in 717.3492 ms
2025-07-23 20:57:44 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3927 ms
2025-07-23 20:57:44 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.2708 ms
2025-07-23 20:57:45 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.8379 ms
2025-07-23 20:57:45 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5312 ms
2025-07-23 20:57:46 [Information] HTTP GET /Account/Login responded 200 in 2.6039 ms
2025-07-23 20:57:46 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5394 ms
2025-07-23 20:57:47 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.8427 ms
2025-07-23 20:57:47 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5131 ms
2025-07-23 20:57:47 [Information] HTTP GET /images/email-logo.png responded 200 in 0.3070 ms
2025-07-23 20:57:47 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.3664 ms
2025-07-23 20:57:52 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 20:57:52 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-23 20:57:52 [Debug] Configured "Basic" authentication
2025-07-23 20:57:52 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 20:57:55 [Error] No CT Number has been found with this email address
2025-07-23 20:57:55 [Error] Failed to execute WebUtilitiesProxy.GetContactNoFromEmailAsync
System.Exception: WebServiceIsNotConnected: BadGateway
 ---> System.ServiceModel.FaultException: No CT Number has been found with this email address
   at System.ServiceModel.Channels.ServiceChannel.HandleReply(ProxyOperationRuntime operation, ProxyRpc& rpc)
   at System.ServiceModel.Channels.ServiceChannel.EndCall(String action, Object[] outs, IAsyncResult result)
   at System.ServiceModel.Channels.ServiceChannelProxy.TaskCreator.<>c__DisplayClass1_0.<CreateGenericTask>b__0(IAsyncResult asyncResult)
--- End of stack trace from previous location ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.WebUtilities.WebUtilitiesProxy.<>c__DisplayClass22_0.<<GetContactNoFromEmailAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.<WcfProxyBase>F273E8D520A9DEE9429D6E7AEA7C9D0833863FB601BE141AAD21C77977E1DE2BA__Extensions.<>c__DisplayClass0_0`1.<<ExecuteWithPolicyAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.Fallback.AsyncFallbackEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, Func`3 onFallbackAsync, Func`4 fallbackAction, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.WcfProxyBase`2.<>c__DisplayClass6_0`1.<CreatePolicy>b__4(DelegateResult`1 result)
   at Polly.AsyncFallbackTResultSyntax.<>c__DisplayClass3_0`1.<FallbackAsync>b__1(DelegateResult`1 outcome, Context _)
   at Polly.Fallback.AsyncFallbackEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, Func`3 onFallbackAsync, Func`4 fallbackAction, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.<WcfProxyBase>F273E8D520A9DEE9429D6E7AEA7C9D0833863FB601BE141AAD21C77977E1DE2BA__Extensions.ExecuteWithPolicyAsync[T](IAsyncPolicy`1 policy, Func`1 operation, String operationKey, CancellationToken cancellationToken)
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.WcfProxyBase`2.ExecuteServiceCallAsync[TResult](Func`2 operation, String operationName, CancellationToken cancellationToken)
2025-07-23 20:57:55 [Information] HTTP POST /Account/ForgotCTNumber responded 200 in 2772.3983 ms
2025-07-23 20:57:55 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5434 ms
2025-07-23 20:58:38 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 20:58:38 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 20:58:38 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 20:58:38 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 20:58:38 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 20:58:38 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 20:58:38 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 20:58:38 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 20:58:38 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 20:58:38 [Information] Seeding database...
2025-07-23 20:58:40 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 20:58:40 [Debug] Clients already populated
2025-07-23 20:58:40 [Debug] IdentityResources already populated
2025-07-23 20:58:40 [Debug] ApiScopes already populated
2025-07-23 20:58:40 [Debug] OIDC IdentityProviders already populated
2025-07-23 20:58:40 [Information] Done seeding database. Exiting.
2025-07-23 20:58:40 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 20:58:40 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 20:58:41 [Debug] Login Url: /Account/Login
2025-07-23 20:58:41 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 20:58:41 [Debug] Logout Url: /Account/Logout
2025-07-23 20:58:41 [Debug] ConsentUrl Url: /consent
2025-07-23 20:58:41 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 20:58:41 [Debug] Error Url: /home/<USER>
2025-07-23 20:58:41 [Debug] Error Id Parameter: errorId
2025-07-23 20:58:41 [Information] HTTP GET / responded 302 in 72.2110 ms
2025-07-23 20:58:41 [Information] HTTP GET /Account/Login responded 200 in 170.1206 ms
2025-07-23 20:59:18 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 5.6689 ms
2025-07-23 20:59:19 [Information] HTTP GET /Account/Login responded 200 in 45.7102 ms
2025-07-23 20:59:19 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5729 ms
2025-07-23 20:59:19 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 6.6506 ms
2025-07-23 20:59:19 [Information] HTTP GET /images/help.svg responded 200 in 4.5021 ms
2025-07-23 20:59:19 [Information] HTTP GET /css/site.css responded 200 in 5.0917 ms
2025-07-23 20:59:19 [Information] HTTP GET /images/help-blue.svg responded 200 in 4.4235 ms
2025-07-23 20:59:19 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 6.0944 ms
2025-07-23 20:59:19 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2646 ms
2025-07-23 20:59:19 [Information] HTTP GET /images/logo.svg responded 200 in 0.4631 ms
2025-07-23 20:59:19 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.3894 ms
2025-07-23 20:59:19 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.6674 ms
2025-07-23 20:59:19 [Information] HTTP GET /css/site.css.map responded 200 in 0.3138 ms
2025-07-23 20:59:19 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 3.1094 ms
2025-07-23 20:59:19 [Information] HTTP GET /js/index.js responded 200 in 0.2659 ms
2025-07-23 20:59:19 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.6864 ms
2025-07-23 20:59:19 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3225 ms
2025-07-23 20:59:19 [Information] HTTP GET /js/components/input.js responded 200 in 0.3433 ms
2025-07-23 20:59:19 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.3037 ms
2025-07-23 20:59:19 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2480 ms
2025-07-23 20:59:19 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2823 ms
2025-07-23 20:59:19 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2257 ms
2025-07-23 20:59:19 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3434 ms
2025-07-23 20:59:19 [Information] HTTP GET /images/login-background.png responded 200 in 0.8832 ms
2025-07-23 20:59:19 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.8100 ms
2025-07-23 20:59:20 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3417 ms
2025-07-23 20:59:24 [Information] HTTP GET /Account/Login responded 200 in 13.8633 ms
2025-07-23 20:59:24 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.7981 ms
2025-07-23 20:59:25 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 14.9354 ms
2025-07-23 20:59:25 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6013 ms
2025-07-23 20:59:25 [Information] HTTP GET /images/email-logo.png responded 200 in 0.2826 ms
2025-07-23 20:59:25 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2041 ms
2025-07-23 20:59:26 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.2920 ms
2025-07-23 20:59:32 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.3116 ms
2025-07-23 21:01:35 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 2.7161 ms
2025-07-23 21:01:35 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4530 ms
2025-07-23 21:01:35 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.9581 ms
2025-07-23 21:01:35 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2764 ms
2025-07-23 21:01:35 [Information] HTTP GET /css/site.css responded 200 in 0.5030 ms
2025-07-23 21:01:35 [Information] HTTP GET /images/help.svg responded 200 in 0.4084 ms
2025-07-23 21:01:35 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.4307 ms
2025-07-23 21:01:35 [Information] HTTP GET /images/email-logo.png responded 200 in 0.3226 ms
2025-07-23 21:01:35 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.3174 ms
2025-07-23 21:01:35 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3776 ms
2025-07-23 21:01:35 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4603 ms
2025-07-23 21:01:35 [Information] HTTP GET /js/index.js responded 200 in 0.2552 ms
2025-07-23 21:01:35 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2057 ms
2025-07-23 21:01:35 [Information] HTTP GET /js/components/input.js responded 200 in 0.2302 ms
2025-07-23 21:01:35 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2686 ms
2025-07-23 21:01:35 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2807 ms
2025-07-23 21:01:35 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2281 ms
2025-07-23 21:01:35 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2535 ms
2025-07-23 21:01:35 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.1929 ms
2025-07-23 21:01:35 [Information] HTTP GET /css/site.css.map responded 200 in 0.2633 ms
2025-07-23 21:01:35 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.6838 ms
2025-07-23 21:01:35 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 27.5818 ms
2025-07-23 21:01:35 [Information] HTTP GET /images/login-background.png responded 200 in 27.0672 ms
2025-07-23 21:01:36 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2922 ms
2025-07-23 21:01:36 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.3142 ms
2025-07-23 21:01:41 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 4.2295 ms
2025-07-23 21:01:41 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3897 ms
2025-07-23 21:01:41 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.9268 ms
2025-07-23 21:01:41 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.6119 ms
2025-07-23 21:01:41 [Information] HTTP GET /images/help.svg responded 200 in 0.3305 ms
2025-07-23 21:01:41 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.3102 ms
2025-07-23 21:01:41 [Information] HTTP GET /css/site.css responded 200 in 0.7108 ms
2025-07-23 21:01:41 [Information] HTTP GET /images/email-logo.png responded 200 in 0.2708 ms
2025-07-23 21:01:41 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.3291 ms
2025-07-23 21:01:41 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4990 ms
2025-07-23 21:01:41 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.5409 ms
2025-07-23 21:01:41 [Information] HTTP GET /css/site.css.map responded 200 in 0.3380 ms
2025-07-23 21:01:41 [Information] HTTP GET /js/index.js responded 200 in 0.2086 ms
2025-07-23 21:01:41 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.3047 ms
2025-07-23 21:01:41 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2513 ms
2025-07-23 21:01:41 [Information] HTTP GET /js/components/input.js responded 200 in 0.3802 ms
2025-07-23 21:01:41 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.3363 ms
2025-07-23 21:01:41 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3158 ms
2025-07-23 21:01:41 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2339 ms
2025-07-23 21:01:41 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1674 ms
2025-07-23 21:01:41 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.2988 ms
2025-07-23 21:01:41 [Information] HTTP GET /images/login-background.png responded 200 in 0.9695 ms
2025-07-23 21:01:41 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.4715 ms
2025-07-23 21:01:41 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3520 ms
2025-07-23 21:02:09 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.8877 ms
2025-07-23 21:02:09 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5461 ms
2025-07-23 21:02:09 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.6856 ms
2025-07-23 21:02:09 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.3408 ms
2025-07-23 21:02:09 [Information] HTTP GET /css/site.css responded 200 in 0.5435 ms
2025-07-23 21:02:09 [Information] HTTP GET /images/help.svg responded 200 in 0.2011 ms
2025-07-23 21:02:09 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1979 ms
2025-07-23 21:02:09 [Information] HTTP GET /images/email-logo.png responded 200 in 0.2142 ms
2025-07-23 21:02:09 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1808 ms
2025-07-23 21:02:09 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4300 ms
2025-07-23 21:02:09 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4818 ms
2025-07-23 21:02:09 [Information] HTTP GET /css/site.css.map responded 200 in 0.3182 ms
2025-07-23 21:02:10 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.4144 ms
2025-07-23 21:02:10 [Information] HTTP GET /js/index.js responded 200 in 0.1781 ms
2025-07-23 21:02:10 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1620 ms
2025-07-23 21:02:10 [Information] HTTP GET /js/components/input.js responded 200 in 0.2079 ms
2025-07-23 21:02:10 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1455 ms
2025-07-23 21:02:10 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.1766 ms
2025-07-23 21:02:10 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1278 ms
2025-07-23 21:02:10 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1829 ms
2025-07-23 21:02:10 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.1457 ms
2025-07-23 21:02:10 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.8747 ms
2025-07-23 21:02:10 [Information] HTTP GET /images/login-background.png responded 200 in 0.6304 ms
2025-07-23 21:02:10 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2042 ms
2025-07-23 21:02:11 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.4534 ms
2025-07-23 21:02:30 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.5073 ms
2025-07-23 21:02:30 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.8409 ms
2025-07-23 21:02:30 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4137 ms
2025-07-23 21:02:30 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3867 ms
2025-07-23 21:02:30 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1556 ms
2025-07-23 21:02:30 [Information] HTTP GET /images/help.svg responded 200 in 0.2617 ms
2025-07-23 21:02:30 [Information] HTTP GET /css/site.css responded 200 in 0.3411 ms
2025-07-23 21:02:30 [Information] HTTP GET /images/email-logo.png responded 200 in 0.2501 ms
2025-07-23 21:02:30 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2838 ms
2025-07-23 21:02:30 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3237 ms
2025-07-23 21:02:30 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.3668 ms
2025-07-23 21:02:30 [Information] HTTP GET /css/site.css.map responded 200 in 0.2136 ms
2025-07-23 21:02:30 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.3989 ms
2025-07-23 21:02:30 [Information] HTTP GET /js/index.js responded 200 in 0.1504 ms
2025-07-23 21:02:30 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1561 ms
2025-07-23 21:02:30 [Information] HTTP GET /js/components/input.js responded 200 in 0.2074 ms
2025-07-23 21:02:30 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2317 ms
2025-07-23 21:02:30 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2258 ms
2025-07-23 21:02:30 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1774 ms
2025-07-23 21:02:30 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.1744 ms
2025-07-23 21:02:30 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2354 ms
2025-07-23 21:02:30 [Information] HTTP GET /images/login-background.png responded 200 in 0.8785 ms
2025-07-23 21:02:30 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.1853 ms
2025-07-23 21:02:30 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2278 ms
2025-07-23 21:02:32 [Information] HTTP GET /images/error-warning.svg responded 200 in 1.0375 ms
2025-07-23 21:02:42 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 21:02:42 [Information] HTTP POST /Account/ForgotCTNumber responded 200 in 11.6940 ms
2025-07-23 21:02:42 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3955 ms
2025-07-23 21:02:42 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 304 in 0.6858 ms
2025-07-23 21:03:31 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 21:03:31 [Information] HTTP POST /Account/ForgotCTNumber responded 200 in 3.4253 ms
2025-07-23 21:03:31 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3545 ms
2025-07-23 21:03:31 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.6865 ms
2025-07-23 21:03:31 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2580 ms
2025-07-23 21:03:31 [Information] HTTP GET /css/site.css responded 200 in 0.3410 ms
2025-07-23 21:03:31 [Information] HTTP GET /images/help.svg responded 200 in 0.1843 ms
2025-07-23 21:03:31 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2085 ms
2025-07-23 21:03:31 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1782 ms
2025-07-23 21:03:31 [Information] HTTP GET /images/email-logo.png responded 200 in 0.3335 ms
2025-07-23 21:03:31 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4060 ms
2025-07-23 21:03:31 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.2876 ms
2025-07-23 21:03:31 [Information] HTTP GET /css/site.css.map responded 200 in 0.2739 ms
2025-07-23 21:03:31 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.1779 ms
2025-07-23 21:03:31 [Information] HTTP GET /js/index.js responded 200 in 0.1813 ms
2025-07-23 21:03:31 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1733 ms
2025-07-23 21:03:31 [Information] HTTP GET /js/components/input.js responded 200 in 0.3054 ms
2025-07-23 21:03:31 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1730 ms
2025-07-23 21:03:31 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1993 ms
2025-07-23 21:03:31 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2352 ms
2025-07-23 21:03:31 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2181 ms
2025-07-23 21:03:31 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.1425 ms
2025-07-23 21:03:32 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.2009 ms
2025-07-23 21:03:32 [Information] HTTP GET /images/login-background.png responded 200 in 0.6040 ms
2025-07-23 21:03:32 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3079 ms
2025-07-23 21:03:34 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.5642 ms
2025-07-23 21:03:34 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 21:03:34 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-23 21:03:34 [Debug] Configured "Basic" authentication
2025-07-23 21:03:34 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 21:03:35 [Error] No CT Number has been found with this email address
2025-07-23 21:03:35 [Error] Failed to execute WebUtilitiesProxy.GetContactNoFromEmailAsync
System.Exception: WebServiceIsNotConnected: BadGateway
 ---> System.ServiceModel.FaultException: No CT Number has been found with this email address
   at System.ServiceModel.Channels.ServiceChannel.HandleReply(ProxyOperationRuntime operation, ProxyRpc& rpc)
   at System.ServiceModel.Channels.ServiceChannel.EndCall(String action, Object[] outs, IAsyncResult result)
   at System.ServiceModel.Channels.ServiceChannelProxy.TaskCreator.<>c__DisplayClass1_0.<CreateGenericTask>b__0(IAsyncResult asyncResult)
--- End of stack trace from previous location ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.WebUtilities.WebUtilitiesProxy.<>c__DisplayClass22_0.<<GetContactNoFromEmailAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.<WcfProxyBase>F273E8D520A9DEE9429D6E7AEA7C9D0833863FB601BE141AAD21C77977E1DE2BA__Extensions.<>c__DisplayClass0_0`1.<<ExecuteWithPolicyAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.Fallback.AsyncFallbackEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, Func`3 onFallbackAsync, Func`4 fallbackAction, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.WcfProxyBase`2.<>c__DisplayClass6_0`1.<CreatePolicy>b__4(DelegateResult`1 result)
   at Polly.AsyncFallbackTResultSyntax.<>c__DisplayClass3_0`1.<FallbackAsync>b__1(DelegateResult`1 outcome, Context _)
   at Polly.Fallback.AsyncFallbackEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, Func`3 onFallbackAsync, Func`4 fallbackAction, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.<WcfProxyBase>F273E8D520A9DEE9429D6E7AEA7C9D0833863FB601BE141AAD21C77977E1DE2BA__Extensions.ExecuteWithPolicyAsync[T](IAsyncPolicy`1 policy, Func`1 operation, String operationKey, CancellationToken cancellationToken)
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.WcfProxyBase`2.ExecuteServiceCallAsync[TResult](Func`2 operation, String operationName, CancellationToken cancellationToken)
2025-07-23 21:03:35 [Information] HTTP POST /Account/ForgotCTNumber responded 200 in 741.9322 ms
2025-07-23 21:03:35 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4068 ms
2025-07-23 21:04:42 [Information] HTTP GET /Account/Login responded 200 in 2.8509 ms
2025-07-23 21:04:42 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3711 ms
2025-07-23 21:04:42 [Information] HTTP GET /images/logo.svg responded 200 in 0.4095 ms
2025-07-23 21:04:42 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.1964 ms
2025-07-23 21:04:42 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.1567 ms
2025-07-23 21:04:42 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3591 ms
2025-07-23 21:04:44 [Information] HTTP GET /Account/ForgotPassword responded 200 in 16.1922 ms
2025-07-23 21:04:44 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4047 ms
2025-07-23 21:04:44 [Information] HTTP GET /images/password-logo.png responded 200 in 0.2088 ms
2025-07-23 21:04:44 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.1385 ms
2025-07-23 21:05:09 [Information] HTTP GET /Account/ForgotPassword responded 200 in 2.4608 ms
2025-07-23 21:05:09 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5134 ms
2025-07-23 21:05:09 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.0856 ms
2025-07-23 21:05:09 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.3390 ms
2025-07-23 21:05:09 [Information] HTTP GET /css/site.css responded 200 in 0.3552 ms
2025-07-23 21:05:09 [Information] HTTP GET /images/help.svg responded 200 in 0.2144 ms
2025-07-23 21:05:09 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1505 ms
2025-07-23 21:05:09 [Information] HTTP GET /images/password-logo.png responded 200 in 0.2181 ms
2025-07-23 21:05:09 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2296 ms
2025-07-23 21:05:09 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2310 ms
2025-07-23 21:05:09 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.3330 ms
2025-07-23 21:05:09 [Information] HTTP GET /css/site.css.map responded 200 in 0.2263 ms
2025-07-23 21:05:09 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.3252 ms
2025-07-23 21:05:09 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4319 ms
2025-07-23 21:05:09 [Information] HTTP GET /js/index.js responded 200 in 0.1456 ms
2025-07-23 21:05:09 [Information] HTTP GET /js/components/input.js responded 200 in 0.2203 ms
2025-07-23 21:05:09 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2344 ms
2025-07-23 21:05:09 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1937 ms
2025-07-23 21:05:09 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2432 ms
2025-07-23 21:05:09 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2414 ms
2025-07-23 21:05:09 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2571 ms
2025-07-23 21:05:09 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.1872 ms
2025-07-23 21:05:09 [Information] HTTP GET /images/login-background.png responded 200 in 0.4167 ms
2025-07-23 21:05:09 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.6309 ms
2025-07-23 21:05:10 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2025 ms
2025-07-23 21:05:11 [Information] HTTP GET /Account/Login responded 200 in 2.4032 ms
2025-07-23 21:05:11 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3496 ms
2025-07-23 21:05:11 [Information] HTTP GET /images/logo.svg responded 200 in 0.3511 ms
2025-07-23 21:05:11 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2102 ms
2025-07-23 21:05:11 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2414 ms
2025-07-23 21:05:16 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.4443 ms
2025-07-23 21:05:16 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3765 ms
2025-07-23 21:05:16 [Information] HTTP GET /images/email-logo.png responded 200 in 0.2031 ms
2025-07-23 21:05:16 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.2035 ms
2025-07-23 21:05:20 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 21:05:20 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-23 21:05:20 [Debug] Configured "Basic" authentication
2025-07-23 21:05:20 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 21:05:21 [Error] No CT Number has been found with this email address
2025-07-23 21:05:21 [Error] Failed to execute WebUtilitiesProxy.GetContactNoFromEmailAsync
System.Exception: WebServiceIsNotConnected: BadGateway
 ---> System.ServiceModel.FaultException: No CT Number has been found with this email address
   at System.ServiceModel.Channels.ServiceChannel.HandleReply(ProxyOperationRuntime operation, ProxyRpc& rpc)
   at System.ServiceModel.Channels.ServiceChannel.EndCall(String action, Object[] outs, IAsyncResult result)
   at System.ServiceModel.Channels.ServiceChannelProxy.TaskCreator.<>c__DisplayClass1_0.<CreateGenericTask>b__0(IAsyncResult asyncResult)
--- End of stack trace from previous location ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.WebUtilities.WebUtilitiesProxy.<>c__DisplayClass22_0.<<GetContactNoFromEmailAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.<WcfProxyBase>F273E8D520A9DEE9429D6E7AEA7C9D0833863FB601BE141AAD21C77977E1DE2BA__Extensions.<>c__DisplayClass0_0`1.<<ExecuteWithPolicyAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.Fallback.AsyncFallbackEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, Func`3 onFallbackAsync, Func`4 fallbackAction, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.WcfProxyBase`2.<>c__DisplayClass6_0`1.<CreatePolicy>b__4(DelegateResult`1 result)
   at Polly.AsyncFallbackTResultSyntax.<>c__DisplayClass3_0`1.<FallbackAsync>b__1(DelegateResult`1 outcome, Context _)
   at Polly.Fallback.AsyncFallbackEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, Func`3 onFallbackAsync, Func`4 fallbackAction, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.<WcfProxyBase>F273E8D520A9DEE9429D6E7AEA7C9D0833863FB601BE141AAD21C77977E1DE2BA__Extensions.ExecuteWithPolicyAsync[T](IAsyncPolicy`1 policy, Func`1 operation, String operationKey, CancellationToken cancellationToken)
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.WcfProxyBase`2.ExecuteServiceCallAsync[TResult](Func`2 operation, String operationName, CancellationToken cancellationToken)
2025-07-23 21:05:21 [Information] HTTP POST /Account/ForgotCTNumber responded 200 in 447.3792 ms
2025-07-23 21:05:21 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3404 ms
2025-07-23 21:05:21 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.2054 ms
2025-07-23 21:39:08 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 21:39:08 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-23 21:39:08 [Debug] Configured "Basic" authentication
2025-07-23 21:39:08 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 21:39:08 [Error] No CT Number has been found with this email address
2025-07-23 21:39:08 [Error] Failed to execute WebUtilitiesProxy.GetContactNoFromEmailAsync
System.Exception: WebServiceIsNotConnected: BadGateway
 ---> System.ServiceModel.FaultException: No CT Number has been found with this email address
   at System.ServiceModel.Channels.ServiceChannel.HandleReply(ProxyOperationRuntime operation, ProxyRpc& rpc)
   at System.ServiceModel.Channels.ServiceChannel.EndCall(String action, Object[] outs, IAsyncResult result)
   at System.ServiceModel.Channels.ServiceChannelProxy.TaskCreator.<>c__DisplayClass1_0.<CreateGenericTask>b__0(IAsyncResult asyncResult)
--- End of stack trace from previous location ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.WebUtilities.WebUtilitiesProxy.<>c__DisplayClass22_0.<<GetContactNoFromEmailAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.<WcfProxyBase>F273E8D520A9DEE9429D6E7AEA7C9D0833863FB601BE141AAD21C77977E1DE2BA__Extensions.<>c__DisplayClass0_0`1.<<ExecuteWithPolicyAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.Fallback.AsyncFallbackEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, Func`3 onFallbackAsync, Func`4 fallbackAction, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.WcfProxyBase`2.<>c__DisplayClass6_0`1.<CreatePolicy>b__4(DelegateResult`1 result)
   at Polly.AsyncFallbackTResultSyntax.<>c__DisplayClass3_0`1.<FallbackAsync>b__1(DelegateResult`1 outcome, Context _)
   at Polly.Fallback.AsyncFallbackEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, Func`3 onFallbackAsync, Func`4 fallbackAction, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.<WcfProxyBase>F273E8D520A9DEE9429D6E7AEA7C9D0833863FB601BE141AAD21C77977E1DE2BA__Extensions.ExecuteWithPolicyAsync[T](IAsyncPolicy`1 policy, Func`1 operation, String operationKey, CancellationToken cancellationToken)
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.WcfProxyBase`2.ExecuteServiceCallAsync[TResult](Func`2 operation, String operationName, CancellationToken cancellationToken)
2025-07-23 21:39:08 [Information] HTTP POST /Account/ForgotCTNumber responded 200 in 355.2331 ms
2025-07-23 21:39:08 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4196 ms
2025-07-23 21:39:08 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.7365 ms
2025-07-23 21:39:08 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.3708 ms
2025-07-23 21:39:08 [Information] HTTP GET /images/help.svg responded 200 in 0.2396 ms
2025-07-23 21:39:08 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.3093 ms
2025-07-23 21:39:08 [Information] HTTP GET /css/site.css responded 200 in 0.9257 ms
2025-07-23 21:39:08 [Information] HTTP GET /images/email-logo.png responded 200 in 0.1991 ms
2025-07-23 21:39:08 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1442 ms
2025-07-23 21:39:08 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4193 ms
2025-07-23 21:39:08 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4190 ms
2025-07-23 21:39:08 [Information] HTTP GET /js/index.js responded 200 in 0.1840 ms
2025-07-23 21:39:08 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1623 ms
2025-07-23 21:39:08 [Information] HTTP GET /css/site.css.map responded 200 in 0.3842 ms
2025-07-23 21:39:08 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.5461 ms
2025-07-23 21:39:08 [Information] HTTP GET /js/components/input.js responded 200 in 0.2004 ms
2025-07-23 21:39:08 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1829 ms
2025-07-23 21:39:08 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2813 ms
2025-07-23 21:39:08 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2914 ms
2025-07-23 21:39:08 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2131 ms
2025-07-23 21:39:08 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.1857 ms
2025-07-23 21:39:09 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.2319 ms
2025-07-23 21:39:09 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.7734 ms
2025-07-23 21:39:09 [Information] HTTP GET /images/login-background.png responded 200 in 0.6607 ms
2025-07-23 21:39:09 [Information] HTTP GET /images/favicon.ico responded 200 in 0.1696 ms
2025-07-23 21:40:15 [Information] HTTP GET /Account/Login responded 200 in 619.0877 ms
2025-07-23 21:40:15 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4168 ms
2025-07-23 21:40:15 [Information] HTTP GET /images/logo.svg responded 200 in 0.3581 ms
2025-07-23 21:40:15 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.3023 ms
2025-07-23 21:40:15 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2070 ms
2025-07-23 21:40:15 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0608 ms
2025-07-23 21:40:15 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2181 ms
2025-07-23 21:40:16 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.9862 ms
2025-07-23 21:40:16 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4913 ms
2025-07-23 21:40:16 [Information] HTTP GET /images/password-logo.png responded 200 in 0.2028 ms
2025-07-23 21:40:16 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.1638 ms
2025-07-23 21:40:27 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 21:40:27 [Information] HTTP POST /Account/ForgotPassword responded 200 in 11.1034 ms
2025-07-23 21:40:27 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3075 ms
2025-07-23 21:40:27 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0849 ms
2025-07-23 21:41:51 [Information] HTTP GET /Account/ForgotPassword responded 200 in 2.2270 ms
2025-07-23 21:41:51 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4378 ms
2025-07-23 21:41:51 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0862 ms
2025-07-23 21:41:52 [Information] HTTP GET /Account/Login responded 200 in 3.0629 ms
2025-07-23 21:41:52 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3715 ms
2025-07-23 21:41:53 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.3463 ms
2025-07-23 21:41:53 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4323 ms
2025-07-23 21:43:18 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 1.2349 ms
2025-07-23 21:43:18 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3646 ms
2025-07-23 21:43:18 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.9150 ms
2025-07-23 21:43:18 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.3747 ms
2025-07-23 21:43:18 [Information] HTTP GET /css/site.css responded 200 in 0.4957 ms
2025-07-23 21:43:18 [Information] HTTP GET /images/help.svg responded 200 in 0.2750 ms
2025-07-23 21:43:18 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.1589 ms
2025-07-23 21:43:18 [Information] HTTP GET /images/email-logo.png responded 200 in 0.2016 ms
2025-07-23 21:43:18 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1576 ms
2025-07-23 21:43:18 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.3570 ms
2025-07-23 21:43:18 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4201 ms
2025-07-23 21:43:18 [Information] HTTP GET /css/site.css.map responded 200 in 0.2401 ms
2025-07-23 21:43:18 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.8023 ms
2025-07-23 21:43:18 [Information] HTTP GET /js/index.js responded 200 in 0.1556 ms
2025-07-23 21:43:18 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.1284 ms
2025-07-23 21:43:18 [Information] HTTP GET /js/components/input.js responded 200 in 0.2747 ms
2025-07-23 21:43:18 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2854 ms
2025-07-23 21:43:18 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2467 ms
2025-07-23 21:43:18 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.1380 ms
2025-07-23 21:43:18 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2768 ms
2025-07-23 21:43:18 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.1912 ms
2025-07-23 21:43:18 [Information] HTTP GET /images/login-background.png responded 200 in 0.3994 ms
2025-07-23 21:43:18 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.1656 ms
2025-07-23 21:43:18 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2361 ms
2025-07-23 21:43:20 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.6266 ms
2025-07-23 21:43:34 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 21:43:34 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-23 21:43:34 [Debug] Configured "Basic" authentication
2025-07-23 21:43:34 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 21:43:35 [Error] No CT Number has been found with this email address
2025-07-23 21:43:35 [Error] Failed to execute WebUtilitiesProxy.GetContactNoFromEmailAsync
System.Exception: WebServiceIsNotConnected: BadGateway
 ---> System.ServiceModel.FaultException: No CT Number has been found with this email address
   at System.ServiceModel.Channels.ServiceChannel.HandleReply(ProxyOperationRuntime operation, ProxyRpc& rpc)
   at System.ServiceModel.Channels.ServiceChannel.EndCall(String action, Object[] outs, IAsyncResult result)
   at System.ServiceModel.Channels.ServiceChannelProxy.TaskCreator.<>c__DisplayClass1_0.<CreateGenericTask>b__0(IAsyncResult asyncResult)
--- End of stack trace from previous location ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.WebUtilities.WebUtilitiesProxy.<>c__DisplayClass22_0.<<GetContactNoFromEmailAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.<WcfProxyBase>F273E8D520A9DEE9429D6E7AEA7C9D0833863FB601BE141AAD21C77977E1DE2BA__Extensions.<>c__DisplayClass0_0`1.<<ExecuteWithPolicyAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.Fallback.AsyncFallbackEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, Func`3 onFallbackAsync, Func`4 fallbackAction, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.WcfProxyBase`2.<>c__DisplayClass6_0`1.<CreatePolicy>b__4(DelegateResult`1 result)
   at Polly.AsyncFallbackTResultSyntax.<>c__DisplayClass3_0`1.<FallbackAsync>b__1(DelegateResult`1 outcome, Context _)
   at Polly.Fallback.AsyncFallbackEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, Func`3 onFallbackAsync, Func`4 fallbackAction, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.<WcfProxyBase>F273E8D520A9DEE9429D6E7AEA7C9D0833863FB601BE141AAD21C77977E1DE2BA__Extensions.ExecuteWithPolicyAsync[T](IAsyncPolicy`1 policy, Func`1 operation, String operationKey, CancellationToken cancellationToken)
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.WcfProxyBase`2.ExecuteServiceCallAsync[TResult](Func`2 operation, String operationName, CancellationToken cancellationToken)
2025-07-23 21:43:35 [Information] HTTP POST /Account/ForgotCTNumber responded 200 in 407.2002 ms
2025-07-23 21:43:35 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3466 ms
2025-07-23 21:43:35 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 304 in 0.0674 ms
2025-07-23 21:43:45 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 21:43:45 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 21:43:45 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 21:43:45 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 21:43:45 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 21:43:45 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 21:43:45 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 21:43:45 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 21:43:45 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 21:43:45 [Information] Seeding database...
2025-07-23 21:43:46 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 21:43:46 [Debug] Clients already populated
2025-07-23 21:43:46 [Debug] IdentityResources already populated
2025-07-23 21:43:46 [Debug] ApiScopes already populated
2025-07-23 21:43:46 [Debug] OIDC IdentityProviders already populated
2025-07-23 21:43:46 [Information] Done seeding database. Exiting.
2025-07-23 21:43:47 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 21:43:47 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 21:43:47 [Debug] Login Url: /Account/Login
2025-07-23 21:43:47 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 21:43:47 [Debug] Logout Url: /Account/Logout
2025-07-23 21:43:47 [Debug] ConsentUrl Url: /consent
2025-07-23 21:43:47 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 21:43:47 [Debug] Error Url: /home/<USER>
2025-07-23 21:43:47 [Debug] Error Id Parameter: errorId
2025-07-23 21:43:47 [Information] HTTP GET / responded 302 in 63.2971 ms
2025-07-23 21:43:48 [Information] HTTP GET /Account/Login responded 200 in 152.6206 ms
2025-07-23 21:43:48 [Information] HTTP GET /images/logo.svg responded 200 in 5.8544 ms
2025-07-23 21:43:48 [Information] HTTP GET /images/info-circle.svg responded 200 in 5.3141 ms
2025-07-23 21:43:48 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.6491 ms
2025-07-23 21:43:48 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3693 ms
2025-07-23 21:43:48 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 1.1393 ms
2025-07-23 21:45:12 [Information] HTTP GET /Account/Login responded 200 in 51.9442 ms
2025-07-23 21:45:13 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.2082 ms
2025-07-23 21:45:15 [Information] HTTP GET /Account/Setup responded 200 in 31.1980 ms
2025-07-23 21:45:15 [Information] HTTP GET /images/step-active.png responded 200 in 0.2975 ms
2025-07-23 21:45:15 [Information] HTTP GET /images/step-next.png responded 200 in 0.2674 ms
2025-07-23 21:46:06 [Information] HTTP GET /Account/Login responded 200 in 5.5756 ms
2025-07-23 21:46:06 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.4223 ms
2025-07-23 21:46:16 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 21:46:17 [Information] Contact?$filter=(No eq 'CT123445')
2025-07-23 21:46:17 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123445","Type":"Person","Organizational_Level_Code":"H'CARE TM","Company_No":"CT098765","Company_Name":"Eastbourne District General Hospital Aubagio - CP","Anonymise":false,"IntegrationCustomerNo":"EDGHAUB-CP","Name":"CT123445","First_Name":"CT123445","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123445","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT038707","Group_Consortium_Name":"South East Coast NHS Commercial Solutions","Wholesaler_Trust":"CT051142","Wholesaler_Trust_Name":"East Sussex Healthcare NHS Trust","Hospital":"CT054870","Hospital_Name":"Eastbourne District General Hospital","Hospital_No":"","Bill_to_Customer_No":"EDGHAUB-CP","Therapy":"AUBAGIO","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"MS","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"SOUTH EAST - B","Salutation_Code":"","Registration_Date":"2020-07-06","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-02-12","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":"Email","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":1,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 21:46:17 [Information] Contact?$filter=(No eq 'CT098765')
2025-07-23 21:46:17 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXdBRGtBT0FBM0FEWUFOUUFBQUFBQTExOzExMTE5MzM0MDMwMDsn\"","No":"CT098765","Type":"Company","Organizational_Level_Code":"","Company_No":"CT098765","Company_Name":"Eastbourne District General Hospital Aubagio - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Eastbourne District General Hospital Aubagio - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"KINGS DRIVE","Address_2":"","City":"","County":"EASTBOURNE","Post_Code":"BN21 2UD","Country_Region_Code":"GB","Search_Name":"EASTBOURNE DISTRICT HOSPITAL AUBAGIO CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT038707","Group_Consortium_Name":"South East Coast NHS Commercial Solutions","Wholesaler_Trust":"CT051142","Wholesaler_Trust_Name":"East Sussex Healthcare NHS Trust","Hospital":"CT054870","Hospital_Name":"Eastbourne District General Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AUBAGIO","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"SOUTH EAST - B","Salutation_Code":"","Registration_Date":"2019-10-22","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2020-08-21","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 21:46:17 [Information] {"Username":"CT123445","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNEA33E1QJO0:0000001D","TimeStamp":"2025-07-23T18:46:17.4177341","ProcessId":39324,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-23 21:46:17 [Information] HTTP POST /Account/Login responded 200 in 444.5158 ms
2025-07-23 21:46:27 [Information] HTTP POST /Account/Login responded 200 in 193.0533 ms
2025-07-23 21:46:28 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 21:46:28 [Information] Contact?$filter=(No eq 'CT411887')
2025-07-23 21:46:28 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411887","Type":"Person","Organizational_Level_Code":"TRUST PHAR","Company_No":"CT031253","Company_Name":"Birmingham Womens and Children NHS Foundation Trus","Anonymise":false,"IntegrationCustomerNo":"","Name":"Test ClinicianTwo","First_Name":"Test","Middle_Name":"","Surname":"ClinicianTwo","Address":"","Address_2":"","City":"","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"TEST CLINICIANTWO","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"-","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"HUMIRA-AX","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"","Salutation_Code":"","Registration_Date":"2025-01-20","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":0,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 21:46:28 [Information] Contact?$filter=(No eq 'CT031253')
2025-07-23 21:46:28 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXdBRE1BTVFBeUFEVUFNd0FBQUFBQTExOzExMTE5MjY4MDExMDsn\"","No":"CT031253","Type":"Company","Organizational_Level_Code":"","Company_No":"CT031253","Company_Name":"Birmingham Womens and Children NHS Foundation Trus","Anonymise":false,"IntegrationCustomerNo":"","Name":"Birmingham Womens and Children NHS Foundation Trus","First_Name":"Birmingham Womens and Children","Middle_Name":"","Surname":"NHS Foundation Trust","Address":"STEELHOUSE LANE","Address_2":"","City":"","County":"BIRMINGHAM","Post_Code":"B4 6NH","Country_Region_Code":"GB","Search_Name":"BIRMINGHAM WOMENS AND CHILDREN NHS FOUNDATION TRUS","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"-","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"HUMIRA-AX","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"","Salutation_Code":"","Registration_Date":"2017-11-07","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2023-04-04","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":1,"No_of_Business_Relations":0,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 21:46:28 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 21:46:28 [Debug] Configured "Basic" authentication
2025-07-23 21:46:28 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 21:46:29 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.6241501"ms
2025-07-23 21:46:29 [Information] {"Username":"CT411887","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNEA33E1QJO0:********","TimeStamp":"2025-07-23T18:46:29.0502827","ProcessId":39324,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-23 21:46:29 [Information] HTTP POST /Account/Login responded 200 in 787.3541 ms
2025-07-23 21:47:24 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 21:47:24 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 21:47:24 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 21:47:24 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 21:47:24 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 21:47:24 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 21:47:24 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 21:47:24 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 21:47:24 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 21:47:24 [Information] Seeding database...
2025-07-23 21:47:25 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 21:47:26 [Debug] Clients already populated
2025-07-23 21:47:26 [Debug] IdentityResources already populated
2025-07-23 21:47:26 [Debug] ApiScopes already populated
2025-07-23 21:47:26 [Debug] OIDC IdentityProviders already populated
2025-07-23 21:47:26 [Information] Done seeding database. Exiting.
2025-07-23 21:47:26 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 21:47:26 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 21:47:26 [Debug] Login Url: /Account/Login
2025-07-23 21:47:26 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 21:47:26 [Debug] Logout Url: /Account/Logout
2025-07-23 21:47:26 [Debug] ConsentUrl Url: /consent
2025-07-23 21:47:26 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 21:47:26 [Debug] Error Url: /home/<USER>
2025-07-23 21:47:26 [Debug] Error Id Parameter: errorId
2025-07-23 21:47:27 [Information] HTTP GET / responded 302 in 60.9150 ms
2025-07-23 21:47:27 [Information] HTTP GET /Account/Login responded 200 in 152.1100 ms
2025-07-23 21:47:27 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 1.7706 ms
2025-07-23 21:47:35 [Information] HTTP POST /Account/Login responded 200 in 251.0916 ms
2025-07-23 21:47:37 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 21:47:37 [Information] HTTP POST /Account/Login responded 200 in 66.7948 ms
2025-07-23 21:47:40 [Information] HTTP GET /Account/Login responded 200 in 4.7589 ms
2025-07-23 21:47:43 [Information] HTTP POST /Account/Login responded 200 in 99.2947 ms
2025-07-23 21:47:44 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 21:47:44 [Information] Contact?$filter=(No eq 'CT411887')
2025-07-23 21:47:44 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411887","Type":"Person","Organizational_Level_Code":"TRUST PHAR","Company_No":"CT031253","Company_Name":"Birmingham Womens and Children NHS Foundation Trus","Anonymise":false,"IntegrationCustomerNo":"","Name":"Test ClinicianTwo","First_Name":"Test","Middle_Name":"","Surname":"ClinicianTwo","Address":"","Address_2":"","City":"","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"TEST CLINICIANTWO","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"-","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"HUMIRA-AX","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"","Salutation_Code":"","Registration_Date":"2025-01-20","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":0,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 21:47:44 [Information] Contact?$filter=(No eq 'CT031253')
2025-07-23 21:47:44 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXdBRE1BTVFBeUFEVUFNd0FBQUFBQTExOzExMTE5MjY4MDExMDsn\"","No":"CT031253","Type":"Company","Organizational_Level_Code":"","Company_No":"CT031253","Company_Name":"Birmingham Womens and Children NHS Foundation Trus","Anonymise":false,"IntegrationCustomerNo":"","Name":"Birmingham Womens and Children NHS Foundation Trus","First_Name":"Birmingham Womens and Children","Middle_Name":"","Surname":"NHS Foundation Trust","Address":"STEELHOUSE LANE","Address_2":"","City":"","County":"BIRMINGHAM","Post_Code":"B4 6NH","Country_Region_Code":"GB","Search_Name":"BIRMINGHAM WOMENS AND CHILDREN NHS FOUNDATION TRUS","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"-","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"HUMIRA-AX","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"","Salutation_Code":"","Registration_Date":"2017-11-07","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2023-04-04","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":1,"No_of_Business_Relations":0,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 21:47:44 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 21:47:44 [Debug] Configured "Basic" authentication
2025-07-23 21:47:44 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 21:47:45 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.6323085"ms
2025-07-23 21:47:45 [Information] {"Username":"CT411887","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNEA35FBSB2A:0000000F","TimeStamp":"2025-07-23T18:47:45.3365175","ProcessId":10780,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-23 21:47:45 [Information] HTTP POST /Account/Login responded 200 in 989.9576 ms
2025-07-23 21:48:14 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 21:48:14 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 21:48:14 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 21:48:14 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 21:48:14 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 21:48:14 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 21:48:14 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 21:48:14 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 21:48:14 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 21:48:14 [Information] Seeding database...
2025-07-23 21:48:15 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 21:48:16 [Debug] Clients already populated
2025-07-23 21:48:16 [Debug] IdentityResources already populated
2025-07-23 21:48:16 [Debug] ApiScopes already populated
2025-07-23 21:48:16 [Debug] OIDC IdentityProviders already populated
2025-07-23 21:48:16 [Information] Done seeding database. Exiting.
2025-07-23 21:48:16 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 21:48:16 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 21:48:17 [Debug] Login Url: /Account/Login
2025-07-23 21:48:17 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 21:48:17 [Debug] Logout Url: /Account/Logout
2025-07-23 21:48:17 [Debug] ConsentUrl Url: /consent
2025-07-23 21:48:17 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 21:48:17 [Debug] Error Url: /home/<USER>
2025-07-23 21:48:17 [Debug] Error Id Parameter: errorId
2025-07-23 21:48:17 [Information] HTTP GET / responded 302 in 63.4556 ms
2025-07-23 21:48:17 [Information] HTTP GET /Account/Login responded 200 in 151.5726 ms
2025-07-23 21:48:17 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 1.7642 ms
2025-07-23 21:48:31 [Information] HTTP GET /Account/Login responded 200 in 51.6719 ms
2025-07-23 21:48:37 [Information] HTTP POST /Account/Login responded 200 in 257.5927 ms
2025-07-23 21:48:37 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 21:48:37 [Information] Contact?$filter=(No eq 'CT411887')
2025-07-23 21:48:38 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411887","Type":"Person","Organizational_Level_Code":"TRUST PHAR","Company_No":"CT031253","Company_Name":"Birmingham Womens and Children NHS Foundation Trus","Anonymise":false,"IntegrationCustomerNo":"","Name":"Test ClinicianTwo","First_Name":"Test","Middle_Name":"","Surname":"ClinicianTwo","Address":"","Address_2":"","City":"","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"TEST CLINICIANTWO","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"-","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"HUMIRA-AX","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"","Salutation_Code":"","Registration_Date":"2025-01-20","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":0,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 21:48:38 [Information] Contact?$filter=(No eq 'CT031253')
2025-07-23 21:48:38 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXdBRE1BTVFBeUFEVUFNd0FBQUFBQTExOzExMTE5MjY4MDExMDsn\"","No":"CT031253","Type":"Company","Organizational_Level_Code":"","Company_No":"CT031253","Company_Name":"Birmingham Womens and Children NHS Foundation Trus","Anonymise":false,"IntegrationCustomerNo":"","Name":"Birmingham Womens and Children NHS Foundation Trus","First_Name":"Birmingham Womens and Children","Middle_Name":"","Surname":"NHS Foundation Trust","Address":"STEELHOUSE LANE","Address_2":"","City":"","County":"BIRMINGHAM","Post_Code":"B4 6NH","Country_Region_Code":"GB","Search_Name":"BIRMINGHAM WOMENS AND CHILDREN NHS FOUNDATION TRUS","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"-","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"HUMIRA-AX","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"","Salutation_Code":"","Registration_Date":"2017-11-07","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2023-04-04","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":1,"No_of_Business_Relations":0,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 21:48:38 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 21:48:38 [Debug] Configured "Basic" authentication
2025-07-23 21:48:38 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 21:48:38 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.6243119"ms
2025-07-23 21:48:38 [Information] {"Username":"CT411887","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNEA35U9334A:0000000B","TimeStamp":"2025-07-23T18:48:38.8837411","ProcessId":20720,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-23 21:48:38 [Information] HTTP POST /Account/Login responded 200 in 1001.7382 ms
2025-07-23 21:49:53 [Information] HTTP GET /Account/Setup responded 200 in 24.9186 ms
2025-07-23 21:49:53 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.3794 ms
2025-07-23 21:49:55 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.8433 ms
2025-07-23 21:52:06 [Information] HTTP GET /Account/Setup responded 200 in 2.8191 ms
2025-07-23 21:52:06 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4255 ms
2025-07-23 21:52:06 [Information] HTTP GET /images/help.svg responded 200 in 2.8209 ms
2025-07-23 21:52:06 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 4.3935 ms
2025-07-23 21:52:06 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 5.7581 ms
2025-07-23 21:52:06 [Information] HTTP GET /css/site.css responded 200 in 3.2001 ms
2025-07-23 21:52:06 [Information] HTTP GET /images/help-blue.svg responded 200 in 2.7968 ms
2025-07-23 21:52:06 [Information] HTTP GET /images/logo.svg responded 200 in 0.4855 ms
2025-07-23 21:52:06 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2062 ms
2025-07-23 21:52:06 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.5775 ms
2025-07-23 21:52:06 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4297 ms
2025-07-23 21:52:06 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2912 ms
2025-07-23 21:52:06 [Information] HTTP GET /js/index.js responded 200 in 0.2880 ms
2025-07-23 21:52:06 [Information] HTTP GET /js/components/input.js responded 200 in 0.2124 ms
2025-07-23 21:52:06 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1870 ms
2025-07-23 21:52:06 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2011 ms
2025-07-23 21:52:06 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3130 ms
2025-07-23 21:52:06 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2543 ms
2025-07-23 21:52:06 [Information] HTTP GET /css/site.css.map responded 200 in 0.3243 ms
2025-07-23 21:52:06 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.6419 ms
2025-07-23 21:52:06 [Information] HTTP GET /images/step-active.png responded 200 in 0.3146 ms
2025-07-23 21:52:06 [Information] HTTP GET /images/step-next.png responded 200 in 0.2316 ms
2025-07-23 21:52:06 [Information] HTTP GET /images/login-background.png responded 200 in 1.1345 ms
2025-07-23 21:52:06 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.4629 ms
2025-07-23 21:52:06 [Information] HTTP GET /images/favicon.ico responded 200 in 0.4107 ms
2025-07-23 22:09:33 [Information] HTTP GET /Account/Setup responded 200 in 6.0671 ms
2025-07-23 22:09:33 [Information] HTTP GET /css/site.css responded 304 in 0.2397 ms
2025-07-23 22:09:33 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.1320 ms
2025-07-23 22:09:33 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0818 ms
2025-07-23 22:16:02 [Information] HTTP GET /Account/Login responded 200 in 496.6331 ms
2025-07-23 22:16:02 [Information] HTTP GET /css/site.css responded 304 in 0.1537 ms
2025-07-23 22:16:02 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.5679 ms
2025-07-23 22:16:02 [Information] HTTP GET /js/pages/login.js responded 200 in 0.4981 ms
2025-07-23 22:16:02 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.1162 ms
2025-07-23 22:16:04 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4175 ms
2025-07-23 22:16:04 [Information] HTTP GET /css/site.css.map responded 304 in 0.1491 ms
2025-07-23 22:16:06 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.8202 ms
2025-07-23 22:16:11 [Information] HTTP POST /Account/Login responded 200 in 114.0420 ms
2025-07-23 22:19:36 [Information] HTTP GET /Account/Login responded 200 in 3.8598 ms
2025-07-23 22:19:36 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3727 ms
2025-07-23 22:19:36 [Information] HTTP GET /css/site.css responded 200 in 0.7794 ms
2025-07-23 22:19:36 [Information] HTTP GET /css/site.css.map responded 200 in 0.7299 ms
2025-07-23 22:19:42 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:19:42 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-23 22:19:42 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123123","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"LWCHS - CP","Name":"CT123123","First_Name":"CT123123","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123123","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"LWCHS - CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-01","Date_Received_Documents":"2020-07-14","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-03-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:19:42 [Information] Contact?$filter=(No eq 'CT037767')
2025-07-23 22:19:42 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXdBRE1BTndBM0FEWUFOd0FBQUFBQTExOzExMTE5Mjc0MjcyMDsn\"","No":"CT037767","Type":"Company","Organizational_Level_Code":"","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"London Womens Clinic Harley Street - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"113-115 Harley Street","Address_2":"Marylebone","City":"London ","County":"","Post_Code":"W1G 6AP","Country_Region_Code":"GB","Search_Name":"LONDON WOMENS CLINIC HARLEY STREET - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2018-02-22","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2023-01-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:19:42 [Information] HTTP POST /Account/Login responded 302 in 273.3911 ms
2025-07-23 22:19:42 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-23 22:19:42 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123123","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"LWCHS - CP","Name":"CT123123","First_Name":"CT123123","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123123","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"LWCHS - CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-01","Date_Received_Documents":"2020-07-14","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-03-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:19:42 [Information] HTTP GET /Account/Setup responded 200 in 66.1478 ms
2025-07-23 22:19:42 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4368 ms
2025-07-23 22:19:42 [Information] HTTP GET /css/site.css responded 304 in 0.0978 ms
2025-07-23 22:19:42 [Information] HTTP GET /images/info-circle-warning.svg responded 200 in 0.4135 ms
2025-07-23 22:19:42 [Information] HTTP GET /images/close.svg responded 200 in 0.3780 ms
2025-07-23 22:19:42 [Information] HTTP GET /css/site.css.map responded 304 in 0.1000 ms
2025-07-23 22:19:46 [Information] HTTP GET /css/site.css.map responded 304 in 0.1002 ms
2025-07-23 22:19:46 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6875 ms
2025-07-23 22:19:46 [Information] HTTP GET /css/site.css responded 304 in 0.1041 ms
2025-07-23 22:19:46 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5317 ms
2025-07-23 22:19:49 [Information] HTTP GET /Account/Login responded 200 in 2.4406 ms
2025-07-23 22:19:50 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5786 ms
2025-07-23 22:19:50 [Information] HTTP GET /css/site.css responded 304 in 0.0797 ms
2025-07-23 22:19:50 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0334 ms
2025-07-23 22:19:50 [Information] HTTP GET /css/site.css.map responded 304 in 0.0913 ms
2025-07-23 22:20:04 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:20:04 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-23 22:20:04 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123123","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"LWCHS - CP","Name":"CT123123","First_Name":"CT123123","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123123","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"LWCHS - CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-01","Date_Received_Documents":"2020-07-14","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-03-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:20:04 [Information] Contact?$filter=(No eq 'CT037767')
2025-07-23 22:20:04 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXdBRE1BTndBM0FEWUFOd0FBQUFBQTExOzExMTE5Mjc0MjcyMDsn\"","No":"CT037767","Type":"Company","Organizational_Level_Code":"","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"London Womens Clinic Harley Street - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"113-115 Harley Street","Address_2":"Marylebone","City":"London ","County":"","Post_Code":"W1G 6AP","Country_Region_Code":"GB","Search_Name":"LONDON WOMENS CLINIC HARLEY STREET - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2018-02-22","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2023-01-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:20:04 [Information] HTTP POST /Account/Login responded 302 in 142.7200 ms
2025-07-23 22:20:04 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-23 22:20:04 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123123","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"LWCHS - CP","Name":"CT123123","First_Name":"CT123123","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123123","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"LWCHS - CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-01","Date_Received_Documents":"2020-07-14","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-03-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:20:04 [Information] HTTP GET /Account/Setup responded 200 in 65.0922 ms
2025-07-23 22:20:04 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3915 ms
2025-07-23 22:20:04 [Information] HTTP GET /css/site.css responded 304 in 0.0695 ms
2025-07-23 22:20:04 [Information] HTTP GET /css/site.css.map responded 304 in 0.0698 ms
2025-07-23 22:20:11 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:20:11 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-23 22:20:11 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123123","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"LWCHS - CP","Name":"CT123123","First_Name":"CT123123","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123123","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"LWCHS - CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-01","Date_Received_Documents":"2020-07-14","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-03-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:20:11 [Information] HTTP POST /Account/Setup responded 200 in 85.4594 ms
2025-07-23 22:20:11 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4402 ms
2025-07-23 22:20:11 [Information] HTTP GET /css/site.css responded 304 in 0.0825 ms
2025-07-23 22:20:11 [Information] HTTP GET /images/user-logo.png responded 200 in 0.3856 ms
2025-07-23 22:20:11 [Information] HTTP GET /images/shield-tick.svg responded 200 in 0.3667 ms
2025-07-23 22:20:11 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2784 ms
2025-07-23 22:20:11 [Information] HTTP GET /js/pages/create-account-birthdate-step.js responded 200 in 0.3312 ms
2025-07-23 22:20:11 [Information] HTTP GET /css/site.css.map responded 304 in 0.0846 ms
2025-07-23 22:22:23 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:22:23 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-23 22:22:24 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123123","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"LWCHS - CP","Name":"CT123123","First_Name":"CT123123","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123123","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"LWCHS - CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-01","Date_Received_Documents":"2020-07-14","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-03-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:22:24 [Information] HTTP POST /Account/Setup responded 200 in 184.3441 ms
2025-07-23 22:22:24 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3469 ms
2025-07-23 22:22:24 [Information] HTTP GET /css/site.css responded 200 in 1.0514 ms
2025-07-23 22:22:24 [Information] HTTP GET /css/site.css.map responded 200 in 0.5590 ms
2025-07-23 22:22:41 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-23 22:22:41 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123123","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"LWCHS - CP","Name":"CT123123","First_Name":"CT123123","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123123","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"LWCHS - CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-01","Date_Received_Documents":"2020-07-14","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-03-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:22:41 [Information] HTTP GET /Account/Setup/ responded 200 in 66.4111 ms
2025-07-23 22:22:41 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3107 ms
2025-07-23 22:22:41 [Information] HTTP GET /css/site.css responded 304 in 0.0563 ms
2025-07-23 22:22:41 [Information] HTTP GET /css/site.css.map responded 304 in 0.0764 ms
2025-07-23 22:22:42 [Information] HTTP GET /Account/Login responded 200 in 2.6690 ms
2025-07-23 22:22:42 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3455 ms
2025-07-23 22:23:15 [Information] HTTP GET /Account/Login responded 200 in 2.4706 ms
2025-07-23 22:23:15 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3228 ms
2025-07-23 22:23:15 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 0.4852 ms
2025-07-23 22:23:15 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.2214 ms
2025-07-23 22:23:15 [Information] HTTP GET /css/site.css responded 200 in 0.3039 ms
2025-07-23 22:23:15 [Information] HTTP GET /images/help.svg responded 200 in 0.1898 ms
2025-07-23 22:23:15 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2271 ms
2025-07-23 22:23:15 [Information] HTTP GET /images/logo.svg responded 200 in 0.3023 ms
2025-07-23 22:23:15 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2986 ms
2025-07-23 22:23:15 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2138 ms
2025-07-23 22:23:15 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.4089 ms
2025-07-23 22:23:15 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.5319 ms
2025-07-23 22:23:15 [Information] HTTP GET /css/site.css.map responded 200 in 0.5485 ms
2025-07-23 22:23:15 [Information] HTTP GET /js/index.js responded 200 in 0.2876 ms
2025-07-23 22:23:15 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.0035 ms
2025-07-23 22:23:15 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2213 ms
2025-07-23 22:23:15 [Information] HTTP GET /js/components/input.js responded 200 in 0.2063 ms
2025-07-23 22:23:15 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.3170 ms
2025-07-23 22:23:15 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2085 ms
2025-07-23 22:23:15 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2597 ms
2025-07-23 22:23:15 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1755 ms
2025-07-23 22:23:15 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2395 ms
2025-07-23 22:23:15 [Information] HTTP GET /images/login-background.png responded 200 in 1.0679 ms
2025-07-23 22:23:15 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.3700 ms
2025-07-23 22:23:15 [Information] HTTP GET /images/favicon.ico responded 200 in 0.2816 ms
2025-07-23 22:23:19 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.2589 ms
2025-07-23 22:23:28 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:23:28 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 22:23:28 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:23:28 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-23 22:23:28 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXlBREFBTVFBekFEVUFOQUFBQUFBQTExOzExMTI0MzQ1MDAxMDsn\"","No":"CT201354","Type":"Company","Organizational_Level_Code":"","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Aimovig  - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"ADDENBROOKES HOSPITAL","Address_2":"HILLS ROAD","City":"","County":"","Post_Code":"CB2 0QQ","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL AIMOVIG  - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2022-01-11","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-08-14","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:23:28 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 22:23:28 [Debug] Configured "Basic" authentication
2025-07-23 22:23:28 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 22:23:28 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.3397625"ms
2025-07-23 22:23:28 [Debug] Augmenting SignInContext
2025-07-23 22:23:28 [Debug] Adding idp claim with value: local
2025-07-23 22:23:28 [Debug] Adding amr claim with value: pwd
2025-07-23 22:23:28 [Debug] Adding auth_time claim with value: **********
2025-07-23 22:23:28 [Information] HTTP POST /Account/Login responded 302 in 568.0228 ms
2025-07-23 22:23:28 [Information] HTTP GET / responded 302 in 3.8169 ms
2025-07-23 22:23:28 [Information] HTTP GET /Account/Login responded 200 in 3.1054 ms
2025-07-23 22:23:28 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 4.4906 ms
2025-07-23 22:23:28 [Information] HTTP GET /css/site.css responded 304 in 0.0632 ms
2025-07-23 22:23:28 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0309 ms
2025-07-23 22:23:28 [Information] HTTP GET /css/site.css.map responded 304 in 0.0555 ms
2025-07-23 22:24:01 [Debug] Start authorize request protocol validation
2025-07-23 22:24:02 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:24:02 [Information] HTTP GET /Account/Login responded 200 in 126.4569 ms
2025-07-23 22:24:02 [Information] HTTP GET /css/site.css responded 304 in 0.0498 ms
2025-07-23 22:24:02 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0559 ms
2025-07-23 22:24:05 [Information] HTTP GET /images/eye.svg responded 200 in 0.4492 ms
2025-07-23 22:24:08 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:24:08 [Debug] Start authorize request protocol validation
2025-07-23 22:24:08 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:24:08 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 22:24:08 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:24:08 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-23 22:24:08 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXlBREFBTVFBekFEVUFOQUFBQUFBQTExOzExMTI0MzQ1MDAxMDsn\"","No":"CT201354","Type":"Company","Organizational_Level_Code":"","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Aimovig  - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"ADDENBROOKES HOSPITAL","Address_2":"HILLS ROAD","City":"","County":"","Post_Code":"CB2 0QQ","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL AIMOVIG  - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2022-01-11","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-08-14","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:24:08 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 22:24:08 [Debug] Configured "Basic" authentication
2025-07-23 22:24:08 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 22:24:08 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.3414420"ms
2025-07-23 22:24:08 [Information] {"Username":"CT411980","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNEA35U9334E:0000005F","TimeStamp":"2025-07-23T19:24:08.9268057","ProcessId":20720,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-23 22:24:08 [Debug] Start authorize request protocol validation
2025-07-23 22:24:08 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:24:08 [Information] HTTP POST /Account/Login responded 200 in 517.0104 ms
2025-07-23 22:24:08 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0448 ms
2025-07-23 22:24:15 [Information] HTTP POST /Account/Login responded 200 in 100.3125 ms
2025-07-23 22:24:16 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:24:16 [Debug] Start authorize request protocol validation
2025-07-23 22:24:16 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:24:16 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 22:24:16 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:24:16 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-23 22:24:16 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXlBREFBTVFBekFEVUFOQUFBQUFBQTExOzExMTI0MzQ1MDAxMDsn\"","No":"CT201354","Type":"Company","Organizational_Level_Code":"","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Aimovig  - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"ADDENBROOKES HOSPITAL","Address_2":"HILLS ROAD","City":"","County":"","Post_Code":"CB2 0QQ","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL AIMOVIG  - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2022-01-11","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-08-14","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:24:16 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 22:24:16 [Debug] Configured "Basic" authentication
2025-07-23 22:24:16 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 22:24:16 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.3263563"ms
2025-07-23 22:24:16 [Debug] Augmenting SignInContext
2025-07-23 22:24:16 [Debug] Adding idp claim with value: local
2025-07-23 22:24:16 [Debug] Adding amr claim with value: pwd
2025-07-23 22:24:16 [Debug] Adding auth_time claim with value: **********
2025-07-23 22:24:16 [Information] HTTP POST /Account/Login responded 302 in 481.7797 ms
2025-07-23 22:24:16 [Debug] Request path /connect/authorize/callback matched to endpoint type Authorize
2025-07-23 22:24:16 [Debug] Endpoint enabled: Authorize, successfully created handler: Duende.IdentityServer.Endpoints.AuthorizeCallbackEndpoint
2025-07-23 22:24:16 [Information] Invoking IdentityServer endpoint: Duende.IdentityServer.Endpoints.AuthorizeCallbackEndpoint for /connect/authorize/callback
2025-07-23 22:24:16 [Debug] Start authorize callback request
2025-07-23 22:24:16 [Debug] User in authorize request: 82fbbc38-bc49-4f86-9575-f7e14dfac56f
2025-07-23 22:24:16 [Debug] Start authorize request protocol validation
2025-07-23 22:24:16 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:24:16 [Error] Request validation failed
2025-07-23 22:24:16 [Information] {"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:24:16 [Information] {"ClientId":"web-client-app","ClientName":null,"RedirectUri":null,"Endpoint":"Authorize","SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","Scopes":"","GrantType":null,"Error":"unauthorized_client","ErrorDescription":"Unknown client or client not enabled","Category":"Token","Name":"Token Issued Failure","EventType":"Failure","Id":2001,"Message":null,"ActivityId":"0HNEA35U9334E:00000067","TimeStamp":"2025-07-23T19:24:16.6081845","ProcessId":20720,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"TokenIssuedFailureEvent"}
2025-07-23 22:24:16 [Information] HTTP GET /connect/authorize/callback responded 302 in 53.7779 ms
2025-07-23 22:24:16 [Information] HTTP GET /home/<USER>
2025-07-23 22:24:16 [Information] HTTP GET /css/site.css responded 304 in 0.0681 ms
2025-07-23 22:24:35 [Debug] Start authorize request protocol validation
2025-07-23 22:24:35 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:24:35 [Information] HTTP GET /Account/Login responded 200 in 16.3325 ms
2025-07-23 22:24:37 [Debug] Start authorize request protocol validation
2025-07-23 22:24:37 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:24:37 [Information] HTTP GET /Account/Login responded 200 in 16.0762 ms
2025-07-23 22:24:37 [Information] HTTP GET /css/site.css responded 304 in 0.0643 ms
2025-07-23 22:24:37 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0278 ms
2025-07-23 22:24:58 [Information] HTTP GET /Account/Setup responded 200 in 1.5035 ms
2025-07-23 22:24:58 [Information] HTTP GET /css/site.css responded 304 in 0.0622 ms
2025-07-23 22:24:58 [Information] HTTP GET /images/step-active.png responded 200 in 0.3093 ms
2025-07-23 22:24:58 [Information] HTTP GET /images/step-next.png responded 200 in 0.2905 ms
2025-07-23 22:25:03 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:25:03 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-23 22:25:03 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123123","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"LWCHS - CP","Name":"CT123123","First_Name":"CT123123","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123123","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"LWCHS - CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-01","Date_Received_Documents":"2020-07-14","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-03-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:25:03 [Information] HTTP POST /Account/Setup responded 200 in 190.7015 ms
2025-07-23 22:25:03 [Information] HTTP GET /images/user-logo.png responded 200 in 0.2240 ms
2025-07-23 22:25:03 [Information] HTTP GET /images/shield-tick.svg responded 200 in 0.2546 ms
2025-07-23 22:25:03 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.3178 ms
2025-07-23 22:25:03 [Information] HTTP GET /js/pages/create-account-birthdate-step.js responded 200 in 0.5142 ms
2025-07-23 22:25:09 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:25:09 [Information] Contact?$filter=(No eq 'CT123123' and DOB eq 1998-12-12 and Type eq 'Person' and Status eq 'Active')
2025-07-23 22:25:10 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[]}
2025-07-23 22:25:10 [Information] HTTP POST /Account/Setup responded 200 in 114.3094 ms
2025-07-23 22:25:22 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-23 22:25:22 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123123","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"LWCHS - CP","Name":"CT123123","First_Name":"CT123123","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123123","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"LWCHS - CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-01","Date_Received_Documents":"2020-07-14","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-03-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:25:22 [Information] HTTP GET /Account/Setup/ responded 200 in 66.8325 ms
2025-07-23 22:25:22 [Information] HTTP GET /css/site.css responded 304 in 0.0599 ms
2025-07-23 22:25:22 [Information] HTTP GET /images/info-circle-warning.svg responded 200 in 0.2904 ms
2025-07-23 22:25:22 [Information] HTTP GET /images/close.svg responded 200 in 0.3410 ms
2025-07-23 22:25:28 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:25:28 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 22:25:28 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:25:28 [Information] HTTP POST /Account/Setup responded 200 in 97.7848 ms
2025-07-23 22:25:28 [Information] HTTP GET /images/email-logo.png responded 200 in 0.3434 ms
2025-07-23 22:25:38 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 22:25:38 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:25:38 [Information] HTTP GET /Account/Setup responded 200 in 67.0465 ms
2025-07-23 22:25:40 [Debug] Start authorize request protocol validation
2025-07-23 22:25:40 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:25:40 [Information] HTTP GET /Account/Login responded 200 in 19.3291 ms
2025-07-23 22:25:40 [Information] HTTP GET /css/site.css responded 304 in 0.0675 ms
2025-07-23 22:25:40 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0338 ms
2025-07-23 22:25:41 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 8.2996 ms
2025-07-23 22:25:41 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.3455 ms
2025-07-23 22:25:50 [Debug] Start authorize request protocol validation
2025-07-23 22:25:50 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:25:50 [Information] HTTP GET /Account/Login responded 200 in 17.3935 ms
2025-07-23 22:25:51 [Information] HTTP GET /Account/Setup responded 200 in 1.2801 ms
2025-07-23 22:27:40 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 22:27:40 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 22:27:40 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 22:27:40 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 22:27:40 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 22:27:40 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 22:27:40 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 22:27:40 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 22:27:40 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 22:27:40 [Information] Seeding database...
2025-07-23 22:27:41 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 22:27:42 [Debug] Clients already populated
2025-07-23 22:27:42 [Debug] IdentityResources already populated
2025-07-23 22:27:42 [Debug] ApiScopes already populated
2025-07-23 22:27:42 [Debug] OIDC IdentityProviders already populated
2025-07-23 22:27:42 [Information] Done seeding database. Exiting.
2025-07-23 22:27:42 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 22:27:42 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 22:27:43 [Debug] Login Url: /Account/Login
2025-07-23 22:27:43 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 22:27:43 [Debug] Logout Url: /Account/Logout
2025-07-23 22:27:43 [Debug] ConsentUrl Url: /consent
2025-07-23 22:27:43 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 22:27:43 [Debug] Error Url: /home/<USER>
2025-07-23 22:27:43 [Debug] Error Id Parameter: errorId
2025-07-23 22:27:43 [Information] HTTP GET / responded 302 in 60.9338 ms
2025-07-23 22:27:43 [Information] HTTP GET /Account/Login responded 200 in 145.6094 ms
2025-07-23 22:27:43 [Information] HTTP GET /css/site.css responded 304 in 2.6846 ms
2025-07-23 22:27:43 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.2048 ms
2025-07-23 22:27:43 [Information] HTTP GET /js/pages/login.js responded 304 in 0.1493 ms
2025-07-23 22:29:52 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 22:29:52 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 22:29:52 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 22:29:52 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 22:29:52 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 22:29:52 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 22:29:52 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 22:29:52 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 22:29:52 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 22:29:52 [Information] Seeding database...
2025-07-23 22:29:53 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 22:29:54 [Debug] Clients already populated
2025-07-23 22:29:54 [Debug] IdentityResources already populated
2025-07-23 22:29:54 [Debug] ApiScopes already populated
2025-07-23 22:29:54 [Debug] OIDC IdentityProviders already populated
2025-07-23 22:29:54 [Information] Done seeding database. Exiting.
2025-07-23 22:29:54 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 22:29:54 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 22:29:55 [Debug] Login Url: /Account/Login
2025-07-23 22:29:55 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 22:29:55 [Debug] Logout Url: /Account/Logout
2025-07-23 22:29:55 [Debug] ConsentUrl Url: /consent
2025-07-23 22:29:55 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 22:29:55 [Debug] Error Url: /home/<USER>
2025-07-23 22:29:55 [Debug] Error Id Parameter: errorId
2025-07-23 22:29:55 [Information] HTTP GET / responded 302 in 66.8001 ms
2025-07-23 22:29:55 [Information] HTTP GET /Account/Login responded 200 in 168.7562 ms
2025-07-23 22:29:55 [Information] HTTP GET /css/site.css responded 304 in 2.4532 ms
2025-07-23 22:29:55 [Information] HTTP GET /js/pages/login.js responded 304 in 0.3588 ms
2025-07-23 22:36:42 [Information] HTTP GET /Account/Setup responded 200 in 42.6847 ms
2025-07-23 22:36:42 [Information] HTTP GET /css/site.css responded 304 in 0.1658 ms
2025-07-23 22:36:42 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.1743 ms
2025-07-23 22:36:48 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:36:48 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 22:36:48 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:36:48 [Information] HTTP POST /Account/Setup responded 200 in 359.7869 ms
2025-07-23 22:37:05 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:37:05 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 22:37:05 [Debug] Configured "Basic" authentication
2025-07-23 22:37:05 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 22:37:06 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.6371167"ms
2025-07-23 22:37:06 [Information] HTTP POST /Account/Setup responded 200 in 662.8888 ms
2025-07-23 22:37:12 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 22:37:12 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:37:12 [Information] HTTP GET /Account/Setup responded 200 in 90.2065 ms
2025-07-23 22:41:17 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 22:41:17 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 22:41:17 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 22:41:17 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 22:41:17 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 22:41:17 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 22:41:17 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 22:41:17 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 22:41:17 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 22:41:17 [Information] Seeding database...
2025-07-23 22:41:18 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 22:41:18 [Debug] Clients already populated
2025-07-23 22:41:18 [Debug] IdentityResources already populated
2025-07-23 22:41:18 [Debug] ApiScopes already populated
2025-07-23 22:41:18 [Debug] OIDC IdentityProviders already populated
2025-07-23 22:41:18 [Information] Done seeding database. Exiting.
2025-07-23 22:41:19 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 22:41:19 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 22:41:20 [Debug] Login Url: /Account/Login
2025-07-23 22:41:20 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 22:41:20 [Debug] Logout Url: /Account/Logout
2025-07-23 22:41:20 [Debug] ConsentUrl Url: /consent
2025-07-23 22:41:20 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 22:41:20 [Debug] Error Url: /home/<USER>
2025-07-23 22:41:20 [Debug] Error Id Parameter: errorId
2025-07-23 22:41:20 [Information] HTTP GET / responded 302 in 70.7050 ms
2025-07-23 22:41:20 [Information] HTTP GET /Account/Login responded 200 in 159.9661 ms
2025-07-23 22:41:20 [Information] HTTP GET /css/site.css responded 304 in 2.6317 ms
2025-07-23 22:41:20 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.3186 ms
2025-07-23 22:41:20 [Information] HTTP GET /js/pages/login.js responded 304 in 0.2492 ms
2025-07-23 22:41:31 [Debug] Start authorize request protocol validation
2025-07-23 22:41:31 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:41:31 [Information] HTTP GET /Account/Login responded 200 in 198.1406 ms
2025-07-23 22:41:35 [Debug] Start authorize request protocol validation
2025-07-23 22:41:35 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:41:35 [Information] HTTP GET /Account/Login responded 200 in 28.6646 ms
2025-07-23 22:41:35 [Information] HTTP GET /images/help.svg responded 200 in 2.0819 ms
2025-07-23 22:41:35 [Information] HTTP GET /images/help-blue.svg responded 200 in 2.0238 ms
2025-07-23 22:41:35 [Information] HTTP GET /css/site.css responded 200 in 4.3869 ms
2025-07-23 22:41:35 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 4.4485 ms
2025-07-23 22:41:35 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 4.5553 ms
2025-07-23 22:41:35 [Information] HTTP GET /images/logo.svg responded 200 in 0.5631 ms
2025-07-23 22:41:35 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.2811 ms
2025-07-23 22:41:35 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2896 ms
2025-07-23 22:41:35 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.5557 ms
2025-07-23 22:41:35 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.4963 ms
2025-07-23 22:41:35 [Information] HTTP GET /js/index.js responded 200 in 0.2270 ms
2025-07-23 22:41:35 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2375 ms
2025-07-23 22:41:35 [Information] HTTP GET /js/components/input.js responded 200 in 0.1862 ms
2025-07-23 22:41:35 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2188 ms
2025-07-23 22:41:35 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.2762 ms
2025-07-23 22:41:35 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2304 ms
2025-07-23 22:41:35 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1987 ms
2025-07-23 22:41:35 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2284 ms
2025-07-23 22:41:35 [Information] HTTP GET /images/login-background.png responded 200 in 0.9675 ms
2025-07-23 22:41:35 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3532 ms
2025-07-23 22:41:38 [Information] HTTP GET /Account/ForgotPassword responded 200 in 19.7184 ms
2025-07-23 22:41:38 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.7791 ms
2025-07-23 22:41:38 [Information] HTTP GET /images/password-logo.png responded 200 in 0.7783 ms
2025-07-23 22:41:38 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.8178 ms
2025-07-23 22:41:47 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.5991 ms
2025-07-23 22:41:59 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:41:59 [Information] HTTP POST /Account/ForgotPassword responded 200 in 21.6985 ms
2025-07-23 22:42:12 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:42:12 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 22:42:12 [Debug] Configured "Basic" authentication
2025-07-23 22:42:12 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 22:42:13 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.6591371"ms
2025-07-23 22:42:13 [Information] HTTP POST /Account/ForgotPassword responded 200 in 701.4352 ms
2025-07-23 22:42:23 [Information] HTTP GET /Account/ForgotPassword responded 200 in 3.8990 ms
2025-07-23 22:42:31 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:42:31 [Information] HTTP POST /Account/ForgotPassword responded 200 in 2.7285 ms
2025-07-23 22:42:33 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.8055 ms
2025-07-23 22:42:33 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 0.9895 ms
2025-07-23 22:42:33 [Information] HTTP GET /css/site.css.map responded 200 in 0.2257 ms
2025-07-23 22:42:33 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.7297 ms
2025-07-23 22:42:38 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:42:38 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 22:42:38 [Debug] Configured "Basic" authentication
2025-07-23 22:42:38 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 22:42:38 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3566133"ms
2025-07-23 22:42:38 [Information] HTTP POST /Account/ForgotPassword responded 200 in 363.2318 ms
2025-07-23 22:42:38 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4565 ms
2025-07-23 22:42:52 [Information] HTTP GET /Account/ForgotPassword responded 200 in 2.5286 ms
2025-07-23 22:42:52 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4697 ms
2025-07-23 22:42:54 [Debug] Start authorize request protocol validation
2025-07-23 22:42:54 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:42:54 [Information] HTTP GET /Account/Login responded 200 in 19.7748 ms
2025-07-23 22:42:54 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5718 ms
2025-07-23 22:42:56 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 8.4337 ms
2025-07-23 22:42:56 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3531 ms
2025-07-23 22:42:56 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.4711 ms
2025-07-23 22:42:56 [Information] HTTP GET /images/email-logo.png responded 200 in 0.9020 ms
2025-07-23 22:42:59 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:42:59 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-23 22:42:59 [Debug] Configured "Basic" authentication
2025-07-23 22:42:59 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 22:42:59 [Error] No CT Number has been found with this email address
2025-07-23 22:42:59 [Error] Failed to execute WebUtilitiesProxy.GetContactNoFromEmailAsync
System.Exception: WebServiceIsNotConnected: BadGateway
 ---> System.ServiceModel.FaultException: No CT Number has been found with this email address
   at System.ServiceModel.Channels.ServiceChannel.HandleReply(ProxyOperationRuntime operation, ProxyRpc& rpc)
   at System.ServiceModel.Channels.ServiceChannel.EndCall(String action, Object[] outs, IAsyncResult result)
   at System.ServiceModel.Channels.ServiceChannelProxy.TaskCreator.<>c__DisplayClass1_0.<CreateGenericTask>b__0(IAsyncResult asyncResult)
--- End of stack trace from previous location ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Features.WebUtilities.WebUtilitiesProxy.<>c__DisplayClass22_0.<<GetContactNoFromEmailAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.<WcfProxyBase>F273E8D520A9DEE9429D6E7AEA7C9D0833863FB601BE141AAD21C77977E1DE2BA__Extensions.<>c__DisplayClass0_0`1.<<ExecuteWithPolicyAsync>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.Fallback.AsyncFallbackEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, Func`3 onFallbackAsync, Func`4 fallbackAction, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.WcfProxyBase`2.<>c__DisplayClass6_0`1.<CreatePolicy>b__4(DelegateResult`1 result)
   at Polly.AsyncFallbackTResultSyntax.<>c__DisplayClass3_0`1.<FallbackAsync>b__1(DelegateResult`1 outcome, Context _)
   at Polly.Fallback.AsyncFallbackEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldHandleExceptionPredicates, ResultPredicates`1 shouldHandleResultPredicates, Func`3 onFallbackAsync, Func`4 fallbackAction, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at Polly.AsyncPolicy`1.ExecuteInternalAsync(Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.<WcfProxyBase>F273E8D520A9DEE9429D6E7AEA7C9D0833863FB601BE141AAD21C77977E1DE2BA__Extensions.ExecuteWithPolicyAsync[T](IAsyncPolicy`1 policy, Func`1 operation, String operationKey, CancellationToken cancellationToken)
   at HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies.Common.WcfProxyBase`2.ExecuteServiceCallAsync[TResult](Func`2 operation, String operationName, CancellationToken cancellationToken)
2025-07-23 22:42:59 [Information] HTTP POST /Account/ForgotCTNumber responded 200 in 443.0369 ms
2025-07-23 22:42:59 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5702 ms
2025-07-23 22:43:07 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:43:07 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-23 22:43:07 [Debug] Configured "Basic" authentication
2025-07-23 22:43:07 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 22:43:08 [Information] Successfully executed WebUtilitiesProxy.GetContactNoFromEmailAsync in "00:00:00.5352998"ms
2025-07-23 22:43:08 [Information] Contact?$filter=(((No eq 'CT411980')))
2025-07-23 22:43:08 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:43:09 [Information] HTTP POST /Account/ForgotCTNumber responded 302 in 1835.0589 ms
2025-07-23 22:43:09 [Information] HTTP GET /Account/CTNumberSentSuccess responded 200 in 7.4330 ms
2025-07-23 22:43:09 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4146 ms
2025-07-23 22:43:15 [Debug] Start authorize request protocol validation
2025-07-23 22:43:15 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:43:15 [Information] HTTP GET /Account/Login responded 200 in 20.1622 ms
2025-07-23 22:43:15 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3711 ms
2025-07-23 22:43:46 [Information] HTTP POST /Account/Login responded 200 in 220.5653 ms
2025-07-23 22:43:46 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:43:46 [Debug] Start authorize request protocol validation
2025-07-23 22:43:46 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:43:46 [Information] Contact?$filter=(No eq 'CT411887')
2025-07-23 22:43:47 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411887","Type":"Person","Organizational_Level_Code":"TRUST PHAR","Company_No":"CT031253","Company_Name":"Birmingham Womens and Children NHS Foundation Trus","Anonymise":false,"IntegrationCustomerNo":"","Name":"Test ClinicianTwo","First_Name":"Test","Middle_Name":"","Surname":"ClinicianTwo","Address":"","Address_2":"","City":"","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"TEST CLINICIANTWO","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"-","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"HUMIRA-AX","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"","Salutation_Code":"","Registration_Date":"2025-01-20","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":0,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:43:47 [Information] Contact?$filter=(No eq 'CT031253')
2025-07-23 22:43:47 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXdBRE1BTVFBeUFEVUFNd0FBQUFBQTExOzExMTE5MjY4MDExMDsn\"","No":"CT031253","Type":"Company","Organizational_Level_Code":"","Company_No":"CT031253","Company_Name":"Birmingham Womens and Children NHS Foundation Trus","Anonymise":false,"IntegrationCustomerNo":"","Name":"Birmingham Womens and Children NHS Foundation Trus","First_Name":"Birmingham Womens and Children","Middle_Name":"","Surname":"NHS Foundation Trust","Address":"STEELHOUSE LANE","Address_2":"","City":"","County":"BIRMINGHAM","Post_Code":"B4 6NH","Country_Region_Code":"GB","Search_Name":"BIRMINGHAM WOMENS AND CHILDREN NHS FOUNDATION TRUS","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"-","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"HUMIRA-AX","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"","Salutation_Code":"","Registration_Date":"2017-11-07","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2023-04-04","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":1,"No_of_Business_Relations":0,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:43:47 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 22:43:47 [Debug] Configured "Basic" authentication
2025-07-23 22:43:47 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 22:43:47 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.3331311"ms
2025-07-23 22:43:47 [Information] {"Username":"CT411887","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNEA43ISAPDG:00000069","TimeStamp":"2025-07-23T19:43:47.4913711","ProcessId":35480,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-23 22:43:47 [Debug] Start authorize request protocol validation
2025-07-23 22:43:47 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:43:47 [Information] HTTP POST /Account/Login responded 200 in 646.0974 ms
2025-07-23 22:43:47 [Information] HTTP GET /css/site.css responded 304 in 0.1116 ms
2025-07-23 22:43:47 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0486 ms
2025-07-23 22:43:47 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5631 ms
2025-07-23 22:43:58 [Information] HTTP GET /Account/Setup responded 200 in 21.8575 ms
2025-07-23 22:43:58 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5184 ms
2025-07-23 22:43:58 [Information] HTTP GET /images/step-next.png responded 200 in 0.6624 ms
2025-07-23 22:43:58 [Information] HTTP GET /images/step-active.png responded 200 in 0.6974 ms
2025-07-23 22:44:00 [Debug] Start authorize request protocol validation
2025-07-23 22:44:00 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"anonymous","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 22:44:00 [Information] HTTP GET /Account/Login responded 200 in 20.3305 ms
2025-07-23 22:44:00 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4272 ms
2025-07-23 22:44:03 [Information] HTTP GET /Account/Setup responded 200 in 2.3482 ms
2025-07-23 22:44:03 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4977 ms
2025-07-23 22:44:06 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:44:06 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 22:44:06 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:44:06 [Information] HTTP POST /Account/Setup responded 200 in 84.9576 ms
2025-07-23 22:44:06 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3790 ms
2025-07-23 22:44:17 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:44:17 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 22:44:17 [Debug] Configured "Basic" authentication
2025-07-23 22:44:17 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 22:44:17 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3421670"ms
2025-07-23 22:44:17 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 22:44:17 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-06-11","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 22:44:18 [Information] Executing WebUtilitiesProxy.UpdatePortalOptInAsync
2025-07-23 22:44:18 [Debug] Configured "Basic" authentication
2025-07-23 22:44:18 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 22:44:18 [Information] Successfully executed WebUtilitiesProxy.UpdatePortalOptInAsync in "00:00:00.4148079"ms
2025-07-23 22:44:18 [Information] HTTP POST /Account/Setup responded 200 in 1732.5266 ms
2025-07-23 22:44:18 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.3605 ms
2025-07-23 22:44:34 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /images/logo.svg from origin: https://outlook.office.com because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:44:34 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /images/email-illustration.png from origin: https://outlook.office.com because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 22:44:34 [Information] HTTP OPTIONS /images/logo.svg responded 404 in 1.5508 ms
2025-07-23 22:44:34 [Information] HTTP OPTIONS /images/email-illustration.png responded 404 in 1.8169 ms
2025-07-23 22:44:34 [Information] HTTP GET /images/logo.svg responded 200 in 0.5066 ms
2025-07-23 22:44:34 [Information] HTTP GET /images/email-illustration.png responded 200 in 0.3580 ms
2025-07-23 22:44:40 [Information] HTTP GET /Account/Setup responded 200 in 39.7473 ms
2025-07-23 22:44:41 [Information] HTTP GET /css/site.css responded 304 in 0.0877 ms
2025-07-23 22:44:41 [Information] HTTP GET /images/password-logo.png responded 200 in 0.4242 ms
2025-07-23 22:44:41 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0695 ms
2025-07-23 22:44:41 [Information] HTTP GET /images/step-check.svg responded 200 in 0.2441 ms
2025-07-23 22:44:41 [Information] HTTP GET /images/check.svg responded 200 in 0.1555 ms
2025-07-23 22:45:04 [Information] HTTP GET /images/check-green.svg responded 200 in 0.7268 ms
2025-07-23 23:07:54 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 23:07:54 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 23:07:54 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 23:07:54 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 23:07:54 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 23:07:54 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 23:07:54 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 23:07:54 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 23:07:54 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 23:07:54 [Information] Seeding database...
2025-07-23 23:07:55 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 23:07:55 [Debug] Clients already populated
2025-07-23 23:07:55 [Debug] IdentityResources already populated
2025-07-23 23:07:55 [Debug] ApiScopes already populated
2025-07-23 23:07:55 [Debug] OIDC IdentityProviders already populated
2025-07-23 23:07:55 [Information] Done seeding database. Exiting.
2025-07-23 23:07:56 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 23:07:56 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 23:07:56 [Debug] Login Url: /Account/Login
2025-07-23 23:07:56 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 23:07:56 [Debug] Logout Url: /Account/Logout
2025-07-23 23:07:56 [Debug] ConsentUrl Url: /consent
2025-07-23 23:07:56 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 23:07:56 [Debug] Error Url: /home/<USER>
2025-07-23 23:07:56 [Debug] Error Id Parameter: errorId
2025-07-23 23:07:56 [Information] HTTP GET / responded 302 in 97.9009 ms
2025-07-23 23:07:56 [Debug] SignOutCalled set; processing post-signout session cleanup.
2025-07-23 23:07:57 [Information] HTTP GET /Account/Login responded 200 in 149.3394 ms
2025-07-23 23:07:57 [Information] HTTP GET /css/site.css responded 304 in 2.0362 ms
2025-07-23 23:07:57 [Information] HTTP GET /js/components/utils.js responded 200 in 4.2251 ms
2025-07-23 23:07:57 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.1070 ms
2025-07-23 23:07:57 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0790 ms
2025-07-23 23:07:57 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0689 ms
2025-07-23 23:08:02 [Information] HTTP GET /Account/Login responded 200 in 54.0001 ms
2025-07-23 23:08:05 [Information] HTTP GET /Account/ForgotPassword responded 200 in 23.0557 ms
2025-07-23 23:08:05 [Information] HTTP GET /js/components/utils.js responded 304 in 0.1434 ms
2025-07-23 23:08:05 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.4415 ms
2025-07-23 23:08:11 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:08:11 [Information] HTTP POST /Account/ForgotPassword responded 200 in 21.2832 ms
2025-07-23 23:08:11 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.1508 ms
2025-07-23 23:08:18 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:08:18 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 23:08:18 [Debug] Configured "Basic" authentication
2025-07-23 23:08:18 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 23:08:18 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.6595109"ms
2025-07-23 23:08:18 [Information] HTTP POST /Account/ForgotPassword responded 200 in 705.7244 ms
2025-07-23 23:08:18 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0978 ms
2025-07-23 23:08:18 [Information] HTTP GET /js/pages/forgot-password.js responded 304 in 0.0629 ms
2025-07-23 23:08:20 [Information] HTTP GET /Account/ForgotPassword responded 200 in 2.1446 ms
2025-07-23 23:08:21 [Information] HTTP GET /Account/Login responded 200 in 7.8605 ms
2025-07-23 23:08:22 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 9.2560 ms
2025-07-23 23:08:22 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 2.0544 ms
2025-07-23 23:08:24 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:08:24 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-23 23:08:24 [Debug] Configured "Basic" authentication
2025-07-23 23:08:24 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 23:08:24 [Information] Successfully executed WebUtilitiesProxy.GetContactNoFromEmailAsync in "00:00:00.5006988"ms
2025-07-23 23:08:24 [Information] Contact?$filter=(((No eq 'CT411980')))
2025-07-23 23:08:24 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:08:25 [Information] HTTP POST /Account/ForgotCTNumber responded 302 in 1754.1307 ms
2025-07-23 23:08:25 [Information] HTTP GET /Account/CTNumberSentSuccess responded 200 in 6.9864 ms
2025-07-23 23:08:27 [Information] HTTP GET /Account/Login responded 200 in 3.1480 ms
2025-07-23 23:08:27 [Information] HTTP GET /js/components/utils.js responded 304 in 0.1427 ms
2025-07-23 23:08:35 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:08:35 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 23:08:35 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:08:35 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-23 23:08:35 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXlBREFBTVFBekFEVUFOQUFBQUFBQTExOzExMTI0MzQ1MDAxMDsn\"","No":"CT201354","Type":"Company","Organizational_Level_Code":"","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Aimovig  - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"ADDENBROOKES HOSPITAL","Address_2":"HILLS ROAD","City":"","County":"","Post_Code":"CB2 0QQ","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL AIMOVIG  - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2022-01-11","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-08-14","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:08:35 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 23:08:35 [Debug] Configured "Basic" authentication
2025-07-23 23:08:35 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 23:08:35 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.3398136"ms
2025-07-23 23:08:35 [Debug] Augmenting SignInContext
2025-07-23 23:08:35 [Debug] Adding idp claim with value: local
2025-07-23 23:08:35 [Debug] Adding amr claim with value: pwd
2025-07-23 23:08:35 [Debug] Adding auth_time claim with value: **********
2025-07-23 23:08:35 [Information] HTTP POST /Account/Login responded 302 in 641.7222 ms
2025-07-23 23:08:35 [Information] HTTP GET / responded 302 in 2.2395 ms
2025-07-23 23:08:35 [Information] HTTP GET /Account/Login responded 200 in 5.7678 ms
2025-07-23 23:09:03 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /images/email-illustration.png from origin: https://outlook.office.com because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:09:03 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /images/logo.svg from origin: https://outlook.office.com because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:09:03 [Information] HTTP OPTIONS /images/email-illustration.png responded 404 in 2.2586 ms
2025-07-23 23:09:03 [Information] HTTP OPTIONS /images/logo.svg responded 404 in 2.4375 ms
2025-07-23 23:09:07 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 1.0025 ms
2025-07-23 23:09:07 [Information] HTTP GET /css/site.css.map responded 304 in 0.1085 ms
2025-07-23 23:09:08 [Information] HTTP GET /Account/Login responded 200 in 3.7693 ms
2025-07-23 23:09:08 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.8128 ms
2025-07-23 23:09:08 [Information] HTTP GET /images/help.svg responded 200 in 0.3855 ms
2025-07-23 23:09:08 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.6888 ms
2025-07-23 23:09:08 [Information] HTTP GET /css/site.css responded 200 in 0.5084 ms
2025-07-23 23:09:08 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.3613 ms
2025-07-23 23:09:08 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 2.5203 ms
2025-07-23 23:09:08 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.4566 ms
2025-07-23 23:09:08 [Information] HTTP GET /images/logo.svg responded 200 in 0.6836 ms
2025-07-23 23:09:08 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.3114 ms
2025-07-23 23:09:08 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.7785 ms
2025-07-23 23:09:08 [Information] HTTP GET /css/site.css.map responded 200 in 0.3295 ms
2025-07-23 23:09:08 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.6835 ms
2025-07-23 23:09:08 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.8582 ms
2025-07-23 23:09:08 [Information] HTTP GET /js/index.js responded 200 in 0.3322 ms
2025-07-23 23:09:08 [Information] HTTP GET /js/components/utils.js responded 200 in 0.4551 ms
2025-07-23 23:09:08 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3703 ms
2025-07-23 23:09:08 [Information] HTTP GET /js/components/input.js responded 200 in 0.2858 ms
2025-07-23 23:09:08 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.3557 ms
2025-07-23 23:09:08 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.4035 ms
2025-07-23 23:09:08 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.3283 ms
2025-07-23 23:09:08 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.1823 ms
2025-07-23 23:09:08 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2775 ms
2025-07-23 23:09:08 [Information] HTTP GET /images/login-background.png responded 200 in 1.2702 ms
2025-07-23 23:09:08 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 2.5435 ms
2025-07-23 23:09:08 [Information] HTTP GET /images/favicon.ico responded 200 in 0.5204 ms
2025-07-23 23:09:09 [Information] HTTP GET /Account/Setup responded 200 in 23.8092 ms
2025-07-23 23:09:09 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6201 ms
2025-07-23 23:09:09 [Information] HTTP GET /images/step-active.png responded 200 in 0.4329 ms
2025-07-23 23:09:09 [Information] HTTP GET /images/step-next.png responded 200 in 0.4295 ms
2025-07-23 23:09:14 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:09:14 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-23 23:09:14 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123123","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"LWCHS - CP","Name":"CT123123","First_Name":"CT123123","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123123","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"LWCHS - CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-01","Date_Received_Documents":"2020-07-14","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-03-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:09:14 [Information] HTTP POST /Account/Setup responded 200 in 95.6304 ms
2025-07-23 23:09:14 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6796 ms
2025-07-23 23:09:14 [Information] HTTP GET /images/user-logo.png responded 200 in 0.2801 ms
2025-07-23 23:09:14 [Information] HTTP GET /images/shield-tick.svg responded 200 in 0.3068 ms
2025-07-23 23:09:14 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.2676 ms
2025-07-23 23:09:14 [Information] HTTP GET /js/pages/create-account-birthdate-step.js responded 200 in 0.3730 ms
2025-07-23 23:09:15 [Information] Contact?$filter=(No eq 'CT123123')
2025-07-23 23:09:15 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT123123","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT037767","Company_Name":"London Womens Clinic Harley Street - CP","Anonymise":false,"IntegrationCustomerNo":"LWCHS - CP","Name":"CT123123","First_Name":"CT123123","Middle_Name":"","Surname":"","Address":"Units 1 & 2 Orbit Business Park","Address_2":"Alfred Eley Close","City":"Swadlincote","County":"","Post_Code":"DE11 0WU","Country_Region_Code":"GB","Search_Name":"CT123123","Gender":"Not Defined","DOB":"1900-01-01","GMC_No":"","NHS_No":"*********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"","Group_Consortium_Name":"","Wholesaler_Trust":"","Wholesaler_Trust_Name":"","Hospital":"","Hospital_Name":"","Hospital_No":"","Bill_to_Customer_No":"LWCHS - CP","Therapy":"FERTILITY","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"IVF","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"***********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"STORK","Salutation_Code":"","Registration_Date":"2020-07-01","Date_Received_Documents":"2020-07-14","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-03-26","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":"Unknown","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Unknown","Send_Blood_Test_Reminder":false,"Nursing_Service":"No","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:09:15 [Information] HTTP GET /Account/Setup/ responded 200 in 73.1306 ms
2025-07-23 23:09:15 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5333 ms
2025-07-23 23:09:15 [Information] HTTP GET /images/info-circle-warning.svg responded 200 in 0.5239 ms
2025-07-23 23:09:15 [Information] HTTP GET /images/close.svg responded 200 in 0.2743 ms
2025-07-23 23:09:22 [Debug] Start authorize request protocol validation
2025-07-23 23:09:22 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 23:09:22 [Information] HTTP GET /Account/Login responded 200 in 133.0550 ms
2025-07-23 23:09:22 [Information] HTTP GET /js/components/utils.js responded 304 in 0.1060 ms
2025-07-23 23:09:24 [Information] HTTP GET /Account/ForgotPassword responded 200 in 1.4586 ms
2025-07-23 23:09:24 [Information] HTTP GET /images/password-logo.png responded 200 in 0.3588 ms
2025-07-23 23:09:24 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.2025 ms
2025-07-23 23:09:31 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:09:31 [Information] HTTP POST /Account/ForgotPassword responded 200 in 3.3186 ms
2025-07-23 23:09:36 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:09:36 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 23:09:36 [Debug] Configured "Basic" authentication
2025-07-23 23:09:36 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 23:09:36 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3596764"ms
2025-07-23 23:09:36 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 23:09:36 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:09:37 [Information] HTTP POST /Account/ForgotPassword responded 302 in 1262.6033 ms
2025-07-23 23:09:37 [Information] HTTP GET /Account/ResetLinkSentSuccess responded 200 in 7.5751 ms
2025-07-23 23:09:37 [Information] HTTP GET /images/email-logo.png responded 200 in 0.2382 ms
2025-07-23 23:09:37 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0402 ms
2025-07-23 23:19:41 [Information] Starting Duende IdentityServer version 7.2.4+dcf95c080fbe638949afbab48914f13284bd9969 (.NET 8.0.11)
2025-07-23 23:19:41 [Warning] You do not have a valid license key for the Duende software. This is allowed for development and testing scenarios. If you are running in production you are required to have a licensed version. Please start a conversation with us: https://duendesoftware.com/contact
2025-07-23 23:19:41 [Warning] You have automatic key management enabled, but you do not have a license. This feature requires the Business or Enterprise Edition tier of license. Alternatively you can disable automatic key management by setting the KeyManagement.Enabled property to false on the IdentityServerOptions.
2025-07-23 23:19:41 [Information] Using the default authentication scheme Identity.Application for IdentityServer
2025-07-23 23:19:41 [Debug] Using Identity.Application as default ASP.NET Core scheme for authentication
2025-07-23 23:19:41 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-in
2025-07-23 23:19:41 [Debug] Using Identity.External as default ASP.NET Core scheme for sign-out
2025-07-23 23:19:41 [Debug] Using Identity.Application as default ASP.NET Core scheme for challenge
2025-07-23 23:19:41 [Debug] Using Identity.Application as default ASP.NET Core scheme for forbid
2025-07-23 23:19:41 [Information] Seeding database...
2025-07-23 23:19:42 [Warning] The 'MfaType' property 'MfaType' on entity type 'UserSecuritySettings' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value 'Unknown', since this is the CLR default for the 'MfaType' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-23 23:19:43 [Debug] Clients already populated
2025-07-23 23:19:43 [Debug] IdentityResources already populated
2025-07-23 23:19:43 [Debug] ApiScopes already populated
2025-07-23 23:19:43 [Debug] OIDC IdentityProviders already populated
2025-07-23 23:19:43 [Information] Done seeding database. Exiting.
2025-07-23 23:19:43 [Information] [ServiceBus] Queue 'identity-server-queue' already exists.
2025-07-23 23:19:43 [Information] [ServiceBus] Queue 'failed-identity-server-queue' already exists.
2025-07-23 23:19:44 [Debug] Login Url: /Account/Login
2025-07-23 23:19:44 [Debug] Login Return Url Parameter: ReturnUrl
2025-07-23 23:19:44 [Debug] Logout Url: /Account/Logout
2025-07-23 23:19:44 [Debug] ConsentUrl Url: /consent
2025-07-23 23:19:44 [Debug] Consent Return Url Parameter: returnUrl
2025-07-23 23:19:44 [Debug] Error Url: /home/<USER>
2025-07-23 23:19:44 [Debug] Error Id Parameter: errorId
2025-07-23 23:19:44 [Information] HTTP GET / responded 302 in 65.5447 ms
2025-07-23 23:19:44 [Information] HTTP GET /Account/Login responded 200 in 154.8963 ms
2025-07-23 23:19:44 [Information] HTTP GET /css/site.css responded 304 in 2.9783 ms
2025-07-23 23:19:44 [Information] HTTP GET /js/components/utils.js responded 200 in 9.0359 ms
2025-07-23 23:19:44 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 4.9901 ms
2025-07-23 23:19:44 [Information] HTTP GET /js/pages/login.js responded 200 in 1.0039 ms
2025-07-23 23:23:01 [Debug] Start authorize request protocol validation
2025-07-23 23:23:01 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 23:23:01 [Information] HTTP GET /Account/Login responded 200 in 189.6454 ms
2025-07-23 23:23:01 [Information] HTTP GET /js/components/utils.js responded 200 in 0.6240 ms
2025-07-23 23:23:01 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.1059 ms
2025-07-23 23:23:01 [Information] HTTP GET /js/pages/login.js responded 304 in 0.1001 ms
2025-07-23 23:23:03 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 1.1068 ms
2025-07-23 23:23:04 [Information] HTTP GET /css/site.css.map responded 304 in 0.1031 ms
2025-07-23 23:23:08 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.3672 ms
2025-07-23 23:23:13 [Debug] Start authorize request protocol validation
2025-07-23 23:23:13 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 23:23:13 [Information] HTTP GET /Account/Login responded 200 in 21.1334 ms
2025-07-23 23:23:13 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6417 ms
2025-07-23 23:23:13 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.3970 ms
2025-07-23 23:23:13 [Information] HTTP GET /css/site.css responded 200 in 0.3703 ms
2025-07-23 23:23:13 [Information] HTTP GET /images/help.svg responded 200 in 0.3954 ms
2025-07-23 23:23:13 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.4779 ms
2025-07-23 23:23:13 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 5.0754 ms
2025-07-23 23:23:13 [Information] HTTP GET /images/logo.svg responded 200 in 0.4981 ms
2025-07-23 23:23:13 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.3669 ms
2025-07-23 23:23:13 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2421 ms
2025-07-23 23:23:13 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.8733 ms
2025-07-23 23:23:13 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.7848 ms
2025-07-23 23:23:13 [Information] HTTP GET /js/components/utils.js responded 200 in 0.3115 ms
2025-07-23 23:23:13 [Information] HTTP GET /js/index.js responded 200 in 0.3953 ms
2025-07-23 23:23:13 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.3273 ms
2025-07-23 23:23:13 [Information] HTTP GET /js/components/input.js responded 200 in 0.2088 ms
2025-07-23 23:23:13 [Information] HTTP GET /css/site.css.map responded 200 in 0.4683 ms
2025-07-23 23:23:13 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.2277 ms
2025-07-23 23:23:13 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.3522 ms
2025-07-23 23:23:13 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 2.7261 ms
2025-07-23 23:23:13 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2723 ms
2025-07-23 23:23:13 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.2757 ms
2025-07-23 23:23:13 [Information] HTTP GET /js/pages/login.js responded 200 in 0.3955 ms
2025-07-23 23:23:13 [Information] HTTP GET /images/login-background.png responded 200 in 1.5474 ms
2025-07-23 23:23:13 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 2.0039 ms
2025-07-23 23:23:14 [Information] HTTP GET /images/favicon.ico responded 200 in 0.4047 ms
2025-07-23 23:23:21 [Information] HTTP POST /Account/Login responded 200 in 247.3706 ms
2025-07-23 23:23:22 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:23:22 [Debug] Start authorize request protocol validation
2025-07-23 23:23:22 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 23:23:22 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 23:23:22 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:23:22 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-23 23:23:22 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXlBREFBTVFBekFEVUFOQUFBQUFBQTExOzExMTI0MzQ1MDAxMDsn\"","No":"CT201354","Type":"Company","Organizational_Level_Code":"","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Aimovig  - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"ADDENBROOKES HOSPITAL","Address_2":"HILLS ROAD","City":"","County":"","Post_Code":"CB2 0QQ","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL AIMOVIG  - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2022-01-11","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-08-14","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:23:22 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 23:23:22 [Debug] Configured "Basic" authentication
2025-07-23 23:23:22 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 23:23:23 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.6391253"ms
2025-07-23 23:23:23 [Information] {"Username":"CT411980","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNEA4P1H3R0U:00000045","TimeStamp":"2025-07-23T20:23:23.1800846","ProcessId":28040,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-23 23:23:23 [Debug] Start authorize request protocol validation
2025-07-23 23:23:23 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 23:23:23 [Information] HTTP POST /Account/Login responded 200 in 1131.1825 ms
2025-07-23 23:23:23 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5853 ms
2025-07-23 23:23:23 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.3321 ms
2025-07-23 23:23:55 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:23:55 [Debug] Start authorize request protocol validation
2025-07-23 23:23:55 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 23:23:55 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 23:23:55 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:23:55 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-23 23:23:56 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXlBREFBTVFBekFEVUFOQUFBQUFBQTExOzExMTI0MzQ1MDAxMDsn\"","No":"CT201354","Type":"Company","Organizational_Level_Code":"","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Aimovig  - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"ADDENBROOKES HOSPITAL","Address_2":"HILLS ROAD","City":"","County":"","Post_Code":"CB2 0QQ","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL AIMOVIG  - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2022-01-11","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-08-14","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:23:56 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 23:23:56 [Debug] Configured "Basic" authentication
2025-07-23 23:23:56 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 23:23:56 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.3444816"ms
2025-07-23 23:23:56 [Information] {"Username":"CT411980","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNEA4P1H3R0U:0000004B","TimeStamp":"2025-07-23T20:23:56.3823425","ProcessId":28040,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-23 23:23:56 [Debug] Start authorize request protocol validation
2025-07-23 23:23:56 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 23:23:56 [Information] HTTP POST /Account/Login responded 200 in 528.7757 ms
2025-07-23 23:23:56 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6719 ms
2025-07-23 23:23:56 [Information] HTTP GET /lib/bootstrap4-glyphicons/css/bootstrap-glyphicons.min.css responded 200 in 0.4892 ms
2025-07-23 23:23:56 [Information] HTTP GET /css/site.css responded 200 in 0.5028 ms
2025-07-23 23:23:56 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css responded 200 in 1.5951 ms
2025-07-23 23:23:56 [Information] HTTP GET /images/help-blue.svg responded 200 in 0.2570 ms
2025-07-23 23:23:56 [Information] HTTP GET /images/help.svg responded 200 in 0.3087 ms
2025-07-23 23:23:56 [Information] HTTP GET /images/logo.svg responded 200 in 0.3396 ms
2025-07-23 23:23:56 [Information] HTTP GET /images/info-circle.svg responded 200 in 0.3107 ms
2025-07-23 23:23:56 [Information] HTTP GET /images/eye-off.svg responded 200 in 0.2879 ms
2025-07-23 23:23:56 [Information] HTTP GET /lib/jquery/dist/jquery.min.js responded 200 in 0.6273 ms
2025-07-23 23:23:56 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js responded 200 in 0.6026 ms
2025-07-23 23:23:56 [Information] HTTP GET /js/index.js responded 200 in 0.2216 ms
2025-07-23 23:23:56 [Information] HTTP GET /css/site.css.map responded 200 in 0.3936 ms
2025-07-23 23:23:56 [Information] HTTP GET /lib/bootstrap/dist/css/bootstrap.min.css.map responded 200 in 1.8606 ms
2025-07-23 23:23:56 [Information] HTTP GET /js/components/info-tooltip.js responded 200 in 0.2889 ms
2025-07-23 23:23:56 [Information] HTTP GET /js/components/utils.js responded 200 in 0.3574 ms
2025-07-23 23:23:56 [Information] HTTP GET /js/components/input.js responded 200 in 0.3446 ms
2025-07-23 23:23:56 [Information] HTTP GET /js/components/button-validation.js responded 200 in 0.1957 ms
2025-07-23 23:23:56 [Information] HTTP GET /js/validation/ct-number-validation.js responded 200 in 0.3776 ms
2025-07-23 23:23:56 [Information] HTTP GET /js/pages/password-validation.js responded 200 in 0.4339 ms
2025-07-23 23:23:56 [Information] HTTP GET /js/validation/email-validation.js responded 200 in 0.2744 ms
2025-07-23 23:23:56 [Information] HTTP GET /js/pages/login.js responded 200 in 0.2497 ms
2025-07-23 23:23:56 [Information] HTTP GET /images/error-warning.svg responded 200 in 0.1860 ms
2025-07-23 23:23:56 [Information] HTTP GET /images/login-background.png responded 200 in 0.8239 ms
2025-07-23 23:23:56 [Information] HTTP GET /lib/bootstrap/dist/js/bootstrap.bundle.min.js.map responded 200 in 1.1884 ms
2025-07-23 23:23:56 [Information] HTTP GET /images/favicon.ico responded 200 in 0.3687 ms
2025-07-23 23:23:59 [Information] HTTP POST /Account/Login responded 200 in 109.3109 ms
2025-07-23 23:23:59 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Login from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:23:59 [Debug] Start authorize request protocol validation
2025-07-23 23:23:59 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 23:23:59 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 23:23:59 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:23:59 [Information] Contact?$filter=(No eq 'CT201354')
2025-07-23 23:23:59 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"JzM2O3VoTUFBQUo3LzBNQVZBQXlBREFBTVFBekFEVUFOQUFBQUFBQTExOzExMTI0MzQ1MDAxMDsn\"","No":"CT201354","Type":"Company","Organizational_Level_Code":"","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"","Name":"Addenbrookes Hospital Aimovig  - CP","First_Name":"","Middle_Name":"","Surname":"","Address":"ADDENBROOKES HOSPITAL","Address_2":"HILLS ROAD","City":"","County":"","Post_Code":"CB2 0QQ","Country_Region_Code":"GB","Search_Name":"ADDENBROOKES HOSPITAL AIMOVIG  - CP","Gender":" ","DOB":"0001-01-01","GMC_No":"","NHS_No":"","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"","Bill_to_Customer_No":"","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"","Registration_Date":"2022-01-11","Date_Received_Documents":"0001-01-01","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2024-08-14","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":"","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":" ","Enhanced_Services":false,"Mobile_Phone_No":"","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:23:59 [Information] Executing WebUtilitiesProxy.LoginCheckAsync
2025-07-23 23:23:59 [Debug] Configured "Basic" authentication
2025-07-23 23:23:59 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 23:24:00 [Information] Successfully executed WebUtilitiesProxy.LoginCheckAsync in "00:00:00.3318570"ms
2025-07-23 23:24:00 [Information] {"Username":"CT411980","Endpoint":"UI","ClientId":null,"Category":"Authentication","Name":"User Login Failure","EventType":"Failure","Id":1001,"Message":"invalid credentials","ActivityId":"0HNEA4P1H3R0U:00000083","TimeStamp":"2025-07-23T20:24:00.1575981","ProcessId":28040,"LocalIpAddress":"::1:5001","RemoteIpAddress":"::1","$type":"UserLoginFailureEvent"}
2025-07-23 23:24:00 [Debug] Start authorize request protocol validation
2025-07-23 23:24:00 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 23:24:00 [Information] HTTP POST /Account/Login responded 200 in 540.8697 ms
2025-07-23 23:24:00 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.8117 ms
2025-07-23 23:24:00 [Information] HTTP GET /js/components/utils.js responded 304 in 0.2079 ms
2025-07-23 23:24:11 [Information] HTTP GET /Account/ForgotPassword responded 200 in 19.1188 ms
2025-07-23 23:24:11 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6026 ms
2025-07-23 23:24:11 [Information] HTTP GET /images/password-logo.png responded 200 in 0.2265 ms
2025-07-23 23:24:11 [Information] HTTP GET /images/arrow-left.svg responded 200 in 0.1880 ms
2025-07-23 23:24:11 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0695 ms
2025-07-23 23:24:11 [Information] HTTP GET /js/pages/forgot-password.js responded 200 in 0.2325 ms
2025-07-23 23:24:16 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:24:16 [Information] HTTP POST /Account/ForgotPassword responded 200 in 12.2089 ms
2025-07-23 23:24:16 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5768 ms
2025-07-23 23:24:16 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0602 ms
2025-07-23 23:24:22 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotPassword from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:24:22 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 23:24:22 [Debug] Configured "Basic" authentication
2025-07-23 23:24:22 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 23:24:22 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3625733"ms
2025-07-23 23:24:22 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 23:24:22 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:24:24 [Information] HTTP POST /Account/ForgotPassword responded 302 in 1513.7531 ms
2025-07-23 23:24:24 [Information] HTTP GET /Account/ResetLinkSentSuccess responded 200 in 7.9176 ms
2025-07-23 23:24:24 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.6576 ms
2025-07-23 23:24:24 [Information] HTTP GET /images/email-logo.png responded 200 in 0.3916 ms
2025-07-23 23:24:24 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0722 ms
2025-07-23 23:24:25 [Debug] Start authorize request protocol validation
2025-07-23 23:24:25 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 23:24:25 [Information] HTTP GET /Account/Login responded 200 in 17.1193 ms
2025-07-23 23:24:25 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.8488 ms
2025-07-23 23:24:26 [Information] HTTP GET /Account/ForgotCTNumber responded 200 in 9.6074 ms
2025-07-23 23:24:26 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.9647 ms
2025-07-23 23:24:26 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0604 ms
2025-07-23 23:24:26 [Information] HTTP GET /js/pages/forgot-ct-number.js responded 200 in 0.2897 ms
2025-07-23 23:24:28 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/ForgotCTNumber from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:24:28 [Information] Executing WebUtilitiesProxy.GetContactNoFromEmailAsync
2025-07-23 23:24:28 [Debug] Configured "Basic" authentication
2025-07-23 23:24:28 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 23:24:28 [Information] Successfully executed WebUtilitiesProxy.GetContactNoFromEmailAsync in "00:00:00.3323613"ms
2025-07-23 23:24:28 [Information] Contact?$filter=(((No eq 'CT411980')))
2025-07-23 23:24:28 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:24:29 [Information] HTTP POST /Account/ForgotCTNumber responded 302 in 1261.3171 ms
2025-07-23 23:24:29 [Information] HTTP GET /Account/CTNumberSentSuccess responded 200 in 7.8996 ms
2025-07-23 23:24:29 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4758 ms
2025-07-23 23:24:29 [Information] HTTP GET /js/components/utils.js responded 304 in 0.1346 ms
2025-07-23 23:24:29 [Information] HTTP GET /js/validation/ct-number-validation.js responded 304 in 0.0699 ms
2025-07-23 23:24:30 [Debug] Start authorize request protocol validation
2025-07-23 23:24:30 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 23:24:30 [Information] HTTP GET /Account/Login responded 200 in 20.9551 ms
2025-07-23 23:24:30 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5411 ms
2025-07-23 23:24:32 [Information] HTTP GET /Account/Setup responded 200 in 26.8900 ms
2025-07-23 23:24:32 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5339 ms
2025-07-23 23:24:32 [Information] HTTP GET /images/step-active.png responded 200 in 0.3042 ms
2025-07-23 23:24:32 [Information] HTTP GET /images/step-next.png responded 200 in 0.1672 ms
2025-07-23 23:24:32 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0410 ms
2025-07-23 23:24:37 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:24:37 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 23:24:37 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:24:37 [Information] HTTP POST /Account/Setup responded 200 in 82.4080 ms
2025-07-23 23:24:37 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4464 ms
2025-07-23 23:24:37 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0660 ms
2025-07-23 23:24:37 [Information] HTTP GET /js/validation/email-validation.js responded 304 in 0.0327 ms
2025-07-23 23:24:42 [Debug] IdentityServer CorsPolicyService didn't handle CORS request made for path: /Account/Setup from origin: null because it is not for an IdentityServer CORS endpoint. To allow CORS requests to non IdentityServer endpoints, please set up your own Cors policy for your application by calling app.UseCors("MyPolicy") in the pipeline setup.
2025-07-23 23:24:42 [Information] Executing WebUtilitiesProxy.CheckContactNoAndEmailValidAsync
2025-07-23 23:24:42 [Debug] Configured "Basic" authentication
2025-07-23 23:24:42 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 23:24:42 [Information] Successfully executed WebUtilitiesProxy.CheckContactNoAndEmailValidAsync in "00:00:00.3446422"ms
2025-07-23 23:24:43 [Information] Contact?$filter=(No eq 'CT411980')
2025-07-23 23:24:43 [Debug] Content: {"@odata.context":"https://devts.healthnethomecare.co.uk:7048/HNHAZUREDEVTS/ODataV4/$metadata#Company('Biodose%20Services%20Live')/Contact","value":[{"@odata.etag":"W/\"****************************************************************************\"","No":"CT411980","Type":"Person","Organizational_Level_Code":"PATIENT","Company_No":"CT201354","Company_Name":"Addenbrookes Hospital Aimovig  - CP","Anonymise":false,"IntegrationCustomerNo":"ADHAIM - CP","Name":"Andrii Pravnyk","First_Name":"Andrii","Middle_Name":"","Surname":"Pravnyk","Address":"2 Sandal Hall Mews","Address_2":"","City":"Wakefield","County":"West Yorkshire","Post_Code":"DE11 0WU","Country_Region_Code":"","Search_Name":"ANDRII PRAVNYK","Gender":" ","DOB":"1900-01-01","GMC_No":"","NHS_No":"**********","GP":"","GP_Name":"","Consultant":"","Consultant_Name":"","Prescriber":"","Prescriber_Name":"","Trust_Pharmacist":"","_Pharmacist_Name":0,"Group_Consortium":"CT028842","Group_Consortium_Name":"East of England Collaborative Procurement Hub","Wholesaler_Trust":"CT040374","Wholesaler_Trust_Name":"Cambridge University Hospitals NHS Foundation Trus","Hospital":"CT040375","Hospital_Name":"Addenbrooke's Hospital","Hospital_No":"222","Bill_to_Customer_No":"ADHAIM - CP","Therapy":"AIMOVIG","Therapy_Start_Date":"0001-01-01","Primary_Diagnosis":"","Status":"Active","Switch_Code":"","Regular":false,"Recharge_Funder":"","Phone_No":"**********","Phone_No_2":"","Phone_No_3":"","Salesperson_Code":"EAST OF ENGLAND - A","Salutation_Code":"DR.","Registration_Date":"2025-02-18","Date_Received_Documents":"2025-02-18","Deregistration_Date":"0001-01-01","Last_Date_Modified":"2025-07-23","Date_of_Last_Interaction":"0001-01-01","Last_Date_Attempted":"0001-01-01","Next_Task_Date":"0001-01-01","Remark":"","Switch_Status":" ","Transition_Patient":false,"Date_Filter":"N/A","CCG":"","Previous_Reference_No":"","Carer_Or_Spouse_Or_Parent_Name":";","Portal_Opt_In":"Yes","Send_Blood_Test_Reminder":false,"Nursing_Service":"Yes","Enhanced_Services":false,"Mobile_Phone_No":"***********","Fax_No":"","Telex_No":"","Pager":"","Telex_Answer_Back":"","E_Mail":"<EMAIL>","E_Mail_2":"","Job_Title":"","Home_Page":"","Language_Code":"ENG","Correspondence_Type":" ","Prescription_Handler":"","Prescription_Handler_Name":"","Visit_Allowed":false,"No_of_Mailing_Groups":0,"No_of_Business_Relations":1,"No_of_Industry_Groups":0,"No_of_Job_Responsibilities":0,"Exclude_from_Segment":false,"Voicemail":false,"TextToPatient":false,"EmailToPatient":false,"Call_Made_Date":"0001-01-01","Follow_up_call_comments":"","Call_Successful_Date":"0001-01-01"}]}
2025-07-23 23:24:43 [Information] Executing WebUtilitiesProxy.UpdatePortalOptInAsync
2025-07-23 23:24:43 [Debug] Configured "Basic" authentication
2025-07-23 23:24:43 [Debug] Created WCF proxy WebUtils_PortClient for endpoint https://**************:7047/HNHAZUREDEVTS/WS/Biodose%20Services%20Live/Codeunit/WebUtils
2025-07-23 23:24:44 [Information] Successfully executed WebUtilitiesProxy.UpdatePortalOptInAsync in "00:00:00.3366279"ms
2025-07-23 23:24:44 [Information] HTTP POST /Account/Setup responded 200 in 1697.8933 ms
2025-07-23 23:24:44 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.4475 ms
2025-07-23 23:24:44 [Information] HTTP GET /js/components/utils.js responded 304 in 0.0565 ms
2025-07-23 23:24:45 [Debug] Start authorize request protocol validation
2025-07-23 23:24:45 [Error] Unknown client or not enabled: web-client-app
{"ClientId":null,"ClientName":null,"RedirectUri":null,"AllowedRedirectUris":null,"SubjectId":"82fbbc38-bc49-4f86-9575-f7e14dfac56f","ResponseType":null,"ResponseMode":null,"GrantType":null,"RequestedScopes":"","State":null,"UiLocales":null,"Nonce":null,"AuthenticationContextReferenceClasses":null,"DisplayMode":null,"PromptMode":"","MaxAge":null,"LoginHint":null,"SessionId":null,"Raw":{"client_id":"web-client-app","redirect_uri":"http://localhost:3000/signin-callback","response_type":"code","scope":"openid profile","state":"692729e1068b438ab3195f8afe267373","code_challenge":"5N6ISyHe2s5_5kwog-Y0PCR3RCR6frP-NiKWa_pErrA","code_challenge_method":"S256"},"$type":"AuthorizeRequestValidationLog"}
2025-07-23 23:24:45 [Information] HTTP GET /Account/Login responded 200 in 17.7050 ms
2025-07-23 23:24:45 [Information] HTTP GET /.well-known/appspecific/com.chrome.devtools.json responded 404 in 0.5869 ms
2025-07-23 23:24:45 [Information] HTTP GET /js/pages/login.js responded 304 in 0.0630 ms
