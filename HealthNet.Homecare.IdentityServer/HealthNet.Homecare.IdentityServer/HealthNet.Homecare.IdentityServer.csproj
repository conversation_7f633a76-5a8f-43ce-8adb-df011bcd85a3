<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Duende.IdentityServer.AspNetIdentity" Version="7.2.1" />
    <PackageReference Include="Duende.IdentityServer.EntityFramework" version="7.2.2" />
    <PackageReference Include="HealthNet.Homecare.DigitalTools.IntegrationNAV.Proxies" Version="1.0.9-rc5" />

    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="8.0.11" />

    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.15" />

    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.15" />

    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />

    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.6" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />

    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HealthNet.Homecare.IdentityServer.Abstractions\HealthNet.Homecare.IdentityServer.Abstractions.csproj" />
    <ProjectReference Include="..\HealthNet.Homecare.IdentityServer.Domain\HealthNet.Homecare.IdentityServer.Domain.csproj" />
    <ProjectReference Include="..\HealthNet.Homecare.IdentityServer.Infrastructure\HealthNet.Homecare.IdentityServer.Infrastructure.csproj" />
  </ItemGroup>

</Project>
