using HealthNet.Homecare.IdentityServer.Bundles;
using HealthNet.Homecare.IdentityServer.CSP;
using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace HealthNet.Homecare.IdentityServer.Extensions
{
	public static class HtmlHelperExtensions
	{
		private static string GetNonce(this IHtmlHelper html)
		{
			var context = html.ViewContext.HttpContext;
			var nonceService = context.RequestServices.GetRequiredService<ICspNonceService>();
			return nonceService.Nonce;
		}

		public static IHtmlContent ScriptWithNonce(this IHtmlHelper html, string src)
		{
			var nonce = html.GetNonce();
			var tag = $"<script nonce=\"{nonce}\" src=\"{src}\"></script>";
			return new HtmlString(tag);
		}

		public static IHtmlContent InlineScriptWithNonce(this IHtmlHelper html, string scriptContent)
		{
			var nonce = html.GetNonce();
			var tag = $"<script nonce=\"{nonce}\">{scriptContent}</script>";
			return new HtmlString(tag);
		}

		public static IHtmlContent StylesheetWithNonce(this IHtmlHelper html, string href)
		{
			var nonce = html.GetNonce();
			var tag = $"<link rel=\"stylesheet\" href=\"{href}\" nonce=\"{nonce}\" />";
			return new HtmlString(tag);
		}

		public static IHtmlContent InlineStyleWithNonce(this IHtmlHelper html, string cssContent)
		{
			var nonce = html.GetNonce();
			var tag = $"<style nonce=\"{nonce}\">{cssContent}</style>";
			return new HtmlString(tag);
		}

		public static IHtmlContent RenderScriptBundleWithNonce(this IHtmlHelper html, string bundleName)
		{
			var nonce = html.GetNonce();
			var scripts = ScriptBundles.Scripts.GetValueOrDefault(bundleName) ?? System.Array.Empty<string>();
			var builder = new HtmlContentBuilder();

			foreach (var script in scripts)
			{
				var scriptUrl = script.Replace("~", "");
				builder.AppendHtml($"<script nonce=\"{nonce}\" src=\"{scriptUrl}\"></script>\n");
			}

			return builder;
		}

		public static IHtmlContent RenderStyleBundleWithNonce(this IHtmlHelper html, string bundleName)
		{
			var nonce = html.GetNonce();
			var styles = StyleBundles.Styles.GetValueOrDefault(bundleName) ?? System.Array.Empty<string>();
			var builder = new HtmlContentBuilder();

			foreach (var style in styles)
			{
				var styleUrl = style.Replace("~", "");
				builder.AppendHtml($"<link rel=\"stylesheet\" nonce=\"{nonce}\" href=\"{styleUrl}\" />\n");
			}

			return builder;
		}
	}
}
