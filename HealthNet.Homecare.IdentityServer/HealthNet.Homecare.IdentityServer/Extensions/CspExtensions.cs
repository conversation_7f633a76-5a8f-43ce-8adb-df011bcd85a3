using HealthNet.Homecare.IdentityServer.CSP;
using HealthNet.Homecare.IdentityServer.Middlewares;

namespace HealthNet.Homecare.IdentityServer.Extensions
{
	public static class CspExtensions
	{
		public static IServiceCollection AddCspNonce(this IServiceCollection services)
		{
			return services.AddScoped<ICspNonceService, CspNonceService>();
		}

		public static IApplicationBuilder UseCspHeaders(this IApplicationBuilder app)
		{
			return app.UseMiddleware<CspHeaderMiddleware>();
		}
	}
}
