trigger:
- develop
- test

pool:
  vmImage: 'ubuntu-latest'

variables:
  - ${{ if eq(variables['build.SourceBranchName'], 'develop') }}:
      - group: hhdt-dev-id
  - ${{ if eq(variables['build.SourceBranchName'], 'test') }}:
      - group: hhdt-test-id

stages:
- stage: Build
  displayName: Build and Publish Artifacts
  jobs:
  - job: Build
    displayName: Build .NET 8 Solution
    steps:
    - task: UseDotNet@2
      displayName: 'Install .NET 8 SDK'
      inputs:
        packageType: 'sdk'
        version: '$(dotnetVersion)'
        includePreviewVersions: false
    - task: DotNetCoreCLI@2
      displayName: 'Restore Solution'
      inputs:
        command: 'restore'
        projects: '$(solution)'
        feedsToUse: 'config'
        nugetConfigPath: 'HealthNet.Homecare.IdentityServer/nuget.config'

    - task: DotNetCoreCLI@2
      displayName: 'Build'
      inputs:
        command: 'build'
        projects: '$(project)'
        arguments: '--no-restore --configuration $(buildConfiguration)'

    - task: DotNetCoreCLI@2
      displayName: 'Publish Artifacts'
      inputs:
        command: 'publish'
        projects: '$(project)'
        publishWebProjects: true
        arguments: '--no-build --configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)'
        zipAfterPublish: true

    - task: PublishPipelineArtifact@1
      displayName: 'Upload Build Artifacts'
      inputs:
        targetPath: '$(Build.ArtifactStagingDirectory)'
        artifactName: 'app'

- stage: Deploy
  displayName: Deploy to Azure Web App
  dependsOn: Build
  condition: succeeded()
  jobs:
  - deployment: DeployWebApp
    environment: 'development'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: DownloadPipelineArtifact@2
            displayName: 'Download Artifacts'
            inputs:
              artifactName: 'app'
              downloadPath: '$(Pipeline.Workspace)'

          - task: AzureWebApp@1
            displayName: 'Deploy to Azure Web App (Linux)'
            inputs:
              azureSubscription: '$(azureSubscription)'
              appType: 'webAppLinux'
              appName: '$(webAppName)'
              #resourceGroupName: '$(resourceGroup)'
              package: '$(Pipeline.Workspace)/app/**/*.zip'
