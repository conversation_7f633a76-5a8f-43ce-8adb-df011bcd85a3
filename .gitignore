################################################################################
# This .gitignore file was automatically created by Microsoft(R) Visual Studio.
################################################################################

## .NET Core / ASP.NET
bin/
obj/
out/
project.lock.json
project.fragment.lock.json
artifacts/

## User secrets
**/secrets.json

## Logs
*.log
logs/
**/logs/

## ASP.NET Scaffolding
ScaffoldingReadMe.txt

## ASP.NET Identity
**/App_Data/

## Static site assets
wwwroot/lib/
wwwroot/dist/
wwwroot/node_modules/

## Optional: Uncomment if wwwroot is built at runtime
# wwwroot/

## Visual Studio
.vs/
.vscode/
*.user
*.suo
*.userosscache
*.sln.docstates

## Rider
.idea/
*.sln.iml

## Build results
*.dll
*.exe
*.app
*.so
*.dylib
*.pdb

## Environment files
.env
.env.*

## OS Files
.DS_Store
Thumbs.db
/HealthNet.Homecare.IdentityServer/HealthNet.Homecare.IdentityServer.Client/IdentityServer.React.Client/package-lock.json
